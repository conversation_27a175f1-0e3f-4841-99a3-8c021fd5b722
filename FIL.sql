CREATE TABLE "users" (
  "id" uuid PRIMARY KEY,
  "email" varchar UNIQUE,
  "phone" varchar,
  "role" varchar,
  "invite_code" varchar,
  "referred_by" uuid,
  "created_at" timestamp DEFAULT (now())
);

CREATE TABLE "maker_profiles" (
  "user_id" uuid PRIMARY KEY,
  "domain" varchar,
  "logo_url" varchar,
  "support_email" varchar,
  "sms_signature" varchar,
  "enable_agent_order" boolean DEFAULT true,
  "enable_product_create" boolean DEFAULT true,
  "created_at" timestamp DEFAULT (now())
);

CREATE TABLE "agent_profiles" (
  "user_id" uuid PRIMARY KEY,
  "maker_id" uuid,
  "brand_name" varchar,
  "commission_pct" decimal,
  "kyc_status" varchar,
  "created_at" timestamp DEFAULT (now())
);

CREATE TABLE "customer_profiles" (
  "user_id" uuid PRIMARY KEY,
  "agent_id" uuid,
  "real_name" varchar,
  "id_number" varchar,
  "id_img_front" varchar,
  "id_img_back" varchar,
  "verify_status" varchar,
  "withdraw_pwd_hash" varchar,
  "created_at" timestamp DEFAULT (now())
);

CREATE TABLE "facilities" (
  "id" uuid PRIMARY KEY DEFAULT (gen_random_uuid()),
  "name" varchar,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp DEFAULT (now())
);

CREATE TABLE "miners" (
  "id" uuid PRIMARY KEY DEFAULT (gen_random_uuid()),
  "category" varchar,
  "facility_id" uuid,
  "filecoin_miner_id" varchar,
  "sector_size" bigint,
  "effective_until" date,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp DEFAULT (now())
);

CREATE TABLE "miner_daily_snapshots" (
  "miner_id" uuid,
  "snapshot_date" date,
  "blockchain_height" bigint,
  "power" numeric,
  "available_balance" numeric,
  "pledge_locked" numeric,
  "balance" numeric,
  PRIMARY KEY ("miner_id", "snapshot_date")
);

CREATE TABLE "miner_daily_earnings" (
  "miner_id" uuid,
  "earn_date" date,
  "cumulative_reward" numeric,
  "daily_reward" numeric,
  "blocks_won" int,
  "created_at" timestamp DEFAULT (now()),
  PRIMARY KEY ("miner_id", "earn_date")
);

CREATE TABLE "network_stats" (
  "stat_date" date PRIMARY KEY,
  "fil_per_tib" numeric
);

CREATE TABLE "currencies" (
  "code" varchar PRIMARY KEY,
  "total_supply" numeric,
  "withdrawable" numeric
);

CREATE TABLE "transactions" (
  "id" uuid PRIMARY KEY,
  "tx_date" timestamp DEFAULT (now()),
  "sender_user_id" uuid,
  "receiver_user_id" uuid,
  "amount_net" numeric,
  "tx_type" varchar,
  "filecoin_msg_id" varchar,
  "agent_id" uuid,
  "created_at" timestamp DEFAULT (now())
);

CREATE TABLE "manual_journals" (
  "id" uuid PRIMARY KEY,
  "maker_id" uuid,
  "customer_id" uuid,
  "currency_code" varchar,
  "amount" numeric,
  "journal_type" varchar,
  "remark" text,
  "created_at" timestamp DEFAULT (now())
);

CREATE TABLE "withdrawals" (
  "id" uuid PRIMARY KEY,
  "user_id" uuid,
  "currency_code" varchar,
  "request_amount" numeric,
  "final_amount" numeric,
  "fee" numeric,
  "user_remark" text,
  "admin_remark" text,
  "status" varchar,
  "requested_at" timestamp DEFAULT (now()),
  "reviewed_at" timestamp,
  "wallet_type" varchar,
  "address" varchar
);

CREATE TABLE "products" (
  "id" uuid PRIMARY KEY DEFAULT (gen_random_uuid()),
  "maker_id" uuid,
  "agent_id" uuid,
  "category" varchar,
  "name" varchar,
  "total_shares" numeric,
  "miner_id" uuid,
  "price" numeric,
  "effective_delay_days" int,
  "min_purchase" numeric,
  "sold_shares" numeric,
  "partner_reward_pct" numeric,
  "ops_commission_pct" numeric,
  "tech_commission_pct" numeric,
  "commission_agent_pct" numeric,
  "is_disabled" boolean DEFAULT false,
  "self_distribution" boolean,
  "auto_distribution" boolean,
  "review_status" varchar,
  "created_at" timestamp DEFAULT (now()),
  "delisted_at" timestamp
);

CREATE TABLE "capacity_requests" (
  "id" uuid PRIMARY KEY,
  "maker_id" uuid,
  "product_category" varchar,
  "added_capacity" numeric,
  "capacity_before" numeric,
  "capacity_after" numeric,
  "requested_by" uuid,
  "status" varchar,
  "description" text,
  "review_reply" text,
  "requested_at" timestamp DEFAULT (now()),
  "reviewed_at" timestamp
);

CREATE TABLE "orders" (
  "id" uuid PRIMARY KEY,
  "cid" varchar,
  "product_id" uuid,
  "agent_id" uuid,
  "customer_id" uuid,
  "shares" numeric,
  "proof_image_url" varchar,
  "storage_cost" numeric,
  "pledge_cost" numeric,
  "total_rate" numeric,
  "tech_fee_pct" numeric,
  "sales_fee_pct" numeric,
  "ops_fee_pct" numeric,
  "start_at" date,
  "end_at" date,
  "review_status" varchar,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  "deleted_at" timestamp
);

CREATE TABLE "distribution_batches" (
  "id" uuid PRIMARY KEY,
  "maker_id" uuid,
  "agent_id" uuid,
  "currency_code" varchar,
  "product_id" uuid,
  "shares" numeric,
  "batch_amount" numeric,
  "per_share_amount" numeric,
  "status" varchar,
  "created_at" timestamp DEFAULT (now()),
  "distributed_at" timestamp
);

CREATE TABLE "order_distributions" (
  "id" uuid PRIMARY KEY,
  "batch_id" uuid,
  "order_id" uuid,
  "customer_id" uuid,
  "share_amount" numeric,
  "reward_amount" numeric,
  "fee_amount" numeric,
  "progress" numeric,
  "created_at" timestamp DEFAULT (now())
);

CREATE TABLE "user_assets" (
  "user_id" uuid,
  "currency_code" varchar,
  "balance_available" numeric,
  "balance_locked" numeric,
  "balance_total" numeric,
  "withdrawn_total" numeric,
  PRIMARY KEY ("user_id", "currency_code")
);

CREATE TABLE "audit_logs" (
  "id" uuid PRIMARY KEY,
  "user_id" uuid,
  "action" varchar,
  "object_table" varchar,
  "object_id" uuid,
  "diff" jsonb,
  "created_at" timestamp DEFAULT (now())
);

CREATE TABLE "notifications" (
  "id" uuid PRIMARY KEY,
  "recipient_id" uuid,
  "type" varchar,
  "title" varchar,
  "body" text,
  "is_read" boolean DEFAULT false,
  "created_at" timestamp DEFAULT (now())
);


ALTER TABLE "maker_profiles" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "agent_profiles" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "agent_profiles" ADD FOREIGN KEY ("maker_id") REFERENCES "maker_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "customer_profiles" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "customer_profiles" ADD FOREIGN KEY ("agent_id") REFERENCES "agent_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "miners" ADD FOREIGN KEY ("facility_id") REFERENCES "facilities" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "miner_daily_snapshots" ADD FOREIGN KEY ("miner_id") REFERENCES "miners" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "miner_daily_earnings" ADD FOREIGN KEY ("miner_id") REFERENCES "miners" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "transactions" ADD FOREIGN KEY ("sender_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "transactions" ADD FOREIGN KEY ("receiver_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "transactions" ADD FOREIGN KEY ("agent_id") REFERENCES "agent_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "manual_journals" ADD FOREIGN KEY ("maker_id") REFERENCES "maker_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "manual_journals" ADD FOREIGN KEY ("customer_id") REFERENCES "customer_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "manual_journals" ADD FOREIGN KEY ("currency_code") REFERENCES "currencies" ("code") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "withdrawals" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "withdrawals" ADD FOREIGN KEY ("currency_code") REFERENCES "currencies" ("code") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "products" ADD FOREIGN KEY ("maker_id") REFERENCES "maker_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "products" ADD FOREIGN KEY ("miner_id") REFERENCES "miners" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "products" ADD FOREIGN KEY ("agent_id") REFERENCES "agent_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "capacity_requests" ADD FOREIGN KEY ("maker_id") REFERENCES "maker_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "capacity_requests" ADD FOREIGN KEY ("requested_by") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "orders" ADD FOREIGN KEY ("product_id") REFERENCES "products" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "orders" ADD FOREIGN KEY ("agent_id") REFERENCES "agent_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "orders" ADD FOREIGN KEY ("customer_id") REFERENCES "customer_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "distribution_batches" ADD FOREIGN KEY ("maker_id") REFERENCES "maker_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "distribution_batches" ADD FOREIGN KEY ("agent_id") REFERENCES "agent_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "distribution_batches" ADD FOREIGN KEY ("currency_code") REFERENCES "currencies" ("code") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "distribution_batches" ADD FOREIGN KEY ("product_id") REFERENCES "products" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "order_distributions" ADD FOREIGN KEY ("batch_id") REFERENCES "distribution_batches" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "order_distributions" ADD FOREIGN KEY ("order_id") REFERENCES "orders" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "order_distributions" ADD FOREIGN KEY ("customer_id") REFERENCES "customer_profiles" ("user_id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "user_assets" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "user_assets" ADD FOREIGN KEY ("currency_code") REFERENCES "currencies" ("code") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "audit_logs" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;

ALTER TABLE "notifications" ADD FOREIGN KEY ("recipient_id") REFERENCES "users" ("id") ON UPDATE CASCADE
ON DELETE RESTRICT;
