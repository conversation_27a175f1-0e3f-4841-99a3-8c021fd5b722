<?php
/**
 * Plugin Name: FIL Platform
 * Description: A custom plugin to display a Filecoin platform interface, built with React.
 * Version: 1.0
 * Author: Gemini
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Log plugin loading
error_log('FIL Platform: Main plugin file loading...');

// Plugin activation hook
function fil_platform_activate() {
    error_log('FIL Platform: Plugin activated successfully');
    // Clear any existing cron jobs
    wp_clear_scheduled_hook('fil_platform_filfox_scraper');
}
register_activation_hook(__FILE__, 'fil_platform_activate');

// Plugin deactivation hook
function fil_platform_deactivate() {
    error_log('FIL Platform: Plugin deactivated');
    // Clear cron jobs
    wp_clear_scheduled_hook('fil_platform_filfox_scraper');
}
register_deactivation_hook(__FILE__, 'fil_platform_deactivate');

// Function to enqueue scripts and styles for the React app
function fil_platform_enqueue_scripts() {
    $post = get_post();
    $has_shortcode = false;
    if ($post) {
        $has_shortcode = has_shortcode($post->post_content, 'fil_platform_app');
    }

    // Only load on the page with our shortcode
    if (is_page() && $has_shortcode) {
        $asset_path = plugin_dir_path(__FILE__) . 'frontend/build/asset-manifest.json';

        if (file_exists($asset_path)) {
            $asset_manifest_content = file_get_contents($asset_path);
            $asset_manifest = json_decode($asset_manifest_content, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                $main_js = isset($asset_manifest['files']['main.js']) ? $asset_manifest['files']['main.js'] : '';
                $main_css = isset($asset_manifest['files']['main.css']) ? $asset_manifest['files']['main.css'] : '';

                // 由于 manifest 中的路径现在是绝对路径（以 /wp-content 开头），
                // 我们需要使用 home_url() 来构建完整的 URL
                if ($main_js) {
                    $js_url = home_url($main_js);
                    wp_enqueue_script(
                        'fil-platform-react-app',
                        $js_url,
                        [],
                        '1.0',
                        true
                    );
                }

                if ($main_css) {
                    $css_url = home_url($main_css);
                    wp_enqueue_style(
                        'fil-platform-react-app-css',
                        $css_url,
                        [],
                        '1.0'
                    );
                }

                // Debug output
                error_log('FIL Platform Final Solution:');
                error_log('JS path from manifest: ' . $main_js);
                error_log('CSS path from manifest: ' . $main_css);
                if (isset($js_url)) error_log('Final JS URL: ' . $js_url);
                if (isset($css_url)) error_log('Final CSS URL: ' . $css_url);

                // Pass data to the React app, including the base path for React Router
                wp_localize_script('fil-platform-react-app', 'wpData', [
                    'apiUrl' => home_url('/wp-json/fil-platform/v1/'),
                    'nonce' => wp_create_nonce('wp_rest'),
                    'basePath' => parse_url(get_permalink($post->ID), PHP_URL_PATH),
                ]);
            } else {
                error_log('FIL Platform: Error parsing asset-manifest.json - ' . json_last_error_msg());
            }
        } else {
            error_log('FIL Platform: asset-manifest.json not found at ' . $asset_path);
        }
    }
}
add_action('wp_enqueue_scripts', 'fil_platform_enqueue_scripts');

// Shortcode to display the React app
function fil_platform_shortcode() {
    return '<div id="fil-platform-root"></div>';
}
add_shortcode('fil_platform_app', 'fil_platform_shortcode');

// Include the API routes
$api_routes_file = plugin_dir_path(__FILE__) . 'includes/api-routes.php';
if (file_exists($api_routes_file)) {
    require_once $api_routes_file;
} else {
    error_log('FIL Platform: api-routes.php not found at ' . $api_routes_file);
}

// Include the Filfox scraper
$scraper_file = plugin_dir_path(__FILE__) . 'includes/filfox-scraper.php';
if (file_exists($scraper_file)) {
    require_once $scraper_file;
} else {
    error_log('FIL Platform: filfox-scraper.php not found at ' . $scraper_file);
}

// Include admin page
if (is_admin()) {
    $admin_file = plugin_dir_path(__FILE__) . 'includes/admin-page.php';
    if (file_exists($admin_file)) {
        require_once $admin_file;
    } else {
        error_log('FIL Platform: admin-page.php not found at ' . $admin_file);
    }
}