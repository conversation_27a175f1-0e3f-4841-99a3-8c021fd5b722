"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[138],{1072:(e,r,s)=>{s.d(r,{A:()=>c});var n=s(8139),t=s.n(n),i=s(5043),a=s(7852),d=s(579);const l=i.forwardRef((e,r)=>{let{bsPrefix:s,className:n,as:i="div",...l}=e;const c=(0,a.oU)(s,"row"),o=(0,a.gy)(),u=(0,a.Jm)(),h=`${c}-cols`,x=[];return o.forEach(e=>{const r=l[e];let s;delete l[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const n=e!==u?`-${e}`:"";null!=s&&x.push(`${h}${n}-${s}`)}),(0,d.jsx)(i,{ref:r,...l,className:t()(n,c,...x)})});l.displayName="Row";const c=l},4063:(e,r,s)=>{s.d(r,{A:()=>c});var n=s(8139),t=s.n(n),i=s(5043),a=s(7852),d=s(579);const l=i.forwardRef((e,r)=>{let{bsPrefix:s,bg:n="primary",pill:i=!1,text:l,className:c,as:o="span",...u}=e;const h=(0,a.oU)(s,"badge");return(0,d.jsx)(o,{ref:r,...u,className:t()(c,h,i&&"rounded-pill",l&&`text-${l}`,n&&`bg-${n}`)})});l.displayName="Badge";const c=l},4196:(e,r,s)=>{s.d(r,{A:()=>c});var n=s(8139),t=s.n(n),i=s(5043),a=s(7852),d=s(579);const l=i.forwardRef((e,r)=>{let{bsPrefix:s,className:n,striped:i,bordered:l,borderless:c,hover:o,size:u,variant:h,responsive:x,...j}=e;const m=(0,a.oU)(s,"table"),v=t()(n,m,h&&`${m}-${h}`,u&&`${m}-${u}`,i&&`${m}-${"string"===typeof i?`striped-${i}`:"striped"}`,l&&`${m}-bordered`,c&&`${m}-borderless`,o&&`${m}-hover`),_=(0,d.jsx)("table",{...j,className:v,ref:r});if(x){let e=`${m}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,d.jsx)("div",{className:e,children:_})}return _});l.displayName="Table";const c=l},7994:(e,r,s)=>{s.d(r,{A:()=>x});var n=s(8139),t=s.n(n),i=s(5043),a=s(7852),d=s(1068),l=s(9334),c=s(579);const o=i.forwardRef((e,r)=>{let{className:s,bsPrefix:n,as:i="span",...d}=e;return n=(0,a.oU)(n,"input-group-text"),(0,c.jsx)(i,{ref:r,className:t()(s,n),...d})});o.displayName="InputGroupText";const u=o,h=i.forwardRef((e,r)=>{let{bsPrefix:s,size:n,hasValidation:d,className:o,as:u="div",...h}=e;s=(0,a.oU)(s,"input-group");const x=(0,i.useMemo)(()=>({}),[]);return(0,c.jsx)(l.A.Provider,{value:x,children:(0,c.jsx)(u,{ref:r,...h,className:t()(o,s,n&&`${s}-${n}`,d&&"has-validation")})})});h.displayName="InputGroup";const x=Object.assign(h,{Text:u,Radio:e=>(0,c.jsx)(u,{children:(0,c.jsx)(d.A,{type:"radio",...e})}),Checkbox:e=>(0,c.jsx)(u,{children:(0,c.jsx)(d.A,{type:"checkbox",...e})})})},9138:(e,r,s)=>{s.r(r),s.d(r,{default:()=>_});var n=s(5043),t=s(4063),i=s(3519),a=s(1072),d=s(8602),l=s(8628),c=s(4282),o=s(3722),u=s(7994),h=s(4196),x=s(3204),j=s(4312),m=s(4117),v=s(579);const _=()=>{const{t:e}=(0,m.Bd)(),[r,s]=(0,n.useState)([]),[_,p]=(0,n.useState)(!0),[g,f]=(0,n.useState)(""),[b,A]=(0,n.useState)(""),[w,y]=(0,n.useState)(""),[N,$]=(0,n.useState)(""),[C,k]=(0,n.useState)([]);(0,n.useEffect)(()=>{(async()=>{const e=(0,j.b)();if(!e)return;p(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void p(!1);const{data:n,error:t}=await e.from("customer_profiles").select("user_id, real_name, id_number, id_img_front, id_img_back, verify_status").eq("agent_id",r.id).order("created_at",{ascending:!1});if(t||!n)return console.error("Error fetching customer_profiles:",t),void p(!1);n.map(e=>e.user_id).filter(Boolean);const{data:i,error:a}=await e.from("users").select("id, email, created_at");a&&console.error("Error fetching users:",a);const d=new Map((i||[]).map(e=>[e.id,e])),l=n.map(e=>({...e,users:d.get(e.user_id)||{}}));s(l),p(!1)})()},[]),(0,n.useEffect)(()=>{let e=r;g&&(e=e.filter(e=>{var r,s,n;return(null===(r=e.users)||void 0===r||null===(s=r.email)||void 0===s?void 0:s.toLowerCase().includes(g.toLowerCase()))||(null===(n=e.real_name)||void 0===n?void 0:n.toLowerCase().includes(g.toLowerCase()))})),b&&(e=e.filter(e=>e.verify_status===b)),w&&(e=e.filter(e=>{var r;return new Date(null===(r=e.users)||void 0===r?void 0:r.created_at)>=new Date(w)})),N&&(e=e.filter(e=>{var r;return new Date(null===(r=e.users)||void 0===r?void 0:r.created_at)<=new Date(N)})),k(e)},[r,g,b,w,N]);const S=r=>{switch(r){case"approved":return(0,v.jsx)(t.A,{bg:"success",children:e("approved")});case"pending":return(0,v.jsx)(t.A,{bg:"warning",children:e("pending_review")});case"rejected":return(0,v.jsx)(t.A,{bg:"danger",children:e("rejected")});case"under_review":return(0,v.jsx)(t.A,{bg:"info",children:e("under_review")});default:return(0,v.jsx)(t.A,{bg:"secondary",children:r||e("not_submitted")})}};return _?(0,v.jsx)("div",{children:e("loading_members")}):(0,v.jsxs)(i.A,{children:[(0,v.jsx)("h2",{className:"mb-4",children:e("member_list")}),(0,v.jsx)(a.A,{className:"mb-4",children:(0,v.jsx)(d.A,{children:(0,v.jsx)(l.A,{children:(0,v.jsx)(l.A.Body,{children:(0,v.jsxs)(a.A,{className:"align-items-end",children:[(0,v.jsx)(d.A,{md:2,children:(0,v.jsxs)(c.A,{variant:"primary",onClick:()=>{alert(e("add_member_coming_soon"))},className:"mb-2",children:[(0,v.jsx)(x.OiG,{className:"me-1"}),e("add_member")]})}),(0,v.jsx)(d.A,{md:3,children:(0,v.jsxs)(o.A.Group,{children:[(0,v.jsx)(o.A.Label,{children:e("search_username")}),(0,v.jsx)(u.A,{children:(0,v.jsx)(o.A.Control,{type:"text",placeholder:e("please_enter_username"),value:g,onChange:e=>f(e.target.value)})})]})}),(0,v.jsx)(d.A,{md:2,children:(0,v.jsxs)(o.A.Group,{children:[(0,v.jsx)(o.A.Label,{children:e("status_filter")}),(0,v.jsxs)(o.A.Select,{value:b,onChange:e=>A(e.target.value),children:[(0,v.jsx)("option",{value:"",children:e("please_select_status")}),(0,v.jsx)("option",{value:"pending",children:e("pending_review")}),(0,v.jsx)("option",{value:"approved",children:e("approved")}),(0,v.jsx)("option",{value:"rejected",children:e("rejected")}),(0,v.jsx)("option",{value:"under_review",children:e("under_review")})]})]})}),(0,v.jsx)(d.A,{md:2,children:(0,v.jsxs)(o.A.Group,{children:[(0,v.jsx)(o.A.Label,{children:e("start_date")}),(0,v.jsx)(o.A.Control,{type:"date",value:w,onChange:e=>y(e.target.value)})]})}),(0,v.jsx)(d.A,{md:2,children:(0,v.jsxs)(o.A.Group,{children:[(0,v.jsx)(o.A.Label,{children:e("end_date")}),(0,v.jsx)(o.A.Control,{type:"date",value:N,onChange:e=>$(e.target.value)})]})}),(0,v.jsx)(d.A,{md:1,children:(0,v.jsx)(c.A,{variant:"outline-primary",onClick:()=>{console.log("Search triggered")},className:"mb-2",children:(0,v.jsx)(x.KSO,{})})})]})})})})}),(0,v.jsx)(a.A,{children:(0,v.jsx)(d.A,{children:(0,v.jsx)(l.A,{children:(0,v.jsx)(l.A.Body,{children:(0,v.jsxs)(h.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,v.jsx)("thead",{children:(0,v.jsxs)("tr",{children:[(0,v.jsx)("th",{children:e("username")}),(0,v.jsx)("th",{children:e("real_name")}),(0,v.jsx)("th",{children:e("id_number")}),(0,v.jsx)("th",{children:e("id_front_image")}),(0,v.jsx)("th",{children:e("id_back_image")}),(0,v.jsx)("th",{children:e("agent_name")}),(0,v.jsx)("th",{children:e("status")}),(0,v.jsx)("th",{children:e("registration_time")}),(0,v.jsx)("th",{children:e("actions")})]})}),(0,v.jsx)("tbody",{children:0===C.length?(0,v.jsx)("tr",{children:(0,v.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_members_found")})}):C.map(r=>{var s,n,i,a;return(0,v.jsxs)("tr",{children:[(0,v.jsx)("td",{children:(null===(s=r.users)||void 0===s?void 0:s.email)||"-"}),(0,v.jsx)("td",{children:r.real_name||"-"}),(0,v.jsx)("td",{children:r.id_number||"-"}),(0,v.jsx)("td",{children:r.id_img_front?(0,v.jsx)(t.A,{bg:"success",children:e("uploaded")}):(0,v.jsx)(t.A,{bg:"secondary",children:e("not_uploaded")})}),(0,v.jsx)("td",{children:r.id_img_back?(0,v.jsx)(t.A,{bg:"success",children:e("uploaded")}):(0,v.jsx)(t.A,{bg:"secondary",children:e("not_uploaded")})}),(0,v.jsx)("td",{children:(0,v.jsxs)("div",{children:[(0,v.jsx)("div",{children:(null===(n=r.agent_info)||void 0===n?void 0:n.brand_name)||"-"}),(0,v.jsx)("small",{className:"text-muted",children:(null===(i=r.agent_info)||void 0===i?void 0:i.email)||"-"})]})}),(0,v.jsx)("td",{children:S(r.verify_status)}),(0,v.jsx)("td",{children:null!==(a=r.users)&&void 0!==a&&a.created_at?new Date(r.users.created_at).toLocaleString():"-"}),(0,v.jsx)("td",{children:(0,v.jsxs)("div",{className:"d-flex gap-1",children:[(0,v.jsx)(c.A,{size:"sm",variant:"outline-primary",onClick:()=>(r.user_id,void alert(e("kyc_review_coming_soon"))),title:e("kyc_review"),children:(0,v.jsx)(x.BAG,{})}),(0,v.jsx)(c.A,{size:"sm",variant:"outline-warning",onClick:()=>(r.user_id,void alert(e("change_agent_coming_soon"))),title:e("change_agent"),children:(0,v.jsx)(x.yk7,{})}),(0,v.jsx)(c.A,{size:"sm",variant:"outline-info",onClick:()=>(r.user_id,void alert(e("view_details_coming_soon"))),title:e("view_details"),children:(0,v.jsx)(x.Ny1,{})})]})})]},r.user_id)})})]})})})})})]})}}}]);
//# sourceMappingURL=138.75d15d7f.chunk.js.map