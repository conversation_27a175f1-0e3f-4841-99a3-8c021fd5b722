"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[182],{1182:(e,i,t)=>{t.r(i),t.d(i,{default:()=>N});var s=t(5043),r=t(4117),n=t(8628),a=t(7417),l=t(3519),d=t(1072),c=t(8602),o=t(1719),h=t(4282),x=t(4196),j=t(108),m=t(5748),_=t(7734),u=t(2185),v=t(760),g=t(5525),f=t(95),A=t(2310),b=t(2291),w=t(5715),k=t(4312),p=t(579);const y=e=>{let{title:i,value:t,unit:s,variant:r,loading:l,icon:d}=e;return(0,p.jsx)(n.A,{className:`bg-${r} text-white mb-3 h-100`,children:(0,p.jsx)(n.A.Body,{className:"d-flex flex-column justify-content-between",children:(0,p.jsxs)("div",{className:"d-flex justify-content-between align-items-start",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)(n.A.Title,{className:"h6",children:i}),l?(0,p.jsxs)("div",{className:"d-flex align-items-center",children:[(0,p.jsx)(a.A,{animation:"border",size:"sm",className:"me-2"}),(0,p.jsx)("span",{children:"Loading..."})]}):(0,p.jsxs)("div",{children:[(0,p.jsx)("h4",{className:"mb-0",children:t}),s&&(0,p.jsx)("small",{className:"opacity-75",children:s})]})]}),d&&(0,p.jsx)("div",{className:"fs-2 opacity-50",children:d})]})})})},N=()=>{const{t:e}=(0,r.Bd)(),[i,t]=(0,s.useState)(!0),[N,F]=(0,s.useState)(null),[B,L]=(0,s.useState)(null),[I,E]=(0,s.useState)([]),[S,T]=(0,s.useState)(!1),D=async()=>{const e=(0,k.b)();if(!e)return F("Failed to initialize Supabase client"),void t(!1);try{t(!0);const{data:{user:i}}=await e.auth.getUser();if(!i)return F("User not logged in"),void t(!1);const{data:s,error:r}=await e.from("network_stats").select("*").order("stat_date",{ascending:!1}).limit(1);if(r)return console.error("Error fetching latest stats:",r),void F("Failed to fetch latest network statistics");s&&s.length>0&&L(s[0]);const{data:n,error:a}=await e.from("network_stats").select("*").order("stat_date",{ascending:!1}).limit(30);a?console.error("Error fetching historical stats:",a):E(n.reverse())}catch(N){console.error("Error fetching network stats:",N),F("Failed to load network statistics")}finally{t(!1),T(!1)}};(0,s.useEffect)(()=>{D()},[]);const K=e=>null===e||void 0===e?"N/A":new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:4}).format(e),z=e=>new Date(e).toLocaleDateString();return N?(0,p.jsx)(l.A,{fluid:!0,children:(0,p.jsx)(d.A,{className:"mb-3",children:(0,p.jsxs)(c.A,{children:[(0,p.jsx)("h2",{children:e("filfox_network_stats")}),(0,p.jsx)(o.A,{variant:"danger",children:N})]})})}):(0,p.jsxs)(l.A,{fluid:!0,children:[(0,p.jsx)(d.A,{className:"mb-3",children:(0,p.jsx)(c.A,{children:(0,p.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,p.jsx)("h2",{children:e("filfox_network_stats")}),(0,p.jsx)(h.A,{variant:"outline-primary",onClick:()=>{T(!0),D()},disabled:S,children:S?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(a.A,{animation:"border",size:"sm",className:"me-2"}),e("refreshing")]}):e("refresh")})]})})}),(0,p.jsxs)(d.A,{className:"mb-4",children:[(0,p.jsx)(c.A,{md:3,children:(0,p.jsx)(y,{title:e("block_height"),value:K(null===B||void 0===B?void 0:B.block_height),variant:"primary",loading:i,icon:"\ud83d\udd17"})}),(0,p.jsx)(c.A,{md:3,children:(0,p.jsx)(y,{title:e("network_storage_power"),value:K(null===B||void 0===B?void 0:B.network_storage_power),unit:"EiB",variant:"success",loading:i,icon:"\ud83d\udcbe"})}),(0,p.jsx)(c.A,{md:3,children:(0,p.jsx)(y,{title:e("active_miners"),value:K(null===B||void 0===B?void 0:B.active_miners),variant:"info",loading:i,icon:"\u26cf\ufe0f"})}),(0,p.jsx)(c.A,{md:3,children:(0,p.jsx)(y,{title:e("block_reward"),value:K(null===B||void 0===B?void 0:B.block_reward),unit:"FIL",variant:"warning",loading:i,icon:"\ud83c\udf81"})})]}),(0,p.jsxs)(d.A,{className:"mb-4",children:[(0,p.jsx)(c.A,{md:3,children:(0,p.jsx)(y,{title:e("mining_reward_24h"),value:K(null===B||void 0===B?void 0:B.fil_per_tib),unit:"FIL/TiB",variant:"secondary",loading:i,icon:"\u26a1"})}),(0,p.jsx)(c.A,{md:3,children:(0,p.jsx)(y,{title:e("fil_production_24h"),value:K(null===B||void 0===B?void 0:B.fil_production_24h),unit:"FIL",variant:"dark",loading:i,icon:"\ud83c\udfed"})}),(0,p.jsx)(c.A,{md:3,children:(0,p.jsx)(y,{title:e("total_pledge_collateral"),value:K(null===B||void 0===B?void 0:B.total_pledge_collateral),unit:"FIL",variant:"danger",loading:i,icon:"\ud83d\udd12"})}),(0,p.jsx)(c.A,{md:3,children:(0,p.jsx)(y,{title:e("messages_24h"),value:K(null===B||void 0===B?void 0:B.messages_24h),variant:"light",loading:i,icon:"\ud83d\udce8"})})]}),(0,p.jsxs)(d.A,{className:"mb-4",children:[(0,p.jsx)(c.A,{md:6,children:(0,p.jsx)(y,{title:e("sector_initial_pledge"),value:K(null===B||void 0===B?void 0:B.sector_initial_pledge),unit:"FIL/32GiB",variant:"primary",loading:i,icon:"\ud83d\udd10"})}),(0,p.jsx)(c.A,{md:6,children:(0,p.jsx)(y,{title:e("latest_block"),value:(null===B||void 0===B?void 0:B.latest_block)||"N/A",variant:"info",loading:i,icon:"\u23f0"})})]}),I.length>0&&(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(d.A,{className:"mb-4",children:(0,p.jsx)(c.A,{children:(0,p.jsx)(n.A,{children:(0,p.jsxs)(n.A.Body,{children:[(0,p.jsx)(n.A.Title,{children:e("mining_reward_trend")}),(0,p.jsx)(j.u,{width:"100%",height:300,children:(0,p.jsxs)(m.b,{data:I,children:[(0,p.jsx)(_.d,{strokeDasharray:"3 3"}),(0,p.jsx)(u.W,{dataKey:"stat_date",tickFormatter:z}),(0,p.jsx)(v.h,{}),(0,p.jsx)(g.m,{labelFormatter:z,formatter:e=>[K(e),"FIL/TiB"]}),(0,p.jsx)(f.s,{}),(0,p.jsx)(A.N,{type:"monotone",dataKey:"fil_per_tib",stroke:"#8884d8",name:e("mining_reward")})]})})]})})})}),(0,p.jsxs)(d.A,{className:"mb-4",children:[(0,p.jsx)(c.A,{md:6,children:(0,p.jsx)(n.A,{children:(0,p.jsxs)(n.A.Body,{children:[(0,p.jsx)(n.A.Title,{children:e("network_storage_trend")}),(0,p.jsx)(j.u,{width:"100%",height:300,children:(0,p.jsxs)(m.b,{data:I,children:[(0,p.jsx)(_.d,{strokeDasharray:"3 3"}),(0,p.jsx)(u.W,{dataKey:"stat_date",tickFormatter:z}),(0,p.jsx)(v.h,{}),(0,p.jsx)(g.m,{labelFormatter:z,formatter:e=>[K(e),"EiB"]}),(0,p.jsx)(f.s,{}),(0,p.jsx)(A.N,{type:"monotone",dataKey:"network_storage_power",stroke:"#82ca9d",name:e("storage_power")})]})})]})})}),(0,p.jsx)(c.A,{md:6,children:(0,p.jsx)(n.A,{children:(0,p.jsxs)(n.A.Body,{children:[(0,p.jsx)(n.A.Title,{children:e("active_miners_trend")}),(0,p.jsx)(j.u,{width:"100%",height:300,children:(0,p.jsxs)(b.E,{data:I,children:[(0,p.jsx)(_.d,{strokeDasharray:"3 3"}),(0,p.jsx)(u.W,{dataKey:"stat_date",tickFormatter:z}),(0,p.jsx)(v.h,{}),(0,p.jsx)(g.m,{labelFormatter:z,formatter:i=>[K(i),e("miners")]}),(0,p.jsx)(f.s,{}),(0,p.jsx)(w.y,{dataKey:"active_miners",fill:"#ffc658",name:e("active_miners")})]})})]})})})]})]}),(0,p.jsx)(d.A,{children:(0,p.jsx)(c.A,{children:(0,p.jsx)(n.A,{children:(0,p.jsxs)(n.A.Body,{children:[(0,p.jsx)(n.A.Title,{children:e("recent_network_data")}),i?(0,p.jsxs)("div",{className:"text-center",children:[(0,p.jsx)(a.A,{animation:"border"}),(0,p.jsx)("p",{className:"mt-2",children:e("loading")})]}):(0,p.jsxs)(x.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,p.jsx)("thead",{children:(0,p.jsxs)("tr",{children:[(0,p.jsx)("th",{children:e("date")}),(0,p.jsx)("th",{children:e("block_height")}),(0,p.jsx)("th",{children:e("storage_power")}),(0,p.jsx)("th",{children:e("active_miners")}),(0,p.jsx)("th",{children:e("mining_reward")}),(0,p.jsx)("th",{children:e("fil_production")})]})}),(0,p.jsx)("tbody",{children:I.slice(-10).reverse().map((e,i)=>(0,p.jsxs)("tr",{children:[(0,p.jsx)("td",{children:z(e.stat_date)}),(0,p.jsx)("td",{children:K(e.block_height)}),(0,p.jsxs)("td",{children:[K(e.network_storage_power)," EiB"]}),(0,p.jsx)("td",{children:K(e.active_miners)}),(0,p.jsxs)("td",{children:[K(e.fil_per_tib)," FIL/TiB"]}),(0,p.jsxs)("td",{children:[K(e.fil_production_24h)," FIL"]})]},`${e.stat_date}-${i}`))})]})]})})})})]})}}}]);
//# sourceMappingURL=182.9f2146d8.chunk.js.map