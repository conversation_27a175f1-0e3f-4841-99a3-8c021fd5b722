{"version": 3, "file": "static/js/182.9f2146d8.chunk.js", "mappings": "4VAMA,MAAMA,EAAWC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,KAAEC,EAAI,QAAEC,EAAO,QAAEC,EAAO,KAAEC,GAAMN,EAAA,OAC5DO,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAACC,UAAW,MAAML,0BAAgCM,UACnDH,EAAAA,EAAAA,KAACC,EAAAA,EAAKG,KAAI,CAACF,UAAU,6CAA4CC,UAC7DE,EAAAA,EAAAA,MAAA,OAAKH,UAAU,mDAAkDC,SAAA,EAC7DE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAACJ,UAAU,KAAIC,SAAET,IAC3BI,GACGO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,4BAA2BC,SAAA,EACtCH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,SAASC,KAAK,KAAKP,UAAU,UAChDF,EAAAA,EAAAA,KAAA,QAAAG,SAAM,mBAGVE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAIE,UAAU,OAAMC,SAAER,IACrBC,IAAQI,EAAAA,EAAAA,KAAA,SAAOE,UAAU,aAAYC,SAAEP,UAInDG,IAAQC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,kBAAiBC,SAAEJ,YAyX3D,EAnXeW,KACX,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPd,EAASe,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,OAC5BG,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAS,OAC1CK,EAAgBC,IAAqBN,EAAAA,EAAAA,UAAS,KAC9CO,EAAYC,IAAiBR,EAAAA,EAAAA,WAAS,GAGvCS,EAAoBC,UACtB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAGD,OAFAT,EAAS,6CACTH,GAAW,GAIf,IACIA,GAAW,GACX,MAAQc,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAGD,OAFAZ,EAAS,2BACTH,GAAW,GAKf,MAAQc,KAAMI,EAAYhB,MAAOiB,SAAsBP,EAClDQ,KAAK,iBACLC,OAAO,KACPC,MAAM,YAAa,CAAEC,WAAW,IAChCC,MAAM,GAEX,GAAIL,EAGA,OAFAM,QAAQvB,MAAM,+BAAgCiB,QAC9ChB,EAAS,6CAITe,GAAcA,EAAWQ,OAAS,GAClCrB,EAAgBa,EAAW,IAI/B,MAAQJ,KAAMR,EAAgBJ,MAAOyB,SAA0Bf,EAC1DQ,KAAK,iBACLC,OAAO,KACPC,MAAM,YAAa,CAAEC,WAAW,IAChCC,MAAM,IAEPG,EACAF,QAAQvB,MAAM,mCAAoCyB,GAGlDpB,EAAkBD,EAAesB,UAGzC,CAAE,MAAO1B,GACLuB,QAAQvB,MAAM,gCAAiCA,GAC/CC,EAAS,oCACb,CAAC,QACGH,GAAW,GACXS,GAAc,EAClB,IAGJoB,EAAAA,EAAAA,WAAU,KACNnB,KACD,IAEH,MAKMoB,EAAgBC,GACN,OAARA,QAAwBC,IAARD,EAA0B,MACvC,IAAIE,KAAKC,aAAa,QAAS,CAClCC,sBAAuB,EACvBC,sBAAuB,IACxBC,OAAON,GAGRO,EAAcC,GACT,IAAIC,KAAKD,GAAYE,qBAGhC,OAAIvC,GAEIf,EAAAA,EAAAA,KAACuD,EAAAA,EAAS,CAACC,OAAK,EAAArD,UACZH,EAAAA,EAAAA,KAACyD,EAAAA,EAAG,CAACvD,UAAU,OAAMC,UACjBE,EAAAA,EAAAA,MAACqD,EAAAA,EAAG,CAAAvD,SAAA,EACAH,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,2BACPX,EAAAA,EAAAA,KAAC2D,EAAAA,EAAK,CAAC9D,QAAQ,SAAQM,SAAEY,YAQzCV,EAAAA,EAAAA,MAACkD,EAAAA,EAAS,CAACC,OAAK,EAAArD,SAAA,EACZH,EAAAA,EAAAA,KAACyD,EAAAA,EAAG,CAACvD,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAAAvD,UACAE,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oDAAmDC,SAAA,EAC9DH,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,2BACPX,EAAAA,EAAAA,KAAC4D,EAAAA,EAAM,CACH/D,QAAQ,kBACRgE,QAtCFC,KAClBxC,GAAc,GACdC,KAqCoBwC,SAAU1C,EAAWlB,SAEpBkB,GACGhB,EAAAA,EAAAA,MAAA2D,EAAAA,SAAA,CAAA7D,SAAA,EACIH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,SAASC,KAAK,KAAKP,UAAU,SAC/CS,EAAE,iBAGPA,EAAE,qBAQtBN,EAAAA,EAAAA,MAACoD,EAAAA,EAAG,CAACvD,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAciD,cAClCrE,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,yBACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAckD,uBAClCvE,KAAK,MACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,iBACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAcmD,eAClCvE,QAAQ,OACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAcoD,cAClCzE,KAAK,MACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,uBAKjBM,EAAAA,EAAAA,MAACoD,EAAAA,EAAG,CAACvD,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,qBACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAcqD,aAClC1E,KAAK,UACLC,QAAQ,YACRC,QAASA,EACTC,KAAK,cAGbC,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,sBACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAcsD,oBAClC3E,KAAK,MACLC,QAAQ,OACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,2BACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAcuD,yBAClC5E,KAAK,MACLC,QAAQ,SACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAcwD,cAClC5E,QAAQ,QACRC,QAASA,EACTC,KAAK,uBAMjBM,EAAAA,EAAAA,MAACoD,EAAAA,EAAG,CAACvD,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,yBACThB,MAAOgD,EAAyB,OAAZ1B,QAAY,IAAZA,OAAY,EAAZA,EAAcyD,uBAClC9E,KAAK,YACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,OAAmB,OAAZsB,QAAY,IAAZA,OAAY,EAAZA,EAAc0D,eAAgB,MACrC9E,QAAQ,OACRC,QAASA,EACTC,KAAK,gBAMhBoB,EAAeoB,OAAS,IACrBlC,EAAAA,EAAAA,MAAA2D,EAAAA,SAAA,CAAA7D,SAAA,EACIH,EAAAA,EAAAA,KAACyD,EAAAA,EAAG,CAACvD,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAAAvD,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,0BACfX,EAAAA,EAAAA,KAAC4E,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAI3E,UAC1CE,EAAAA,EAAAA,MAAC0E,EAAAA,EAAS,CAACpD,KAAMR,EAAehB,SAAA,EAC5BH,EAAAA,EAAAA,KAACgF,EAAAA,EAAa,CAACC,gBAAgB,SAC/BjF,EAAAA,EAAAA,KAACkF,EAAAA,EAAK,CACFC,QAAQ,YACRC,cAAejC,KAEnBnD,EAAAA,EAAAA,KAACqF,EAAAA,EAAK,KACNrF,EAAAA,EAAAA,KAACsF,EAAAA,EAAO,CACJC,eAAgBpC,EAChBqC,UAAY7F,GAAU,CAACgD,EAAahD,GAAQ,cAEhDK,EAAAA,EAAAA,KAACyF,EAAAA,EAAM,KACPzF,EAAAA,EAAAA,KAAC0F,EAAAA,EAAI,CACDC,KAAK,WACLR,QAAQ,cACRS,OAAO,UACPC,KAAMlF,EAAE,kCASpCN,EAAAA,EAAAA,MAACoD,EAAAA,EAAG,CAACvD,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,4BACfX,EAAAA,EAAAA,KAAC4E,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAI3E,UAC1CE,EAAAA,EAAAA,MAAC0E,EAAAA,EAAS,CAACpD,KAAMR,EAAehB,SAAA,EAC5BH,EAAAA,EAAAA,KAACgF,EAAAA,EAAa,CAACC,gBAAgB,SAC/BjF,EAAAA,EAAAA,KAACkF,EAAAA,EAAK,CACFC,QAAQ,YACRC,cAAejC,KAEnBnD,EAAAA,EAAAA,KAACqF,EAAAA,EAAK,KACNrF,EAAAA,EAAAA,KAACsF,EAAAA,EAAO,CACJC,eAAgBpC,EAChBqC,UAAY7F,GAAU,CAACgD,EAAahD,GAAQ,UAEhDK,EAAAA,EAAAA,KAACyF,EAAAA,EAAM,KACPzF,EAAAA,EAAAA,KAAC0F,EAAAA,EAAI,CACDC,KAAK,WACLR,QAAQ,wBACRS,OAAO,UACPC,KAAMlF,EAAE,gCAOhCX,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAACO,GAAI,EAAE9D,UACPH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,0BACfX,EAAAA,EAAAA,KAAC4E,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAI3E,UAC1CE,EAAAA,EAAAA,MAACyF,EAAAA,EAAQ,CAACnE,KAAMR,EAAehB,SAAA,EAC3BH,EAAAA,EAAAA,KAACgF,EAAAA,EAAa,CAACC,gBAAgB,SAC/BjF,EAAAA,EAAAA,KAACkF,EAAAA,EAAK,CACFC,QAAQ,YACRC,cAAejC,KAEnBnD,EAAAA,EAAAA,KAACqF,EAAAA,EAAK,KACNrF,EAAAA,EAAAA,KAACsF,EAAAA,EAAO,CACJC,eAAgBpC,EAChBqC,UAAY7F,GAAU,CAACgD,EAAahD,GAAQgB,EAAE,cAElDX,EAAAA,EAAAA,KAACyF,EAAAA,EAAM,KACPzF,EAAAA,EAAAA,KAAC+F,EAAAA,EAAG,CACAZ,QAAQ,gBACRa,KAAK,UACLH,KAAMlF,EAAE,sCAY5CX,EAAAA,EAAAA,KAACyD,EAAAA,EAAG,CAAAtD,UACAH,EAAAA,EAAAA,KAAC0D,EAAAA,EAAG,CAAAvD,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,yBACdb,GACGO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,cAAaC,SAAA,EACxBH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,YACnBR,EAAAA,EAAAA,KAAA,KAAGE,UAAU,OAAMC,SAAEQ,EAAE,iBAG3BN,EAAAA,EAAAA,MAAC4F,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAlG,SAAA,EACpCH,EAAAA,EAAAA,KAAA,SAAAG,UACIE,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,WACPX,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,mBACPX,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,oBACPX,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,oBACPX,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,oBACPX,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,0BAGfX,EAAAA,EAAAA,KAAA,SAAAG,SACKgB,EAAemF,OAAO,IAAI7D,UAAU8D,IAAI,CAACC,EAAMC,KAC5CpG,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,SAAKgD,EAAWqD,EAAKE,cACrB1G,EAAAA,EAAAA,KAAA,MAAAG,SAAKwC,EAAa6D,EAAKtC,iBACvB7D,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKwC,EAAa6D,EAAKrC,uBAAuB,WAC9CnE,EAAAA,EAAAA,KAAA,MAAAG,SAAKwC,EAAa6D,EAAKpC,kBACvB/D,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKwC,EAAa6D,EAAKlC,aAAa,eACpCjE,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKwC,EAAa6D,EAAKjC,oBAAoB,YANtC,GAAGiC,EAAKE,aAAaD,uB", "sources": ["pages/customer/Filfox.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, Table, Spinner, Al<PERSON>, Button } from 'react-bootstrap';\nimport { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Bar } from 'recharts';\nimport { getSupabase } from '../../supabaseClient';\n\nconst StatCard = ({ title, value, unit, variant, loading, icon }) => (\n    <Card className={`bg-${variant} text-white mb-3 h-100`}>\n        <Card.Body className=\"d-flex flex-column justify-content-between\">\n            <div className=\"d-flex justify-content-between align-items-start\">\n                <div>\n                    <Card.Title className=\"h6\">{title}</Card.Title>\n                    {loading ? (\n                        <div className=\"d-flex align-items-center\">\n                            <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                            <span>Loading...</span>\n                        </div>\n                    ) : (\n                        <div>\n                            <h4 className=\"mb-0\">{value}</h4>\n                            {unit && <small className=\"opacity-75\">{unit}</small>}\n                        </div>\n                    )}\n                </div>\n                {icon && <div className=\"fs-2 opacity-50\">{icon}</div>}\n            </div>\n        </Card.Body>\n    </Card>\n);\n\nconst Filfox = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [currentStats, setCurrentStats] = useState(null);\n    const [historicalData, setHistoricalData] = useState([]);\n    const [refreshing, setRefreshing] = useState(false);\n\n    // Fetch current and historical network stats\n    const fetchNetworkStats = async () => {\n        const supabase = getSupabase();\n        if (!supabase) {\n            setError('Failed to initialize Supabase client');\n            setLoading(false);\n            return;\n        }\n\n        try {\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setError('User not logged in');\n                setLoading(false);\n                return;\n            }\n\n            // Fetch latest network stats\n            const { data: latestData, error: latestError } = await supabase\n                .from('network_stats')\n                .select('*')\n                .order('stat_date', { ascending: false })\n                .limit(1);\n\n            if (latestError) {\n                console.error('Error fetching latest stats:', latestError);\n                setError('Failed to fetch latest network statistics');\n                return;\n            }\n\n            if (latestData && latestData.length > 0) {\n                setCurrentStats(latestData[0]);\n            }\n\n            // Fetch historical data for charts (last 30 days)\n            const { data: historicalData, error: historicalError } = await supabase\n                .from('network_stats')\n                .select('*')\n                .order('stat_date', { ascending: false })\n                .limit(30);\n\n            if (historicalError) {\n                console.error('Error fetching historical stats:', historicalError);\n            } else {\n                // Reverse to show chronological order in charts\n                setHistoricalData(historicalData.reverse());\n            }\n\n        } catch (error) {\n            console.error('Error fetching network stats:', error);\n            setError('Failed to load network statistics');\n        } finally {\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n\n    useEffect(() => {\n        fetchNetworkStats();\n    }, []);\n\n    const handleRefresh = () => {\n        setRefreshing(true);\n        fetchNetworkStats();\n    };\n\n    const formatNumber = (num) => {\n        if (num === null || num === undefined) return 'N/A';\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 4\n        }).format(num);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString();\n    };\n\n    if (error) {\n        return (\n            <Container fluid>\n                <Row className=\"mb-3\">\n                    <Col>\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Alert variant=\"danger\">{error}</Alert>\n                    </Col>\n                </Row>\n            </Container>\n        );\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Button \n                            variant=\"outline-primary\" \n                            onClick={handleRefresh}\n                            disabled={refreshing}\n                        >\n                            {refreshing ? (\n                                <>\n                                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                                    {t('refreshing')}\n                                </>\n                            ) : (\n                                t('refresh')\n                            )}\n                        </Button>\n                    </div>\n                </Col>\n            </Row>\n\n            {/* Current Statistics Cards */}\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_height')}\n                        value={formatNumber(currentStats?.block_height)}\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔗\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('network_storage_power')}\n                        value={formatNumber(currentStats?.network_storage_power)}\n                        unit=\"EiB\"\n                        variant=\"success\"\n                        loading={loading}\n                        icon=\"💾\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('active_miners')}\n                        value={formatNumber(currentStats?.active_miners)}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⛏️\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_reward')}\n                        value={formatNumber(currentStats?.block_reward)}\n                        unit=\"FIL\"\n                        variant=\"warning\"\n                        loading={loading}\n                        icon=\"🎁\"\n                    />\n                </Col>\n            </Row>\n\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('mining_reward_24h')}\n                        value={formatNumber(currentStats?.fil_per_tib)}\n                        unit=\"FIL/TiB\"\n                        variant=\"secondary\"\n                        loading={loading}\n                        icon=\"⚡\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('fil_production_24h')}\n                        value={formatNumber(currentStats?.fil_production_24h)}\n                        unit=\"FIL\"\n                        variant=\"dark\"\n                        loading={loading}\n                        icon=\"🏭\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('total_pledge_collateral')}\n                        value={formatNumber(currentStats?.total_pledge_collateral)}\n                        unit=\"FIL\"\n                        variant=\"danger\"\n                        loading={loading}\n                        icon=\"🔒\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('messages_24h')}\n                        value={formatNumber(currentStats?.messages_24h)}\n                        variant=\"light\"\n                        loading={loading}\n                        icon=\"📨\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Additional Stats */}\n            <Row className=\"mb-4\">\n                <Col md={6}>\n                    <StatCard\n                        title={t('sector_initial_pledge')}\n                        value={formatNumber(currentStats?.sector_initial_pledge)}\n                        unit=\"FIL/32GiB\"\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔐\"\n                    />\n                </Col>\n                <Col md={6}>\n                    <StatCard\n                        title={t('latest_block')}\n                        value={currentStats?.latest_block || 'N/A'}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⏰\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Historical Charts */}\n            {historicalData.length > 0 && (\n                <>\n                    <Row className=\"mb-4\">\n                        <Col>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('mining_reward_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <LineChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), 'FIL/TiB']}\n                                            />\n                                            <Legend />\n                                            <Line \n                                                type=\"monotone\" \n                                                dataKey=\"fil_per_tib\" \n                                                stroke=\"#8884d8\" \n                                                name={t('mining_reward')}\n                                            />\n                                        </LineChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n\n                    <Row className=\"mb-4\">\n                        <Col md={6}>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('network_storage_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <LineChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), 'EiB']}\n                                            />\n                                            <Legend />\n                                            <Line \n                                                type=\"monotone\" \n                                                dataKey=\"network_storage_power\" \n                                                stroke=\"#82ca9d\" \n                                                name={t('storage_power')}\n                                            />\n                                        </LineChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                        <Col md={6}>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('active_miners_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <BarChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), t('miners')]}\n                                            />\n                                            <Legend />\n                                            <Bar \n                                                dataKey=\"active_miners\" \n                                                fill=\"#ffc658\" \n                                                name={t('active_miners')}\n                                            />\n                                        </BarChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n                </>\n            )}\n\n            {/* Data Table */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('recent_network_data')}</Card.Title>\n                            {loading ? (\n                                <div className=\"text-center\">\n                                    <Spinner animation=\"border\" />\n                                    <p className=\"mt-2\">{t('loading')}</p>\n                                </div>\n                            ) : (\n                                <Table striped bordered hover responsive>\n                                    <thead>\n                                        <tr>\n                                            <th>{t('date')}</th>\n                                            <th>{t('block_height')}</th>\n                                            <th>{t('storage_power')}</th>\n                                            <th>{t('active_miners')}</th>\n                                            <th>{t('mining_reward')}</th>\n                                            <th>{t('fil_production')}</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        {historicalData.slice(-10).reverse().map((stat, index) => (\n                                            <tr key={`${stat.stat_date}-${index}`}>\n                                                <td>{formatDate(stat.stat_date)}</td>\n                                                <td>{formatNumber(stat.block_height)}</td>\n                                                <td>{formatNumber(stat.network_storage_power)} EiB</td>\n                                                <td>{formatNumber(stat.active_miners)}</td>\n                                                <td>{formatNumber(stat.fil_per_tib)} FIL/TiB</td>\n                                                <td>{formatNumber(stat.fil_production_24h)} FIL</td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </Table>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Filfox;\n"], "names": ["StatCard", "_ref", "title", "value", "unit", "variant", "loading", "icon", "_jsx", "Card", "className", "children", "Body", "_jsxs", "Title", "Spinner", "animation", "size", "Filfox", "t", "useTranslation", "setLoading", "useState", "error", "setError", "currentStats", "setCurrentStats", "historicalData", "setHistoricalData", "refreshing", "setRefreshing", "fetchNetworkStats", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "latestData", "latestError", "from", "select", "order", "ascending", "limit", "console", "length", "historicalError", "reverse", "useEffect", "formatNumber", "num", "undefined", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "Container", "fluid", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "onClick", "handleRefresh", "disabled", "_Fragment", "md", "block_height", "network_storage_power", "active_miners", "block_reward", "fil_per_tib", "fil_production_24h", "total_pledge_collateral", "messages_24h", "sector_initial_pledge", "latest_block", "ResponsiveContainer", "width", "height", "Line<PERSON>hart", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XAxis", "dataKey", "tick<PERSON><PERSON><PERSON><PERSON>", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "labelFormatter", "formatter", "Legend", "Line", "type", "stroke", "name", "<PERSON><PERSON><PERSON>", "Bar", "fill", "Table", "striped", "bordered", "hover", "responsive", "slice", "map", "stat", "index", "stat_date"], "sourceRoot": ""}