"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[182],{1182:(e,s,i)=>{i.r(s),i.d(s,{default:()=>k});var t=i(5043),n=i(4117),l=i(8628),r=i(7417),a=i(3519),d=i(1072),o=i(8602),c=i(1719),h=i(4282),x=i(4196),j=i(108),m=i(2998),_=i(7734),g=i(2185),u=i(760),v=i(9923),f=i(713),p=i(2872),A=i(4312),w=i(579);const b=e=>{let{title:s,value:i,unit:t,variant:n,loading:a,icon:d}=e;return(0,w.jsx)(l.A,{className:`bg-${n} text-white mb-3 h-100`,children:(0,w.jsx)(l.A.Body,{className:"d-flex flex-column justify-content-between",children:(0,w.jsxs)("div",{className:"d-flex justify-content-between align-items-start",children:[(0,w.jsxs)("div",{children:[(0,w.jsx)(l.A.Title,{className:"h6",children:s}),a?(0,w.jsxs)("div",{className:"d-flex align-items-center",children:[(0,w.jsx)(r.A,{animation:"border",size:"sm",className:"me-2"}),(0,w.jsx)("span",{children:"Loading..."})]}):(0,w.jsxs)("div",{children:[(0,w.jsx)("h4",{className:"mb-0",children:i}),t&&(0,w.jsx)("small",{className:"opacity-75",children:t})]})]}),d&&(0,w.jsx)("div",{className:"fs-2 opacity-50",children:d})]})})})},k=()=>{const{t:e}=(0,n.Bd)(),[s,i]=(0,t.useState)(!0),[k,N]=(0,t.useState)(null),[y,F]=(0,t.useState)(null),[T,I]=(0,t.useState)([]),[L,B]=(0,t.useState)(!1),E=async()=>{try{i(!0);const e=window.location.origin+"/wp-json/fil-platform/v1/test";console.log("Testing API connection:",e);const s=await fetch(e,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(console.log("Test response status:",s.status),s.ok){const e=await s.json();console.log("Test API Response:",e)}else console.error("Test API failed:",s.status);const t=window.location.origin+"/wp-json/fil-platform/v1/filfox-realtime";console.log("Fetching from URL:",t);const n=await fetch(t,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(console.log("Response status:",n.status),!n.ok){const e=await n.text();throw console.error("Error response:",e),new Error(`HTTP error! status: ${n.status} - ${e}`)}const l=await n.json();console.log("API Response:",l),l.success?(F(l.data),N(null)):N(l.message||"Failed to fetch real-time data"),await S()}catch(k){console.error("Error fetching real-time stats:",k),N("Failed to load real-time network statistics: "+k.message)}finally{i(!1),B(!1)}},S=async()=>{const e=(0,A.b)();if(e)try{const{data:{user:s}}=await e.auth.getUser();if(!s)return;const{data:i,error:t}=await e.from("network_stats").select("stat_date, fil_per_tib").order("stat_date",{ascending:!1}).limit(30);t?console.error("Error fetching historical stats:",t):I(i.reverse())}catch(k){console.error("Error fetching historical data:",k)}};(0,t.useEffect)(()=>{E()},[]);const D=e=>null===e||void 0===e?"N/A":new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:4}).format(e),C=e=>new Date(e).toLocaleDateString();return k?(0,w.jsx)(a.A,{fluid:!0,children:(0,w.jsx)(d.A,{className:"mb-3",children:(0,w.jsxs)(o.A,{children:[(0,w.jsx)("h2",{children:e("filfox_network_stats")}),(0,w.jsx)(c.A,{variant:"danger",children:k})]})})}):(0,w.jsxs)(a.A,{fluid:!0,children:[(0,w.jsx)(d.A,{className:"mb-3",children:(0,w.jsx)(o.A,{children:(0,w.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,w.jsx)("h2",{children:e("filfox_network_stats")}),(0,w.jsx)(h.A,{variant:"outline-primary",onClick:()=>{B(!0),E()},disabled:L,children:L?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(r.A,{animation:"border",size:"sm",className:"me-2"}),e("refreshing")]}):e("refresh")})]})})}),(0,w.jsxs)(d.A,{className:"mb-4",children:[(0,w.jsx)(o.A,{md:3,children:(0,w.jsx)(b,{title:e("block_height"),value:D(null===y||void 0===y?void 0:y.block_height),variant:"primary",loading:s,icon:"\ud83d\udd17"})}),(0,w.jsx)(o.A,{md:3,children:(0,w.jsx)(b,{title:e("network_storage_power"),value:D(null===y||void 0===y?void 0:y.network_storage_power),unit:"EiB",variant:"success",loading:s,icon:"\ud83d\udcbe"})}),(0,w.jsx)(o.A,{md:3,children:(0,w.jsx)(b,{title:e("active_miners"),value:D(null===y||void 0===y?void 0:y.active_miners),variant:"info",loading:s,icon:"\u26cf\ufe0f"})}),(0,w.jsx)(o.A,{md:3,children:(0,w.jsx)(b,{title:e("block_reward"),value:D(null===y||void 0===y?void 0:y.block_reward),unit:"FIL",variant:"warning",loading:s,icon:"\ud83c\udf81"})})]}),(0,w.jsxs)(d.A,{className:"mb-4",children:[(0,w.jsx)(o.A,{md:3,children:(0,w.jsx)(b,{title:e("mining_reward_24h"),value:D(null===y||void 0===y?void 0:y.fil_per_tib),unit:"FIL/TiB",variant:"secondary",loading:s,icon:"\u26a1"})}),(0,w.jsx)(o.A,{md:3,children:(0,w.jsx)(b,{title:e("fil_production_24h"),value:D(null===y||void 0===y?void 0:y.fil_production_24h),unit:"FIL",variant:"dark",loading:s,icon:"\ud83c\udfed"})}),(0,w.jsx)(o.A,{md:3,children:(0,w.jsx)(b,{title:e("total_pledge_collateral"),value:D(null===y||void 0===y?void 0:y.total_pledge_collateral),unit:"FIL",variant:"danger",loading:s,icon:"\ud83d\udd12"})}),(0,w.jsx)(o.A,{md:3,children:(0,w.jsx)(b,{title:e("messages_24h"),value:D(null===y||void 0===y?void 0:y.messages_24h),variant:"light",loading:s,icon:"\ud83d\udce8"})})]}),(0,w.jsxs)(d.A,{className:"mb-4",children:[(0,w.jsx)(o.A,{md:6,children:(0,w.jsx)(b,{title:e("sector_initial_pledge"),value:D(null===y||void 0===y?void 0:y.sector_initial_pledge),unit:"FIL/32GiB",variant:"primary",loading:s,icon:"\ud83d\udd10"})}),(0,w.jsx)(o.A,{md:6,children:(0,w.jsx)(b,{title:e("latest_block"),value:(null===y||void 0===y?void 0:y.latest_block)||"N/A",variant:"info",loading:s,icon:"\u23f0"})})]}),T.length>0&&(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(d.A,{className:"mb-4",children:(0,w.jsx)(o.A,{children:(0,w.jsx)(l.A,{children:(0,w.jsxs)(l.A.Body,{children:[(0,w.jsx)(l.A.Title,{children:e("mining_reward_trend")}),(0,w.jsx)(j.u,{width:"100%",height:300,children:(0,w.jsxs)(m.b,{data:T,children:[(0,w.jsx)(_.d,{strokeDasharray:"3 3"}),(0,w.jsx)(g.W,{dataKey:"stat_date",tickFormatter:C}),(0,w.jsx)(u.h,{}),(0,w.jsx)(v.m,{labelFormatter:C,formatter:e=>[D(e),"FIL/TiB"]}),(0,w.jsx)(f.s,{}),(0,w.jsx)(p.N,{type:"monotone",dataKey:"fil_per_tib",stroke:"#8884d8",name:e("mining_reward")})]})})]})})})}),(0,w.jsx)(d.A,{className:"mb-4",children:(0,w.jsx)(o.A,{children:(0,w.jsx)(l.A,{children:(0,w.jsxs)(l.A.Body,{children:[(0,w.jsx)(l.A.Title,{children:e("historical_note")}),(0,w.jsx)("p",{className:"text-muted",children:e("historical_note_description")})]})})})})]}),(0,w.jsx)(d.A,{children:(0,w.jsx)(o.A,{children:(0,w.jsx)(l.A,{children:(0,w.jsxs)(l.A.Body,{children:[(0,w.jsx)(l.A.Title,{children:e("current_network_summary")}),s?(0,w.jsxs)("div",{className:"text-center",children:[(0,w.jsx)(r.A,{animation:"border"}),(0,w.jsx)("p",{className:"mt-2",children:e("loading")})]}):y?(0,w.jsx)(x.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:(0,w.jsxs)("tbody",{children:[(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("block_height")})}),(0,w.jsx)("td",{children:D(y.block_height)}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("network_storage_power")})}),(0,w.jsxs)("td",{children:[D(y.network_storage_power)," EiB"]})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("active_miners")})}),(0,w.jsx)("td",{children:D(y.active_miners)}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("block_reward")})}),(0,w.jsxs)("td",{children:[D(y.block_reward)," FIL"]})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("mining_reward_24h")})}),(0,w.jsxs)("td",{children:[D(y.mining_reward)," FIL/TiB"]}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("fil_production_24h")})}),(0,w.jsxs)("td",{children:[D(y.fil_production_24h)," FIL"]})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("total_pledge_collateral")})}),(0,w.jsxs)("td",{children:[D(y.total_pledge_collateral)," FIL"]}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("messages_24h")})}),(0,w.jsx)("td",{children:D(y.messages_24h)})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("sector_initial_pledge")})}),(0,w.jsxs)("td",{children:[D(y.sector_initial_pledge)," FIL/32GiB"]}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("latest_block")})}),(0,w.jsx)("td",{children:y.latest_block||"N/A"})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("last_updated")})}),(0,w.jsx)("td",{colSpan:"3",children:y.scraped_at?new Date(y.scraped_at).toLocaleString():"N/A"})]})]})}):(0,w.jsx)("p",{children:e("no_data_available")})]})})})})]})}}}]);
//# sourceMappingURL=182.ad92954d.chunk.js.map