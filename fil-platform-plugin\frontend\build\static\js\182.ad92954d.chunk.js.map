{"version": 3, "file": "static/js/182.ad92954d.chunk.js", "mappings": "yUAMA,MAAMA,EAAWC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,KAAEC,EAAI,QAAEC,EAAO,QAAEC,EAAO,KAAEC,GAAMN,EAAA,OAC5DO,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAACC,UAAW,MAAML,0BAAgCM,UACnDH,EAAAA,EAAAA,KAACC,EAAAA,EAAKG,KAAI,CAACF,UAAU,6CAA4CC,UAC7DE,EAAAA,EAAAA,MAAA,OAAKH,UAAU,mDAAkDC,SAAA,EAC7DE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAACJ,UAAU,KAAIC,SAAET,IAC3BI,GACGO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,4BAA2BC,SAAA,EACtCH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,SAASC,KAAK,KAAKP,UAAU,UAChDF,EAAAA,EAAAA,KAAA,QAAAG,SAAM,mBAGVE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAIE,UAAU,OAAMC,SAAER,IACrBC,IAAQI,EAAAA,EAAAA,KAAA,SAAOE,UAAU,aAAYC,SAAEP,UAInDG,IAAQC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,kBAAiBC,SAAEJ,YAgY3D,EA1XeW,KACX,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPd,EAASe,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,OAC5BG,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAS,OAC1CK,EAAgBC,IAAqBN,EAAAA,EAAAA,UAAS,KAC9CO,EAAYC,IAAiBR,EAAAA,EAAAA,WAAS,GAGvCS,EAAoBC,UACtB,IACIX,GAAW,GAGX,MAAMY,EAAUC,OAAOC,SAASC,OAAS,gCACzCC,QAAQC,IAAI,0BAA2BL,GAEvC,MAAMM,QAAqBC,MAAMP,EAAS,CACtCQ,OAAQ,MACRC,YAAa,UACbC,QAAS,CACL,eAAgB,sBAMxB,GAFAN,QAAQC,IAAI,wBAAyBC,EAAaK,QAE9CL,EAAaM,GAAI,CACjB,MAAMC,QAAmBP,EAAaQ,OACtCV,QAAQC,IAAI,qBAAsBQ,EACtC,MACIT,QAAQd,MAAM,mBAAoBgB,EAAaK,QAInD,MAAMI,EAAWd,OAAOC,SAASC,OAAS,2CAC1CC,QAAQC,IAAI,qBAAsBU,GAElC,MAAMC,QAAiBT,MAAMQ,EAAU,CACnCP,OAAQ,MACRC,YAAa,UACbC,QAAS,CACL,eAAgB,sBAMxB,GAFAN,QAAQC,IAAI,mBAAoBW,EAASL,SAEpCK,EAASJ,GAAI,CACd,MAAMK,QAAkBD,EAASE,OAEjC,MADAd,QAAQd,MAAM,kBAAmB2B,GAC3B,IAAIE,MAAM,uBAAuBH,EAASL,YAAYM,IAChE,CAEA,MAAMG,QAAeJ,EAASF,OAC9BV,QAAQC,IAAI,gBAAiBe,GAEzBA,EAAOC,SACP5B,EAAgB2B,EAAOE,MACvB/B,EAAS,OAETA,EAAS6B,EAAOG,SAAW,wCAIzBC,GAEV,CAAE,MAAOlC,GACLc,QAAQd,MAAM,kCAAmCA,GACjDC,EAAS,gDAAkDD,EAAMiC,QACrE,CAAC,QACGnC,GAAW,GACXS,GAAc,EAClB,GAIE2B,EAAsBzB,UACxB,MAAM0B,GAAWC,EAAAA,EAAAA,KACjB,GAAKD,EAEL,IACI,MAAQH,MAAM,KAAEK,UAAiBF,EAASG,KAAKC,UAC/C,IAAKF,EAAM,OAGX,MAAQL,KAAM5B,EAAgBJ,MAAOwC,SAA0BL,EAC1DM,KAAK,iBACLC,OAAO,0BACPC,MAAM,YAAa,CAAEC,WAAW,IAChCC,MAAM,IAEPL,EACA1B,QAAQd,MAAM,mCAAoCwC,GAGlDnC,EAAkBD,EAAe0C,UAEzC,CAAE,MAAO9C,GACLc,QAAQd,MAAM,kCAAmCA,EACrD,IAGJ+C,EAAAA,EAAAA,WAAU,KACNvC,KACD,IAEH,MAKMwC,EAAgBC,GACN,OAARA,QAAwBC,IAARD,EAA0B,MACvC,IAAIE,KAAKC,aAAa,QAAS,CAClCC,sBAAuB,EACvBC,sBAAuB,IACxBC,OAAON,GAGRO,EAAcC,GACT,IAAIC,KAAKD,GAAYE,qBAGhC,OAAI3D,GAEIf,EAAAA,EAAAA,KAAC2E,EAAAA,EAAS,CAACC,OAAK,EAAAzE,UACZH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAAC3E,UAAU,OAAMC,UACjBE,EAAAA,EAAAA,MAACyE,EAAAA,EAAG,CAAA3E,SAAA,EACAH,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,2BACPX,EAAAA,EAAAA,KAAC+E,EAAAA,EAAK,CAAClF,QAAQ,SAAQM,SAAEY,YAQzCV,EAAAA,EAAAA,MAACsE,EAAAA,EAAS,CAACC,OAAK,EAAAzE,SAAA,EACZH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAAC3E,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAAA3E,UACAE,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oDAAmDC,SAAA,EAC9DH,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,2BACPX,EAAAA,EAAAA,KAACgF,EAAAA,EAAM,CACHnF,QAAQ,kBACRoF,QAtCFC,KAClB5D,GAAc,GACdC,KAqCoB4D,SAAU9D,EAAWlB,SAEpBkB,GACGhB,EAAAA,EAAAA,MAAA+E,EAAAA,SAAA,CAAAjF,SAAA,EACIH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,SAASC,KAAK,KAAKP,UAAU,SAC/CS,EAAE,iBAGPA,EAAE,qBAQtBN,EAAAA,EAAAA,MAACwE,EAAAA,EAAG,CAAC3E,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAcqE,cAClCzF,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,yBACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAcsE,uBAClC3F,KAAK,MACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,iBACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAcuE,eAClC3F,QAAQ,OACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAcwE,cAClC7F,KAAK,MACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,uBAKjBM,EAAAA,EAAAA,MAACwE,EAAAA,EAAG,CAAC3E,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,qBACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAcyE,aAClC9F,KAAK,UACLC,QAAQ,YACRC,QAASA,EACTC,KAAK,cAGbC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,sBACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAc0E,oBAClC/F,KAAK,MACLC,QAAQ,OACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,2BACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAc2E,yBAClChG,KAAK,MACLC,QAAQ,SACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAc4E,cAClChG,QAAQ,QACRC,QAASA,EACTC,KAAK,uBAMjBM,EAAAA,EAAAA,MAACwE,EAAAA,EAAG,CAAC3E,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,yBACThB,MAAOoE,EAAyB,OAAZ9C,QAAY,IAAZA,OAAY,EAAZA,EAAc6E,uBAClClG,KAAK,YACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAACO,GAAI,EAAElF,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,OAAmB,OAAZsB,QAAY,IAAZA,OAAY,EAAZA,EAAc8E,eAAgB,MACrClG,QAAQ,OACRC,QAASA,EACTC,KAAK,gBAMhBoB,EAAe6E,OAAS,IACrB3F,EAAAA,EAAAA,MAAA+E,EAAAA,SAAA,CAAAjF,SAAA,EACIH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAAC3E,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAAA3E,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,0BACfX,EAAAA,EAAAA,KAACiG,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAIhG,UAC1CE,EAAAA,EAAAA,MAAC+F,EAAAA,EAAS,CAACrD,KAAM5B,EAAehB,SAAA,EAC5BH,EAAAA,EAAAA,KAACqG,EAAAA,EAAa,CAACC,gBAAgB,SAC/BtG,EAAAA,EAAAA,KAACuG,EAAAA,EAAK,CACFC,QAAQ,YACRC,cAAelC,KAEnBvE,EAAAA,EAAAA,KAAC0G,EAAAA,EAAK,KACN1G,EAAAA,EAAAA,KAAC2G,EAAAA,EAAO,CACJC,eAAgBrC,EAChBsC,UAAYlH,GAAU,CAACoE,EAAapE,GAAQ,cAEhDK,EAAAA,EAAAA,KAAC8G,EAAAA,EAAM,KACP9G,EAAAA,EAAAA,KAAC+G,EAAAA,EAAI,CACDC,KAAK,WACLR,QAAQ,cACRS,OAAO,UACPC,KAAMvG,EAAE,kCASpCX,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAAC3E,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAAA3E,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,sBACfX,EAAAA,EAAAA,KAAA,KAAGE,UAAU,aAAYC,SACpBQ,EAAE,8CAU/BX,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAAA1E,UACAH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAG,CAAA3E,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,6BACdb,GACGO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,cAAaC,SAAA,EACxBH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,YACnBR,EAAAA,EAAAA,KAAA,KAAGE,UAAU,OAAMC,SAAEQ,EAAE,gBAE3BM,GACAjB,EAAAA,EAAAA,KAACmH,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAApH,UACpCE,EAAAA,EAAAA,MAAA,SAAAF,SAAA,EACIE,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfX,EAAAA,EAAAA,KAAA,MAAAG,SAAK4D,EAAa9C,EAAaqE,iBAC/BtF,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,8BACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAK4D,EAAa9C,EAAasE,uBAAuB,cAE1DlF,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,sBACfX,EAAAA,EAAAA,KAAA,MAAAG,SAAK4D,EAAa9C,EAAauE,kBAC/BxF,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAK4D,EAAa9C,EAAawE,cAAc,cAEjDpF,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,0BACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAK4D,EAAa9C,EAAauG,eAAe,eAC9CxH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,2BACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAK4D,EAAa9C,EAAa0E,oBAAoB,cAEvDtF,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,gCACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAK4D,EAAa9C,EAAa2E,yBAAyB,WACxD5F,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfX,EAAAA,EAAAA,KAAA,MAAAG,SAAK4D,EAAa9C,EAAa4E,oBAEnCxF,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,8BACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAK4D,EAAa9C,EAAa6E,uBAAuB,iBACtD9F,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfX,EAAAA,EAAAA,KAAA,MAAAG,SAAKc,EAAa8E,cAAgB,YAEtC1F,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfX,EAAAA,EAAAA,KAAA,MAAIyH,QAAQ,IAAGtH,SAAEc,EAAayG,WAAa,IAAIjD,KAAKxD,EAAayG,YAAYC,iBAAmB,iBAK5G3H,EAAAA,EAAAA,KAAA,KAAAG,SAAIQ,EAAE,mC", "sources": ["pages/customer/Filfox.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, Table, Spinner, Al<PERSON>, Button } from 'react-bootstrap';\nimport { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { getSupabase } from '../../supabaseClient';\n\nconst StatCard = ({ title, value, unit, variant, loading, icon }) => (\n    <Card className={`bg-${variant} text-white mb-3 h-100`}>\n        <Card.Body className=\"d-flex flex-column justify-content-between\">\n            <div className=\"d-flex justify-content-between align-items-start\">\n                <div>\n                    <Card.Title className=\"h6\">{title}</Card.Title>\n                    {loading ? (\n                        <div className=\"d-flex align-items-center\">\n                            <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                            <span>Loading...</span>\n                        </div>\n                    ) : (\n                        <div>\n                            <h4 className=\"mb-0\">{value}</h4>\n                            {unit && <small className=\"opacity-75\">{unit}</small>}\n                        </div>\n                    )}\n                </div>\n                {icon && <div className=\"fs-2 opacity-50\">{icon}</div>}\n            </div>\n        </Card.Body>\n    </Card>\n);\n\nconst Filfox = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [currentStats, setCurrentStats] = useState(null);\n    const [historicalData, setHistoricalData] = useState([]);\n    const [refreshing, setRefreshing] = useState(false);\n\n    // Fetch real-time network stats from WordPress API\n    const fetchNetworkStats = async () => {\n        try {\n            setLoading(true);\n\n            // First test the API connection\n            const testUrl = window.location.origin + '/wp-json/fil-platform/v1/test';\n            console.log('Testing API connection:', testUrl);\n\n            const testResponse = await fetch(testUrl, {\n                method: 'GET',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json',\n                }\n            });\n\n            console.log('Test response status:', testResponse.status);\n\n            if (testResponse.ok) {\n                const testResult = await testResponse.json();\n                console.log('Test API Response:', testResult);\n            } else {\n                console.error('Test API failed:', testResponse.status);\n            }\n\n            // Now try the real endpoint\n            const wpApiUrl = window.location.origin + '/wp-json/fil-platform/v1/filfox-realtime';\n            console.log('Fetching from URL:', wpApiUrl);\n\n            const response = await fetch(wpApiUrl, {\n                method: 'GET',\n                credentials: 'include', // Include cookies for authentication\n                headers: {\n                    'Content-Type': 'application/json',\n                }\n            });\n\n            console.log('Response status:', response.status);\n\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Error response:', errorText);\n                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);\n            }\n\n            const result = await response.json();\n            console.log('API Response:', result);\n\n            if (result.success) {\n                setCurrentStats(result.data);\n                setError(null);\n            } else {\n                setError(result.message || 'Failed to fetch real-time data');\n            }\n\n            // For historical data, we'll fetch from Supabase (only fil_per_tib)\n            await fetchHistoricalData();\n\n        } catch (error) {\n            console.error('Error fetching real-time stats:', error);\n            setError('Failed to load real-time network statistics: ' + error.message);\n        } finally {\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n\n    // Fetch historical data for charts (only fil_per_tib from database)\n    const fetchHistoricalData = async () => {\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        try {\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) return;\n\n            // Fetch historical data for charts (last 30 days, only fil_per_tib)\n            const { data: historicalData, error: historicalError } = await supabase\n                .from('network_stats')\n                .select('stat_date, fil_per_tib')\n                .order('stat_date', { ascending: false })\n                .limit(30);\n\n            if (historicalError) {\n                console.error('Error fetching historical stats:', historicalError);\n            } else {\n                // Reverse to show chronological order in charts\n                setHistoricalData(historicalData.reverse());\n            }\n        } catch (error) {\n            console.error('Error fetching historical data:', error);\n        }\n    };\n\n    useEffect(() => {\n        fetchNetworkStats();\n    }, []);\n\n    const handleRefresh = () => {\n        setRefreshing(true);\n        fetchNetworkStats();\n    };\n\n    const formatNumber = (num) => {\n        if (num === null || num === undefined) return 'N/A';\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 4\n        }).format(num);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString();\n    };\n\n    if (error) {\n        return (\n            <Container fluid>\n                <Row className=\"mb-3\">\n                    <Col>\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Alert variant=\"danger\">{error}</Alert>\n                    </Col>\n                </Row>\n            </Container>\n        );\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Button \n                            variant=\"outline-primary\" \n                            onClick={handleRefresh}\n                            disabled={refreshing}\n                        >\n                            {refreshing ? (\n                                <>\n                                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                                    {t('refreshing')}\n                                </>\n                            ) : (\n                                t('refresh')\n                            )}\n                        </Button>\n                    </div>\n                </Col>\n            </Row>\n\n            {/* Current Statistics Cards */}\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_height')}\n                        value={formatNumber(currentStats?.block_height)}\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔗\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('network_storage_power')}\n                        value={formatNumber(currentStats?.network_storage_power)}\n                        unit=\"EiB\"\n                        variant=\"success\"\n                        loading={loading}\n                        icon=\"💾\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('active_miners')}\n                        value={formatNumber(currentStats?.active_miners)}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⛏️\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_reward')}\n                        value={formatNumber(currentStats?.block_reward)}\n                        unit=\"FIL\"\n                        variant=\"warning\"\n                        loading={loading}\n                        icon=\"🎁\"\n                    />\n                </Col>\n            </Row>\n\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('mining_reward_24h')}\n                        value={formatNumber(currentStats?.fil_per_tib)}\n                        unit=\"FIL/TiB\"\n                        variant=\"secondary\"\n                        loading={loading}\n                        icon=\"⚡\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('fil_production_24h')}\n                        value={formatNumber(currentStats?.fil_production_24h)}\n                        unit=\"FIL\"\n                        variant=\"dark\"\n                        loading={loading}\n                        icon=\"🏭\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('total_pledge_collateral')}\n                        value={formatNumber(currentStats?.total_pledge_collateral)}\n                        unit=\"FIL\"\n                        variant=\"danger\"\n                        loading={loading}\n                        icon=\"🔒\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('messages_24h')}\n                        value={formatNumber(currentStats?.messages_24h)}\n                        variant=\"light\"\n                        loading={loading}\n                        icon=\"📨\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Additional Stats */}\n            <Row className=\"mb-4\">\n                <Col md={6}>\n                    <StatCard\n                        title={t('sector_initial_pledge')}\n                        value={formatNumber(currentStats?.sector_initial_pledge)}\n                        unit=\"FIL/32GiB\"\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔐\"\n                    />\n                </Col>\n                <Col md={6}>\n                    <StatCard\n                        title={t('latest_block')}\n                        value={currentStats?.latest_block || 'N/A'}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⏰\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Historical Charts */}\n            {historicalData.length > 0 && (\n                <>\n                    <Row className=\"mb-4\">\n                        <Col>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('mining_reward_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <LineChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), 'FIL/TiB']}\n                                            />\n                                            <Legend />\n                                            <Line \n                                                type=\"monotone\" \n                                                dataKey=\"fil_per_tib\" \n                                                stroke=\"#8884d8\" \n                                                name={t('mining_reward')}\n                                            />\n                                        </LineChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n\n                    <Row className=\"mb-4\">\n                        <Col>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('historical_note')}</Card.Title>\n                                    <p className=\"text-muted\">\n                                        {t('historical_note_description')}\n                                    </p>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n                </>\n            )}\n\n            {/* Current Data Summary */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('current_network_summary')}</Card.Title>\n                            {loading ? (\n                                <div className=\"text-center\">\n                                    <Spinner animation=\"border\" />\n                                    <p className=\"mt-2\">{t('loading')}</p>\n                                </div>\n                            ) : currentStats ? (\n                                <Table striped bordered hover responsive>\n                                    <tbody>\n                                        <tr>\n                                            <td><strong>{t('block_height')}</strong></td>\n                                            <td>{formatNumber(currentStats.block_height)}</td>\n                                            <td><strong>{t('network_storage_power')}</strong></td>\n                                            <td>{formatNumber(currentStats.network_storage_power)} EiB</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('active_miners')}</strong></td>\n                                            <td>{formatNumber(currentStats.active_miners)}</td>\n                                            <td><strong>{t('block_reward')}</strong></td>\n                                            <td>{formatNumber(currentStats.block_reward)} FIL</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('mining_reward_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.mining_reward)} FIL/TiB</td>\n                                            <td><strong>{t('fil_production_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.fil_production_24h)} FIL</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('total_pledge_collateral')}</strong></td>\n                                            <td>{formatNumber(currentStats.total_pledge_collateral)} FIL</td>\n                                            <td><strong>{t('messages_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.messages_24h)}</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('sector_initial_pledge')}</strong></td>\n                                            <td>{formatNumber(currentStats.sector_initial_pledge)} FIL/32GiB</td>\n                                            <td><strong>{t('latest_block')}</strong></td>\n                                            <td>{currentStats.latest_block || 'N/A'}</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('last_updated')}</strong></td>\n                                            <td colSpan=\"3\">{currentStats.scraped_at ? new Date(currentStats.scraped_at).toLocaleString() : 'N/A'}</td>\n                                        </tr>\n                                    </tbody>\n                                </Table>\n                            ) : (\n                                <p>{t('no_data_available')}</p>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Filfox;\n"], "names": ["StatCard", "_ref", "title", "value", "unit", "variant", "loading", "icon", "_jsx", "Card", "className", "children", "Body", "_jsxs", "Title", "Spinner", "animation", "size", "Filfox", "t", "useTranslation", "setLoading", "useState", "error", "setError", "currentStats", "setCurrentStats", "historicalData", "setHistoricalData", "refreshing", "setRefreshing", "fetchNetworkStats", "async", "testUrl", "window", "location", "origin", "console", "log", "testResponse", "fetch", "method", "credentials", "headers", "status", "ok", "testResult", "json", "wpApiUrl", "response", "errorText", "text", "Error", "result", "success", "data", "message", "fetchHistoricalData", "supabase", "getSupabase", "user", "auth", "getUser", "historicalError", "from", "select", "order", "ascending", "limit", "reverse", "useEffect", "formatNumber", "num", "undefined", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "Container", "fluid", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "onClick", "handleRefresh", "disabled", "_Fragment", "md", "block_height", "network_storage_power", "active_miners", "block_reward", "fil_per_tib", "fil_production_24h", "total_pledge_collateral", "messages_24h", "sector_initial_pledge", "latest_block", "length", "ResponsiveContainer", "width", "height", "Line<PERSON>hart", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XAxis", "dataKey", "tick<PERSON><PERSON><PERSON><PERSON>", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "labelFormatter", "formatter", "Legend", "Line", "type", "stroke", "name", "Table", "striped", "bordered", "hover", "responsive", "mining_reward", "colSpan", "scraped_at", "toLocaleString"], "sourceRoot": ""}