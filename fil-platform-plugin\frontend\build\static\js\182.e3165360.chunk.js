"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[182],{1182:(e,i,s)=>{s.r(i),s.d(i,{default:()=>k});var t=s(5043),r=s(4117),n=s(8628),l=s(7417),a=s(3519),d=s(1072),c=s(8602),o=s(1719),h=s(4282),x=s(4196),j=s(108),m=s(2998),_=s(7734),g=s(2185),u=s(760),v=s(9923),f=s(713),A=s(2872),p=s(4312),w=s(579);const b=e=>{let{title:i,value:s,unit:t,variant:r,loading:a,icon:d}=e;return(0,w.jsx)(n.A,{className:`bg-${r} text-white mb-3 h-100`,children:(0,w.jsx)(n.A.Body,{className:"d-flex flex-column justify-content-between",children:(0,w.jsxs)("div",{className:"d-flex justify-content-between align-items-start",children:[(0,w.jsxs)("div",{children:[(0,w.jsx)(n.A.Title,{className:"h6",children:i}),a?(0,w.jsxs)("div",{className:"d-flex align-items-center",children:[(0,w.jsx)(l.A,{animation:"border",size:"sm",className:"me-2"}),(0,w.jsx)("span",{children:"Loading..."})]}):(0,w.jsxs)("div",{children:[(0,w.jsx)("h4",{className:"mb-0",children:s}),t&&(0,w.jsx)("small",{className:"opacity-75",children:t})]})]}),d&&(0,w.jsx)("div",{className:"fs-2 opacity-50",children:d})]})})})},k=()=>{const{t:e}=(0,r.Bd)(),[i,s]=(0,t.useState)(!0),[k,N]=(0,t.useState)(null),[y,F]=(0,t.useState)(null),[L,B]=(0,t.useState)([]),[I,T]=(0,t.useState)(!1),S=async()=>{try{s(!0);const e=window.location.origin+"/wp-json/fil-platform/v1/filfox-realtime",i=await fetch(e,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(!i.ok)throw new Error(`HTTP error! status: ${i.status}`);const t=await i.json();t.success?(F(t.data),N(null)):N(t.message||"Failed to fetch real-time data"),await E()}catch(k){console.error("Error fetching real-time stats:",k),N("Failed to load real-time network statistics: "+k.message)}finally{s(!1),T(!1)}},E=async()=>{const e=(0,p.b)();if(e)try{const{data:{user:i}}=await e.auth.getUser();if(!i)return;const{data:s,error:t}=await e.from("network_stats").select("stat_date, fil_per_tib").order("stat_date",{ascending:!1}).limit(30);t?console.error("Error fetching historical stats:",t):B(s.reverse())}catch(k){console.error("Error fetching historical data:",k)}};(0,t.useEffect)(()=>{S()},[]);const D=e=>null===e||void 0===e?"N/A":new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:4}).format(e),C=e=>new Date(e).toLocaleDateString();return k?(0,w.jsx)(a.A,{fluid:!0,children:(0,w.jsx)(d.A,{className:"mb-3",children:(0,w.jsxs)(c.A,{children:[(0,w.jsx)("h2",{children:e("filfox_network_stats")}),(0,w.jsx)(o.A,{variant:"danger",children:k})]})})}):(0,w.jsxs)(a.A,{fluid:!0,children:[(0,w.jsx)(d.A,{className:"mb-3",children:(0,w.jsx)(c.A,{children:(0,w.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,w.jsx)("h2",{children:e("filfox_network_stats")}),(0,w.jsx)(h.A,{variant:"outline-primary",onClick:()=>{T(!0),S()},disabled:I,children:I?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(l.A,{animation:"border",size:"sm",className:"me-2"}),e("refreshing")]}):e("refresh")})]})})}),(0,w.jsxs)(d.A,{className:"mb-4",children:[(0,w.jsx)(c.A,{md:3,children:(0,w.jsx)(b,{title:e("block_height"),value:D(null===y||void 0===y?void 0:y.block_height),variant:"primary",loading:i,icon:"\ud83d\udd17"})}),(0,w.jsx)(c.A,{md:3,children:(0,w.jsx)(b,{title:e("network_storage_power"),value:D(null===y||void 0===y?void 0:y.network_storage_power),unit:"EiB",variant:"success",loading:i,icon:"\ud83d\udcbe"})}),(0,w.jsx)(c.A,{md:3,children:(0,w.jsx)(b,{title:e("active_miners"),value:D(null===y||void 0===y?void 0:y.active_miners),variant:"info",loading:i,icon:"\u26cf\ufe0f"})}),(0,w.jsx)(c.A,{md:3,children:(0,w.jsx)(b,{title:e("block_reward"),value:D(null===y||void 0===y?void 0:y.block_reward),unit:"FIL",variant:"warning",loading:i,icon:"\ud83c\udf81"})})]}),(0,w.jsxs)(d.A,{className:"mb-4",children:[(0,w.jsx)(c.A,{md:3,children:(0,w.jsx)(b,{title:e("mining_reward_24h"),value:D(null===y||void 0===y?void 0:y.fil_per_tib),unit:"FIL/TiB",variant:"secondary",loading:i,icon:"\u26a1"})}),(0,w.jsx)(c.A,{md:3,children:(0,w.jsx)(b,{title:e("fil_production_24h"),value:D(null===y||void 0===y?void 0:y.fil_production_24h),unit:"FIL",variant:"dark",loading:i,icon:"\ud83c\udfed"})}),(0,w.jsx)(c.A,{md:3,children:(0,w.jsx)(b,{title:e("total_pledge_collateral"),value:D(null===y||void 0===y?void 0:y.total_pledge_collateral),unit:"FIL",variant:"danger",loading:i,icon:"\ud83d\udd12"})}),(0,w.jsx)(c.A,{md:3,children:(0,w.jsx)(b,{title:e("messages_24h"),value:D(null===y||void 0===y?void 0:y.messages_24h),variant:"light",loading:i,icon:"\ud83d\udce8"})})]}),(0,w.jsxs)(d.A,{className:"mb-4",children:[(0,w.jsx)(c.A,{md:6,children:(0,w.jsx)(b,{title:e("sector_initial_pledge"),value:D(null===y||void 0===y?void 0:y.sector_initial_pledge),unit:"FIL/32GiB",variant:"primary",loading:i,icon:"\ud83d\udd10"})}),(0,w.jsx)(c.A,{md:6,children:(0,w.jsx)(b,{title:e("latest_block"),value:(null===y||void 0===y?void 0:y.latest_block)||"N/A",variant:"info",loading:i,icon:"\u23f0"})})]}),L.length>0&&(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(d.A,{className:"mb-4",children:(0,w.jsx)(c.A,{children:(0,w.jsx)(n.A,{children:(0,w.jsxs)(n.A.Body,{children:[(0,w.jsx)(n.A.Title,{children:e("mining_reward_trend")}),(0,w.jsx)(j.u,{width:"100%",height:300,children:(0,w.jsxs)(m.b,{data:L,children:[(0,w.jsx)(_.d,{strokeDasharray:"3 3"}),(0,w.jsx)(g.W,{dataKey:"stat_date",tickFormatter:C}),(0,w.jsx)(u.h,{}),(0,w.jsx)(v.m,{labelFormatter:C,formatter:e=>[D(e),"FIL/TiB"]}),(0,w.jsx)(f.s,{}),(0,w.jsx)(A.N,{type:"monotone",dataKey:"fil_per_tib",stroke:"#8884d8",name:e("mining_reward")})]})})]})})})}),(0,w.jsx)(d.A,{className:"mb-4",children:(0,w.jsx)(c.A,{children:(0,w.jsx)(n.A,{children:(0,w.jsxs)(n.A.Body,{children:[(0,w.jsx)(n.A.Title,{children:e("historical_note")}),(0,w.jsx)("p",{className:"text-muted",children:e("historical_note_description")})]})})})})]}),(0,w.jsx)(d.A,{children:(0,w.jsx)(c.A,{children:(0,w.jsx)(n.A,{children:(0,w.jsxs)(n.A.Body,{children:[(0,w.jsx)(n.A.Title,{children:e("current_network_summary")}),i?(0,w.jsxs)("div",{className:"text-center",children:[(0,w.jsx)(l.A,{animation:"border"}),(0,w.jsx)("p",{className:"mt-2",children:e("loading")})]}):y?(0,w.jsx)(x.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:(0,w.jsxs)("tbody",{children:[(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("block_height")})}),(0,w.jsx)("td",{children:D(y.block_height)}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("network_storage_power")})}),(0,w.jsxs)("td",{children:[D(y.network_storage_power)," EiB"]})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("active_miners")})}),(0,w.jsx)("td",{children:D(y.active_miners)}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("block_reward")})}),(0,w.jsxs)("td",{children:[D(y.block_reward)," FIL"]})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("mining_reward_24h")})}),(0,w.jsxs)("td",{children:[D(y.mining_reward)," FIL/TiB"]}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("fil_production_24h")})}),(0,w.jsxs)("td",{children:[D(y.fil_production_24h)," FIL"]})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("total_pledge_collateral")})}),(0,w.jsxs)("td",{children:[D(y.total_pledge_collateral)," FIL"]}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("messages_24h")})}),(0,w.jsx)("td",{children:D(y.messages_24h)})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("sector_initial_pledge")})}),(0,w.jsxs)("td",{children:[D(y.sector_initial_pledge)," FIL/32GiB"]}),(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("latest_block")})}),(0,w.jsx)("td",{children:y.latest_block||"N/A"})]}),(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{children:(0,w.jsx)("strong",{children:e("last_updated")})}),(0,w.jsx)("td",{colSpan:"3",children:y.scraped_at?new Date(y.scraped_at).toLocaleString():"N/A"})]})]})}):(0,w.jsx)("p",{children:e("no_data_available")})]})})})})]})}}}]);
//# sourceMappingURL=182.e3165360.chunk.js.map