{"version": 3, "file": "static/js/182.e3165360.chunk.js", "mappings": "yUAMA,MAAMA,EAAWC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,KAAEC,EAAI,QAAEC,EAAO,QAAEC,EAAO,KAAEC,GAAMN,EAAA,OAC5DO,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAACC,UAAW,MAAML,0BAAgCM,UACnDH,EAAAA,EAAAA,KAACC,EAAAA,EAAKG,KAAI,CAACF,UAAU,6CAA4CC,UAC7DE,EAAAA,EAAAA,MAAA,OAAKH,UAAU,mDAAkDC,SAAA,EAC7DE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAACJ,UAAU,KAAIC,SAAET,IAC3BI,GACGO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,4BAA2BC,SAAA,EACtCH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,SAASC,KAAK,KAAKP,UAAU,UAChDF,EAAAA,EAAAA,KAAA,QAAAG,SAAM,mBAGVE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAIE,UAAU,OAAMC,SAAER,IACrBC,IAAQI,EAAAA,EAAAA,KAAA,SAAOE,UAAU,aAAYC,SAAEP,UAInDG,IAAQC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,kBAAiBC,SAAEJ,YAqW3D,EA/VeW,KACX,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPd,EAASe,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,OAC5BG,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAS,OAC1CK,EAAgBC,IAAqBN,EAAAA,EAAAA,UAAS,KAC9CO,EAAYC,IAAiBR,EAAAA,EAAAA,WAAS,GAGvCS,EAAoBC,UACtB,IACIX,GAAW,GAGX,MAAMY,EAAWC,OAAOC,SAASC,OAAS,2CAEpCC,QAAiBC,MAAML,EAAU,CACnCM,OAAQ,MACRC,YAAa,UACbC,QAAS,CACL,eAAgB,sBAIxB,IAAKJ,EAASK,GACV,MAAM,IAAIC,MAAM,uBAAuBN,EAASO,UAGpD,MAAMC,QAAeR,EAASS,OAE1BD,EAAOE,SACPrB,EAAgBmB,EAAOG,MACvBxB,EAAS,OAETA,EAASqB,EAAOI,SAAW,wCAIzBC,GAEV,CAAE,MAAO3B,GACL4B,QAAQ5B,MAAM,kCAAmCA,GACjDC,EAAS,gDAAkDD,EAAM0B,QACrE,CAAC,QACG5B,GAAW,GACXS,GAAc,EAClB,GAIEoB,EAAsBlB,UACxB,MAAMoB,GAAWC,EAAAA,EAAAA,KACjB,GAAKD,EAEL,IACI,MAAQJ,MAAM,KAAEM,UAAiBF,EAASG,KAAKC,UAC/C,IAAKF,EAAM,OAGX,MAAQN,KAAMrB,EAAgBJ,MAAOkC,SAA0BL,EAC1DM,KAAK,iBACLC,OAAO,0BACPC,MAAM,YAAa,CAAEC,WAAW,IAChCC,MAAM,IAEPL,EACAN,QAAQ5B,MAAM,mCAAoCkC,GAGlD7B,EAAkBD,EAAeoC,UAEzC,CAAE,MAAOxC,GACL4B,QAAQ5B,MAAM,kCAAmCA,EACrD,IAGJyC,EAAAA,EAAAA,WAAU,KACNjC,KACD,IAEH,MAKMkC,EAAgBC,GACN,OAARA,QAAwBC,IAARD,EAA0B,MACvC,IAAIE,KAAKC,aAAa,QAAS,CAClCC,sBAAuB,EACvBC,sBAAuB,IACxBC,OAAON,GAGRO,EAAcC,GACT,IAAIC,KAAKD,GAAYE,qBAGhC,OAAIrD,GAEIf,EAAAA,EAAAA,KAACqE,EAAAA,EAAS,CAACC,OAAK,EAAAnE,UACZH,EAAAA,EAAAA,KAACuE,EAAAA,EAAG,CAACrE,UAAU,OAAMC,UACjBE,EAAAA,EAAAA,MAACmE,EAAAA,EAAG,CAAArE,SAAA,EACAH,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,2BACPX,EAAAA,EAAAA,KAACyE,EAAAA,EAAK,CAAC5E,QAAQ,SAAQM,SAAEY,YAQzCV,EAAAA,EAAAA,MAACgE,EAAAA,EAAS,CAACC,OAAK,EAAAnE,SAAA,EACZH,EAAAA,EAAAA,KAACuE,EAAAA,EAAG,CAACrE,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAAArE,UACAE,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oDAAmDC,SAAA,EAC9DH,EAAAA,EAAAA,KAAA,MAAAG,SAAKQ,EAAE,2BACPX,EAAAA,EAAAA,KAAC0E,EAAAA,EAAM,CACH7E,QAAQ,kBACR8E,QAtCFC,KAClBtD,GAAc,GACdC,KAqCoBsD,SAAUxD,EAAWlB,SAEpBkB,GACGhB,EAAAA,EAAAA,MAAAyE,EAAAA,SAAA,CAAA3E,SAAA,EACIH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,SAASC,KAAK,KAAKP,UAAU,SAC/CS,EAAE,iBAGPA,EAAE,qBAQtBN,EAAAA,EAAAA,MAACkE,EAAAA,EAAG,CAACrE,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAc+D,cAClCnF,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,yBACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAcgE,uBAClCrF,KAAK,MACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,iBACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAciE,eAClCrF,QAAQ,OACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAckE,cAClCvF,KAAK,MACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,uBAKjBM,EAAAA,EAAAA,MAACkE,EAAAA,EAAG,CAACrE,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,qBACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAcmE,aAClCxF,KAAK,UACLC,QAAQ,YACRC,QAASA,EACTC,KAAK,cAGbC,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,sBACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAcoE,oBAClCzF,KAAK,MACLC,QAAQ,OACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,2BACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAcqE,yBAClC1F,KAAK,MACLC,QAAQ,SACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAcsE,cAClC1F,QAAQ,QACRC,QAASA,EACTC,KAAK,uBAMjBM,EAAAA,EAAAA,MAACkE,EAAAA,EAAG,CAACrE,UAAU,OAAMC,SAAA,EACjBH,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,yBACThB,MAAO8D,EAAyB,OAAZxC,QAAY,IAAZA,OAAY,EAAZA,EAAcuE,uBAClC5F,KAAK,YACLC,QAAQ,UACRC,QAASA,EACTC,KAAK,oBAGbC,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAACO,GAAI,EAAE5E,UACPH,EAAAA,EAAAA,KAACR,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,OAAmB,OAAZsB,QAAY,IAAZA,OAAY,EAAZA,EAAcwE,eAAgB,MACrC5F,QAAQ,OACRC,QAASA,EACTC,KAAK,gBAMhBoB,EAAeuE,OAAS,IACrBrF,EAAAA,EAAAA,MAAAyE,EAAAA,SAAA,CAAA3E,SAAA,EACIH,EAAAA,EAAAA,KAACuE,EAAAA,EAAG,CAACrE,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAAArE,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,0BACfX,EAAAA,EAAAA,KAAC2F,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAI1F,UAC1CE,EAAAA,EAAAA,MAACyF,EAAAA,EAAS,CAACtD,KAAMrB,EAAehB,SAAA,EAC5BH,EAAAA,EAAAA,KAAC+F,EAAAA,EAAa,CAACC,gBAAgB,SAC/BhG,EAAAA,EAAAA,KAACiG,EAAAA,EAAK,CACFC,QAAQ,YACRC,cAAelC,KAEnBjE,EAAAA,EAAAA,KAACoG,EAAAA,EAAK,KACNpG,EAAAA,EAAAA,KAACqG,EAAAA,EAAO,CACJC,eAAgBrC,EAChBsC,UAAY5G,GAAU,CAAC8D,EAAa9D,GAAQ,cAEhDK,EAAAA,EAAAA,KAACwG,EAAAA,EAAM,KACPxG,EAAAA,EAAAA,KAACyG,EAAAA,EAAI,CACDC,KAAK,WACLR,QAAQ,cACRS,OAAO,UACPC,KAAMjG,EAAE,kCASpCX,EAAAA,EAAAA,KAACuE,EAAAA,EAAG,CAACrE,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAAArE,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,sBACfX,EAAAA,EAAAA,KAAA,KAAGE,UAAU,aAAYC,SACpBQ,EAAE,8CAU/BX,EAAAA,EAAAA,KAACuE,EAAAA,EAAG,CAAApE,UACAH,EAAAA,EAAAA,KAACwE,EAAAA,EAAG,CAAArE,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDE,EAAAA,EAAAA,MAACJ,EAAAA,EAAKG,KAAI,CAAAD,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEQ,EAAE,6BACdb,GACGO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,cAAaC,SAAA,EACxBH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,YACnBR,EAAAA,EAAAA,KAAA,KAAGE,UAAU,OAAMC,SAAEQ,EAAE,gBAE3BM,GACAjB,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAA9G,UACpCE,EAAAA,EAAAA,MAAA,SAAAF,SAAA,EACIE,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfX,EAAAA,EAAAA,KAAA,MAAAG,SAAKsD,EAAaxC,EAAa+D,iBAC/BhF,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,8BACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKsD,EAAaxC,EAAagE,uBAAuB,cAE1D5E,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,sBACfX,EAAAA,EAAAA,KAAA,MAAAG,SAAKsD,EAAaxC,EAAaiE,kBAC/BlF,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKsD,EAAaxC,EAAakE,cAAc,cAEjD9E,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,0BACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKsD,EAAaxC,EAAaiG,eAAe,eAC9ClH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,2BACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKsD,EAAaxC,EAAaoE,oBAAoB,cAEvDhF,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,gCACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKsD,EAAaxC,EAAaqE,yBAAyB,WACxDtF,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfX,EAAAA,EAAAA,KAAA,MAAAG,SAAKsD,EAAaxC,EAAasE,oBAEnClF,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,8BACfN,EAAAA,EAAAA,MAAA,MAAAF,SAAA,CAAKsD,EAAaxC,EAAauE,uBAAuB,iBACtDxF,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfX,EAAAA,EAAAA,KAAA,MAAAG,SAAKc,EAAawE,cAAgB,YAEtCpF,EAAAA,EAAAA,MAAA,MAAAF,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,UAAIH,EAAAA,EAAAA,KAAA,UAAAG,SAASQ,EAAE,qBACfX,EAAAA,EAAAA,KAAA,MAAImH,QAAQ,IAAGhH,SAAEc,EAAamG,WAAa,IAAIjD,KAAKlD,EAAamG,YAAYC,iBAAmB,iBAK5GrH,EAAAA,EAAAA,KAAA,KAAAG,SAAIQ,EAAE,mC", "sources": ["pages/customer/Filfox.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, Table, Spinner, Al<PERSON>, Button } from 'react-bootstrap';\nimport { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { getSupabase } from '../../supabaseClient';\n\nconst StatCard = ({ title, value, unit, variant, loading, icon }) => (\n    <Card className={`bg-${variant} text-white mb-3 h-100`}>\n        <Card.Body className=\"d-flex flex-column justify-content-between\">\n            <div className=\"d-flex justify-content-between align-items-start\">\n                <div>\n                    <Card.Title className=\"h6\">{title}</Card.Title>\n                    {loading ? (\n                        <div className=\"d-flex align-items-center\">\n                            <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                            <span>Loading...</span>\n                        </div>\n                    ) : (\n                        <div>\n                            <h4 className=\"mb-0\">{value}</h4>\n                            {unit && <small className=\"opacity-75\">{unit}</small>}\n                        </div>\n                    )}\n                </div>\n                {icon && <div className=\"fs-2 opacity-50\">{icon}</div>}\n            </div>\n        </Card.Body>\n    </Card>\n);\n\nconst Filfox = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [currentStats, setCurrentStats] = useState(null);\n    const [historicalData, setHistoricalData] = useState([]);\n    const [refreshing, setRefreshing] = useState(false);\n\n    // Fetch real-time network stats from WordPress API\n    const fetchNetworkStats = async () => {\n        try {\n            setLoading(true);\n\n            // Get WordPress site URL from window object or construct it\n            const wpApiUrl = window.location.origin + '/wp-json/fil-platform/v1/filfox-realtime';\n\n            const response = await fetch(wpApiUrl, {\n                method: 'GET',\n                credentials: 'include', // Include cookies for authentication\n                headers: {\n                    'Content-Type': 'application/json',\n                }\n            });\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const result = await response.json();\n\n            if (result.success) {\n                setCurrentStats(result.data);\n                setError(null);\n            } else {\n                setError(result.message || 'Failed to fetch real-time data');\n            }\n\n            // For historical data, we'll fetch from Supabase (only fil_per_tib)\n            await fetchHistoricalData();\n\n        } catch (error) {\n            console.error('Error fetching real-time stats:', error);\n            setError('Failed to load real-time network statistics: ' + error.message);\n        } finally {\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n\n    // Fetch historical data for charts (only fil_per_tib from database)\n    const fetchHistoricalData = async () => {\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        try {\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) return;\n\n            // Fetch historical data for charts (last 30 days, only fil_per_tib)\n            const { data: historicalData, error: historicalError } = await supabase\n                .from('network_stats')\n                .select('stat_date, fil_per_tib')\n                .order('stat_date', { ascending: false })\n                .limit(30);\n\n            if (historicalError) {\n                console.error('Error fetching historical stats:', historicalError);\n            } else {\n                // Reverse to show chronological order in charts\n                setHistoricalData(historicalData.reverse());\n            }\n        } catch (error) {\n            console.error('Error fetching historical data:', error);\n        }\n    };\n\n    useEffect(() => {\n        fetchNetworkStats();\n    }, []);\n\n    const handleRefresh = () => {\n        setRefreshing(true);\n        fetchNetworkStats();\n    };\n\n    const formatNumber = (num) => {\n        if (num === null || num === undefined) return 'N/A';\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 4\n        }).format(num);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString();\n    };\n\n    if (error) {\n        return (\n            <Container fluid>\n                <Row className=\"mb-3\">\n                    <Col>\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Alert variant=\"danger\">{error}</Alert>\n                    </Col>\n                </Row>\n            </Container>\n        );\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Button \n                            variant=\"outline-primary\" \n                            onClick={handleRefresh}\n                            disabled={refreshing}\n                        >\n                            {refreshing ? (\n                                <>\n                                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                                    {t('refreshing')}\n                                </>\n                            ) : (\n                                t('refresh')\n                            )}\n                        </Button>\n                    </div>\n                </Col>\n            </Row>\n\n            {/* Current Statistics Cards */}\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_height')}\n                        value={formatNumber(currentStats?.block_height)}\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔗\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('network_storage_power')}\n                        value={formatNumber(currentStats?.network_storage_power)}\n                        unit=\"EiB\"\n                        variant=\"success\"\n                        loading={loading}\n                        icon=\"💾\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('active_miners')}\n                        value={formatNumber(currentStats?.active_miners)}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⛏️\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_reward')}\n                        value={formatNumber(currentStats?.block_reward)}\n                        unit=\"FIL\"\n                        variant=\"warning\"\n                        loading={loading}\n                        icon=\"🎁\"\n                    />\n                </Col>\n            </Row>\n\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('mining_reward_24h')}\n                        value={formatNumber(currentStats?.fil_per_tib)}\n                        unit=\"FIL/TiB\"\n                        variant=\"secondary\"\n                        loading={loading}\n                        icon=\"⚡\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('fil_production_24h')}\n                        value={formatNumber(currentStats?.fil_production_24h)}\n                        unit=\"FIL\"\n                        variant=\"dark\"\n                        loading={loading}\n                        icon=\"🏭\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('total_pledge_collateral')}\n                        value={formatNumber(currentStats?.total_pledge_collateral)}\n                        unit=\"FIL\"\n                        variant=\"danger\"\n                        loading={loading}\n                        icon=\"🔒\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('messages_24h')}\n                        value={formatNumber(currentStats?.messages_24h)}\n                        variant=\"light\"\n                        loading={loading}\n                        icon=\"📨\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Additional Stats */}\n            <Row className=\"mb-4\">\n                <Col md={6}>\n                    <StatCard\n                        title={t('sector_initial_pledge')}\n                        value={formatNumber(currentStats?.sector_initial_pledge)}\n                        unit=\"FIL/32GiB\"\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔐\"\n                    />\n                </Col>\n                <Col md={6}>\n                    <StatCard\n                        title={t('latest_block')}\n                        value={currentStats?.latest_block || 'N/A'}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⏰\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Historical Charts */}\n            {historicalData.length > 0 && (\n                <>\n                    <Row className=\"mb-4\">\n                        <Col>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('mining_reward_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <LineChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), 'FIL/TiB']}\n                                            />\n                                            <Legend />\n                                            <Line \n                                                type=\"monotone\" \n                                                dataKey=\"fil_per_tib\" \n                                                stroke=\"#8884d8\" \n                                                name={t('mining_reward')}\n                                            />\n                                        </LineChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n\n                    <Row className=\"mb-4\">\n                        <Col>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('historical_note')}</Card.Title>\n                                    <p className=\"text-muted\">\n                                        {t('historical_note_description')}\n                                    </p>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n                </>\n            )}\n\n            {/* Current Data Summary */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('current_network_summary')}</Card.Title>\n                            {loading ? (\n                                <div className=\"text-center\">\n                                    <Spinner animation=\"border\" />\n                                    <p className=\"mt-2\">{t('loading')}</p>\n                                </div>\n                            ) : currentStats ? (\n                                <Table striped bordered hover responsive>\n                                    <tbody>\n                                        <tr>\n                                            <td><strong>{t('block_height')}</strong></td>\n                                            <td>{formatNumber(currentStats.block_height)}</td>\n                                            <td><strong>{t('network_storage_power')}</strong></td>\n                                            <td>{formatNumber(currentStats.network_storage_power)} EiB</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('active_miners')}</strong></td>\n                                            <td>{formatNumber(currentStats.active_miners)}</td>\n                                            <td><strong>{t('block_reward')}</strong></td>\n                                            <td>{formatNumber(currentStats.block_reward)} FIL</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('mining_reward_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.mining_reward)} FIL/TiB</td>\n                                            <td><strong>{t('fil_production_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.fil_production_24h)} FIL</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('total_pledge_collateral')}</strong></td>\n                                            <td>{formatNumber(currentStats.total_pledge_collateral)} FIL</td>\n                                            <td><strong>{t('messages_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.messages_24h)}</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('sector_initial_pledge')}</strong></td>\n                                            <td>{formatNumber(currentStats.sector_initial_pledge)} FIL/32GiB</td>\n                                            <td><strong>{t('latest_block')}</strong></td>\n                                            <td>{currentStats.latest_block || 'N/A'}</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('last_updated')}</strong></td>\n                                            <td colSpan=\"3\">{currentStats.scraped_at ? new Date(currentStats.scraped_at).toLocaleString() : 'N/A'}</td>\n                                        </tr>\n                                    </tbody>\n                                </Table>\n                            ) : (\n                                <p>{t('no_data_available')}</p>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Filfox;\n"], "names": ["StatCard", "_ref", "title", "value", "unit", "variant", "loading", "icon", "_jsx", "Card", "className", "children", "Body", "_jsxs", "Title", "Spinner", "animation", "size", "Filfox", "t", "useTranslation", "setLoading", "useState", "error", "setError", "currentStats", "setCurrentStats", "historicalData", "setHistoricalData", "refreshing", "setRefreshing", "fetchNetworkStats", "async", "wpApiUrl", "window", "location", "origin", "response", "fetch", "method", "credentials", "headers", "ok", "Error", "status", "result", "json", "success", "data", "message", "fetchHistoricalData", "console", "supabase", "getSupabase", "user", "auth", "getUser", "historicalError", "from", "select", "order", "ascending", "limit", "reverse", "useEffect", "formatNumber", "num", "undefined", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "Container", "fluid", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "onClick", "handleRefresh", "disabled", "_Fragment", "md", "block_height", "network_storage_power", "active_miners", "block_reward", "fil_per_tib", "fil_production_24h", "total_pledge_collateral", "messages_24h", "sector_initial_pledge", "latest_block", "length", "ResponsiveContainer", "width", "height", "Line<PERSON>hart", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XAxis", "dataKey", "tick<PERSON><PERSON><PERSON><PERSON>", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "labelFormatter", "formatter", "Legend", "Line", "type", "stroke", "name", "Table", "striped", "bordered", "hover", "responsive", "mining_reward", "colSpan", "scraped_at", "toLocaleString"], "sourceRoot": ""}