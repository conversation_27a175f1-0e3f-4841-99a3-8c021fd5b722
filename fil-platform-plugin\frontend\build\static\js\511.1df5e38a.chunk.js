"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[511],{511:(e,s,r)=>{r.r(s),r.d(s,{default:()=>m});var a=r(5043),t=r(3519),d=r(1072),l=r(8602),o=r(8628),n=r(4196),c=r(4312),i=r(4117),f=r(579);const m=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)([]),[m,x]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,c.b)();if(!e)return;x(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void x(!1);const{data:a,error:t}=await e.from("network_stats").select("\n                    stat_date,\n                    fil_per_tib\n                ").order("stat_date",{ascending:!1});t?console.error("Error fetching network stats:",t):r(a),x(!1)})()},[]),m?(0,f.jsx)("div",{children:e("loading_network_stats")}):(0,f.jsxs)(t.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("network_stats")}),(0,f.jsx)(d.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsx)(o.A,{children:(0,f.jsx)(o.A.Body,{children:(0,f.jsxs)(n.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:e("stat_date")}),(0,f.jsx)("th",{children:e("fil_per_tib")})]})}),(0,f.jsx)("tbody",{children:0===s.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"3",className:"text-center",children:e("no_network_stats_available")})}):s.map((e,s)=>(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:new Date(e.stat_date).toLocaleDateString()}),(0,f.jsxs)("td",{children:[e.fil_per_tib?Number(e.fil_per_tib).toFixed(8):"0"," FIL/TiB"]})]},`${e.stat_date}`))})]})})})})})]})}},1072:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),o=r(579);const n=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...n}=e;const c=(0,l.oU)(r,"row"),i=(0,l.gy)(),f=(0,l.Jm)(),m=`${c}-cols`,x=[];return i.forEach(e=>{const s=n[e];let r;delete n[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${m}${a}-${r}`)}),(0,o.jsx)(d,{ref:s,...n,className:t()(a,c,...x)})});n.displayName="Row";const c=n},4196:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),o=r(579);const n=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:n,borderless:c,hover:i,size:f,variant:m,responsive:x,...b}=e;const h=(0,l.oU)(r,"table"),N=t()(a,h,m&&`${h}-${m}`,f&&`${h}-${f}`,d&&`${h}-${"string"===typeof d?`striped-${d}`:"striped"}`,n&&`${h}-bordered`,c&&`${h}-borderless`,i&&`${h}-hover`),u=(0,o.jsx)("table",{...b,className:N,ref:s});if(x){let e=`${h}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,o.jsx)("div",{className:e,children:u})}return u});n.displayName="Table";const c=n},8602:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),o=r(579);const n=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:n,spans:c}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,l.oU)(r,"col");const o=(0,l.gy)(),n=(0,l.Jm)(),c=[],i=[];return o.forEach(e=>{const s=d[e];let a,t,l;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:l}=s):a=s;const o=e!==n?`-${e}`:"";a&&c.push(!0===a?`${r}${o}`:`${r}${o}-${a}`),null!=l&&i.push(`order${o}-${l}`),null!=t&&i.push(`offset${o}-${t}`)}),[{...d,className:t()(a,...c,...i)},{as:s,bsPrefix:r,spans:c}]}(e);return(0,o.jsx)(d,{...a,ref:s,className:t()(r,!c.length&&n)})});n.displayName="Col";const c=n},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),o=r(579);const n=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...n}=e;return a=(0,l.oU)(a,"card-body"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...n})});n.displayName="CardBody";const c=n,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...n}=e;return a=(0,l.oU)(a,"card-footer"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...n})});i.displayName="CardFooter";const f=i;var m=r(1778);const x=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...c}=e;const i=(0,l.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,o.jsx)(m.A.Provider,{value:f,children:(0,o.jsx)(n,{ref:s,...c,className:t()(a,i)})})});x.displayName="CardHeader";const b=x,h=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:n="img",...c}=e;const i=(0,l.oU)(r,"card-img");return(0,o.jsx)(n,{ref:s,className:t()(d?`${i}-${d}`:i,a),...c})});h.displayName="CardImg";const N=h,u=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...n}=e;return a=(0,l.oU)(a,"card-img-overlay"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...n})});u.displayName="CardImgOverlay";const p=u,j=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...n}=e;return a=(0,l.oU)(a,"card-link"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...n})});j.displayName="CardLink";const $=j;var v=r(4488);const y=(0,v.A)("h6"),w=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=y,...n}=e;return a=(0,l.oU)(a,"card-subtitle"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...n})});w.displayName="CardSubtitle";const g=w,_=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...n}=e;return a=(0,l.oU)(a,"card-text"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...n})});_.displayName="CardText";const P=_,R=(0,v.A)("h5"),U=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=R,...n}=e;return a=(0,l.oU)(a,"card-title"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...n})});U.displayName="CardTitle";const A=U,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:n,border:i,body:f=!1,children:m,as:x="div",...b}=e;const h=(0,l.oU)(r,"card");return(0,o.jsx)(x,{ref:s,...b,className:t()(a,h,d&&`bg-${d}`,n&&`text-${n}`,i&&`border-${i}`),children:f?(0,o.jsx)(c,{children:m}):m})});C.displayName="Card";const k=Object.assign(C,{Img:N,Title:A,Subtitle:g,Body:c,Link:$,Text:P,Header:b,Footer:f,ImgOverlay:p})}}]);
//# sourceMappingURL=511.1df5e38a.chunk.js.map