{"version": 3, "file": "static/js/511.1df5e38a.chunk.js", "mappings": "6NAKA,MA+EA,EA/EqBA,KACjB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAmCvC,OAjCAG,EAAAA,EAAAA,WAAU,KACaC,WACf,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAM,KAAEK,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,iBACLC,OAAO,uFAIPC,MAAM,YAAa,CAAEC,WAAW,IAEjCJ,EACAK,QAAQL,MAAM,gCAAiCA,GAE/CZ,EAASQ,GAEbL,GAAW,IAGfe,IACD,IAEChB,GACOiB,EAAAA,EAAAA,KAAA,OAAAC,SAAMvB,EAAE,4BAIfwB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACND,EAAAA,EAAAA,KAAA,MAAII,UAAU,OAAMH,SAAEvB,EAAE,oBACxBsB,EAAAA,EAAAA,KAACK,EAAAA,EAAG,CAAAJ,UACAD,EAAAA,EAAAA,KAACM,EAAAA,EAAG,CAAAL,UACAD,EAAAA,EAAAA,KAACO,EAAAA,EAAI,CAAAN,UACDD,EAAAA,EAAAA,KAACO,EAAAA,EAAKC,KAAI,CAAAP,UACNC,EAAAA,EAAAA,MAACO,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAZ,SAAA,EACpCD,EAAAA,EAAAA,KAAA,SAAAC,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACID,EAAAA,EAAAA,KAAA,MAAAC,SAAKvB,EAAE,gBACPsB,EAAAA,EAAAA,KAAA,MAAAC,SAAKvB,EAAE,uBAGfsB,EAAAA,EAAAA,KAAA,SAAAC,SACsB,IAAjBrB,EAAMkC,QACHd,EAAAA,EAAAA,KAAA,MAAAC,UACID,EAAAA,EAAAA,KAAA,MAAIe,QAAQ,IAAIX,UAAU,cAAaH,SAAEvB,EAAE,kCAG/CE,EAAMoC,IAAI,CAACC,EAAMC,KACbhB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACID,EAAAA,EAAAA,KAAA,MAAAC,SAAK,IAAIkB,KAAKF,EAAKG,WAAWC,wBAC9BnB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKgB,EAAKK,YAAcC,OAAON,EAAKK,aAAaE,QAAQ,GAAK,IAAI,gBAF7D,GAAGP,EAAKG,8B,sFC9D7D,MAAMf,EAAmBoB,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRxB,EAEAyB,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMM,GAAoBC,EAAAA,EAAAA,IAAmBL,EAAU,OACjDM,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtC3C,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,KACFI,EACH3B,UAAW0C,IAAW1C,EAAW4B,KAAsBO,OAG3DlC,EAAI0C,YAAc,MAClB,S,sFCjCA,MAAMtC,EAAqBgB,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRxB,EAAS,QACTM,EAAO,SACPC,EAAQ,WACRqC,EAAU,MACVpC,EAAK,KACLqC,EAAI,QACJC,EAAO,WACPrC,KACGkB,GACJL,EACC,MAAMM,GAAoBC,EAAAA,EAAAA,IAAmBL,EAAU,SACjDW,EAAUO,IAAW1C,EAAW4B,EAAmBkB,GAAW,GAAGlB,KAAqBkB,IAAWD,GAAQ,GAAGjB,KAAqBiB,IAAQvC,GAAW,GAAGsB,KAAwC,kBAAZtB,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGqB,aAA8BgB,GAAc,GAAGhB,eAAgCpB,GAAS,GAAGoB,WACxVmB,GAAqBnD,EAAAA,EAAAA,KAAK,QAAS,IACpC+B,EACH3B,UAAWmC,EACXZ,IAAKA,IAEP,GAAId,EAAY,CACd,IAAIuC,EAAkB,GAAGpB,eAIzB,MAH0B,kBAAfnB,IACTuC,EAAkB,GAAGA,KAAmBvC,MAEtBb,EAAAA,EAAAA,KAAK,MAAO,CAC9BI,UAAWgD,EACXnD,SAAUkD,GAEd,CACA,OAAOA,IAET1C,EAAMsC,YAAc,QACpB,S,sFCQA,MAAMzC,EAAmBmB,EAAAA,WAEzB,CAACM,EAAOJ,KACN,OAAO,UACLvB,KACGiD,IAEHxB,GAAIC,EAAY,MAAK,SACrBF,EAAQ,MACR0B,IAjDG,SAAe5B,GAKnB,IALoB,GACrBG,EAAE,SACFD,EAAQ,UACRxB,KACG2B,GACJL,EACCE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,OACxC,MAAMM,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBiB,EAAQ,GACRf,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIc,EACAC,EACA5D,SAHGmC,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCa,OACAC,SACA5D,SACE8C,GAEJa,EAAOb,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDc,GAAMD,EAAMT,MAAc,IAATU,EAAgB,GAAG3B,IAAWgB,IAAU,GAAGhB,IAAWgB,KAASW,KACvE,MAAT3D,GAAe2C,EAAQM,KAAK,QAAQD,KAAShD,KACnC,MAAV4D,GAAgBjB,EAAQM,KAAK,SAASD,KAASY,OAE9C,CAAC,IACHzB,EACH3B,UAAW0C,IAAW1C,KAAckD,KAAUf,IAC7C,CACDV,KACAD,WACA0B,SAEJ,CAWOG,CAAO1B,GACZ,OAAoB/B,EAAAA,EAAAA,KAAK8B,EAAW,IAC/BuB,EACH1B,IAAKA,EACLvB,UAAW0C,IAAW1C,GAAYkD,EAAMxC,QAAUc,OAGtDtB,EAAIyC,YAAc,MAClB,S,sFC1DA,MAAMW,EAAwBjC,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CvB,EAAS,SACTwB,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,EACLvB,UAAW0C,IAAW1C,EAAWwB,MAC9BG,MAGP2B,EAASX,YAAc,WACvB,UCdMY,EAA0BlC,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDvB,EAAS,SACTwB,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,gBACpB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,EACLvB,UAAW0C,IAAW1C,EAAWwB,MAC9BG,MAGP4B,EAAWZ,YAAc,aACzB,U,cCZA,MAAMa,EAA0BnC,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRxB,EAEAyB,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMmC,GAAS5B,EAAAA,EAAAA,IAAmBL,EAAU,eACtCkC,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBH,IAClB,CAACA,IACL,OAAoB7D,EAAAA,EAAAA,KAAKiE,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACP7D,UAAuBD,EAAAA,EAAAA,KAAK8B,EAAW,CACrCH,IAAKA,KACFI,EACH3B,UAAW0C,IAAW1C,EAAWyD,SAIvCD,EAAWb,YAAc,aACzB,UCvBMqB,EAAuB3C,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRxB,EAAS,QACT8C,EACArB,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMmC,GAAS5B,EAAAA,EAAAA,IAAmBL,EAAU,YAC5C,OAAoB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,EACLvB,UAAW0C,IAAWI,EAAU,GAAGW,KAAUX,IAAYW,EAAQzD,MAC9D2B,MAGPqC,EAAQrB,YAAc,UACtB,UCjBMsB,EAA8B5C,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDvB,EAAS,SACTwB,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,qBACpB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,EACLvB,UAAW0C,IAAW1C,EAAWwB,MAC9BG,MAGPsC,EAAetB,YAAc,iBAC7B,UCdMuB,EAAwB7C,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CvB,EAAS,SACTwB,EACAC,GAAIC,EAAY,OACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,EACLvB,UAAW0C,IAAW1C,EAAWwB,MAC9BG,MAGPuC,EAASvB,YAAc,WACvB,U,cCbA,MAAMwB,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BhD,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDvB,EAAS,SACTwB,EACAC,GAAIC,EAAYyC,KACbxC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,kBACpB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,EACLvB,UAAW0C,IAAW1C,EAAWwB,MAC9BG,MAGP0C,EAAa1B,YAAc,eAC3B,UChBM2B,EAAwBjD,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CvB,EAAS,SACTwB,EACAC,GAAIC,EAAY,OACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,EACLvB,UAAW0C,IAAW1C,EAAWwB,MAC9BG,MAGP2C,EAAS3B,YAAc,WACvB,UCbM4B,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyBnD,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CvB,EAAS,SACTwB,EACAC,GAAIC,EAAY6C,KACb5C,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,eACpB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,EACLvB,UAAW0C,IAAW1C,EAAWwB,MAC9BG,MAGP6C,EAAU7B,YAAc,YACxB,UCPMxC,EAAoBkB,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRxB,EAAS,GACTyE,EAAE,KACFC,EAAI,OACJC,EAAM,KACNC,GAAO,EAAK,SACZ/E,EAEA4B,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMmC,GAAS5B,EAAAA,EAAAA,IAAmBL,EAAU,QAC5C,OAAoB5B,EAAAA,EAAAA,KAAK8B,EAAW,CAClCH,IAAKA,KACFI,EACH3B,UAAW0C,IAAW1C,EAAWyD,EAAQgB,GAAM,MAAMA,IAAMC,GAAQ,QAAQA,IAAQC,GAAU,UAAUA,KACvG9E,SAAU+E,GAAoBhF,EAAAA,EAAAA,KAAK0D,EAAU,CAC3CzD,SAAUA,IACPA,MAGTM,EAAKwC,YAAc,OACnB,QAAekC,OAAOC,OAAO3E,EAAM,CACjC4E,IAAKf,EACLgB,MAAOR,EACPS,SAAUZ,EACVjE,KAAMkD,EACN4B,KAAMhB,EACNiB,KAAMb,EACNc,OAAQ5B,EACR6B,OAAQ9B,EACR+B,WAAYrB,G", "sources": ["pages/maker/NetworkStats.js", "../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst NetworkStats = () => {\n    const { t } = useTranslation();\n    const [stats, setStats] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchStats = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch network stats\n            const { data, error } = await supabase\n                .from('network_stats')\n                .select(`\n                    stat_date,\n                    fil_per_tib\n                `)\n                .order('stat_date', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching network stats:', error);\n            } else {\n                setStats(data);\n            }\n            setLoading(false);\n        };\n\n        fetchStats();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_network_stats')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('network_stats')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('stat_date')}</th>\n                                        <th>{t('fil_per_tib')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {stats.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"3\" className=\"text-center\">{t('no_network_stats_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        stats.map((stat, index) => (\n                                            <tr key={`${stat.stat_date}`}>\n                                                <td>{new Date(stat.stat_date).toLocaleDateString()}</td>\n                                                <td>{stat.fil_per_tib ? Number(stat.fil_per_tib).toFixed(8) : '0'} FIL/TiB</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default NetworkStats;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["NetworkStats", "t", "useTranslation", "stats", "setStats", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "fetchStats", "_jsx", "children", "_jsxs", "Container", "className", "Row", "Col", "Card", "Body", "Table", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "stat", "index", "Date", "stat_date", "toLocaleDateString", "fil_per_tib", "Number", "toFixed", "React", "_ref", "ref", "bsPrefix", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "classNames", "displayName", "borderless", "size", "variant", "table", "responsiveClass", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}