(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[574],{677:(e,t,r)=>{"use strict";r.d(t,{h:()=>p});var n=r(5043),a=r(8387),i=r(240),o=r(165),c=r(6307),s=r(6371);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(null,arguments)}var u=e=>{var{cx:t,cy:r,radius:n,angle:a,sign:i,isExternal:c,cornerRadius:s,cornerIsExternal:l}=e,u=s*(c?1:-1)+n,d=Math.asin(s/u)/o.Kg,f=l?a:a+i*d,p=l?a-i*d:a;return{center:(0,o.IZ)(t,r,u,f),circleTangency:(0,o.IZ)(t,r,n,f),lineTangency:(0,o.IZ)(t,r,u*Math.cos(d*o.Kg),p),theta:d}},d=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:a,startAngle:i,endAngle:s}=e,l=((e,t)=>(0,c.sA)(t-e)*Math.min(Math.abs(t-e),359.999))(i,s),u=i+l,d=(0,o.IZ)(t,r,a,i),f=(0,o.IZ)(t,r,a,u),p="M ".concat(d.x,",").concat(d.y,"\n    A ").concat(a,",").concat(a,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(i>u),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var y=(0,o.IZ)(t,r,n,i),v=(0,o.IZ)(t,r,n,u);p+="L ".concat(v.x,",").concat(v.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(i<=u),",\n            ").concat(y.x,",").concat(y.y," Z")}else p+="L ".concat(t,",").concat(r," Z");return p},f={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},p=e=>{var t=(0,s.e)(e,f),{cx:r,cy:o,innerRadius:p,outerRadius:y,cornerRadius:v,forceCornerRadius:m,cornerIsExternal:h,startAngle:b,endAngle:g,className:x}=t;if(y<p||b===g)return null;var O,w=(0,a.$)("recharts-sector",x),j=y-p,E=(0,c.F4)(v,j,0,!0);return O=E>0&&Math.abs(b-g)<360?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:a,cornerRadius:i,forceCornerRadius:o,cornerIsExternal:s,startAngle:l,endAngle:f}=e,p=(0,c.sA)(f-l),{circleTangency:y,lineTangency:v,theta:m}=u({cx:t,cy:r,radius:a,angle:l,sign:p,cornerRadius:i,cornerIsExternal:s}),{circleTangency:h,lineTangency:b,theta:g}=u({cx:t,cy:r,radius:a,angle:f,sign:-p,cornerRadius:i,cornerIsExternal:s}),x=s?Math.abs(l-f):Math.abs(l-f)-m-g;if(x<0)return o?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):d({cx:t,cy:r,innerRadius:n,outerRadius:a,startAngle:l,endAngle:f});var O="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(p<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,").concat(+(x>180),",").concat(+(p<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(p<0),",").concat(b.x,",").concat(b.y,"\n  ");if(n>0){var{circleTangency:w,lineTangency:j,theta:E}=u({cx:t,cy:r,radius:n,angle:l,sign:p,isExternal:!0,cornerRadius:i,cornerIsExternal:s}),{circleTangency:P,lineTangency:A,theta:S}=u({cx:t,cy:r,radius:n,angle:f,sign:-p,isExternal:!0,cornerRadius:i,cornerIsExternal:s}),k=s?Math.abs(l-f):Math.abs(l-f)-E-S;if(k<0&&0===i)return"".concat(O,"L").concat(t,",").concat(r,"Z");O+="L".concat(A.x,",").concat(A.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(p<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(k>180),",").concat(+(p>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(p<0),",").concat(j.x,",").concat(j.y,"Z")}else O+="L".concat(t,",").concat(r,"Z");return O})({cx:r,cy:o,innerRadius:p,outerRadius:y,cornerRadius:Math.min(E,j/2),forceCornerRadius:m,cornerIsExternal:h,startAngle:b,endAngle:g}):d({cx:r,cy:o,innerRadius:p,outerRadius:y,startAngle:b,endAngle:g}),n.createElement("path",l({},(0,i.J9)(t,!0),{className:w,d:O}))}},1719:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var n=r(8139),a=r.n(n),i=r(5043),o=r(1969),c=r(6618),s=r(7852),l=r(4488),u=r(579);const d=(0,l.A)("h4");d.displayName="DivStyledAsH4";const f=i.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:i=d,...o}=e;return n=(0,s.oU)(n,"alert-heading"),(0,u.jsx)(i,{ref:t,className:a()(r,n),...o})});f.displayName="AlertHeading";const p=f;var y=r(7071);const v=i.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:i=y.A,...o}=e;return n=(0,s.oU)(n,"alert-link"),(0,u.jsx)(i,{ref:t,className:a()(r,n),...o})});v.displayName="AlertLink";const m=v;var h=r(8072),b=r(5632);const g=i.forwardRef((e,t)=>{const{bsPrefix:r,show:n=!0,closeLabel:i="Close alert",closeVariant:l,className:d,children:f,variant:p="primary",onClose:y,dismissible:v,transition:m=h.A,...g}=(0,o.Zw)(e,{show:"onClose"}),x=(0,s.oU)(r,"alert"),O=(0,c.A)(e=>{y&&y(!1,e)}),w=!0===m?h.A:m,j=(0,u.jsxs)("div",{role:"alert",...w?void 0:g,ref:t,className:a()(d,x,p&&`${x}-${p}`,v&&`${x}-dismissible`),children:[v&&(0,u.jsx)(b.A,{onClick:O,"aria-label":i,variant:l}),f]});return w?(0,u.jsx)(w,{unmountOnExit:!0,...g,ref:void 0,in:n,children:j}):n?j:null});g.displayName="Alert";const x=Object.assign(g,{Link:m,Heading:p})},2291:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var n=r(5043),a=r(1603),i=r(4664),o=["axis","item"],c=(0,n.forwardRef)((e,t)=>n.createElement(i.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:o,tooltipPayloadSearcher:a.uN,categoricalChartProps:e,ref:t}))},2682:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!==typeof e)return!1;if(null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){const t=e[Symbol.toStringTag];if(null==t)return!1;return!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},4196:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(8139),a=r.n(n),i=r(5043),o=r(7852),c=r(579);const s=i.forwardRef((e,t)=>{let{bsPrefix:r,className:n,striped:i,bordered:s,borderless:l,hover:u,size:d,variant:f,responsive:p,...y}=e;const v=(0,o.oU)(r,"table"),m=a()(n,v,f&&`${v}-${f}`,d&&`${v}-${d}`,i&&`${v}-${"string"===typeof i?`striped-${i}`:"striped"}`,s&&`${v}-bordered`,l&&`${v}-borderless`,u&&`${v}-hover`),h=(0,c.jsx)("table",{...y,className:m,ref:t});if(p){let e=`${v}-responsive`;return"string"===typeof p&&(e=`${e}-${p}`),(0,c.jsx)("div",{className:e,children:h})}return h});s.displayName="Table";const l=s},4342:(e,t,r)=>{"use strict";r.d(t,{M:()=>d});var n=r(5043),a=r(8387),i=r(240),o=r(6371),c=r(5654);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},s.apply(null,arguments)}var l=(e,t,r,n,a)=>{var i,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,s=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&a instanceof Array){for(var u=[0,0,0,0],d=0;d<4;d++)u[d]=a[d]>o?o:a[d];i="M".concat(e,",").concat(t+c*u[0]),u[0]>0&&(i+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(l,",").concat(e+s*u[0],",").concat(t)),i+="L ".concat(e+r-s*u[1],",").concat(t),u[1]>0&&(i+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(l,",\n        ").concat(e+r,",").concat(t+c*u[1])),i+="L ".concat(e+r,",").concat(t+n-c*u[2]),u[2]>0&&(i+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(l,",\n        ").concat(e+r-s*u[2],",").concat(t+n)),i+="L ".concat(e+s*u[3],",").concat(t+n),u[3]>0&&(i+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(l,",\n        ").concat(e,",").concat(t+n-c*u[3])),i+="Z"}else if(o>0&&a===+a&&a>0){var f=Math.min(o,a);i="M ".concat(e,",").concat(t+c*f,"\n            A ").concat(f,",").concat(f,",0,0,").concat(l,",").concat(e+s*f,",").concat(t,"\n            L ").concat(e+r-s*f,",").concat(t,"\n            A ").concat(f,",").concat(f,",0,0,").concat(l,",").concat(e+r,",").concat(t+c*f,"\n            L ").concat(e+r,",").concat(t+n-c*f,"\n            A ").concat(f,",").concat(f,",0,0,").concat(l,",").concat(e+r-s*f,",").concat(t+n,"\n            L ").concat(e+s*f,",").concat(t+n,"\n            A ").concat(f,",").concat(f,",0,0,").concat(l,",").concat(e,",").concat(t+n-c*f," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},u={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},d=e=>{var t=(0,o.e)(e,u),r=(0,n.useRef)(null),[d,f]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&f(e)}catch(t){}},[]);var{x:p,y:y,width:v,height:m,radius:h,className:b}=t,{animationEasing:g,animationDuration:x,animationBegin:O,isAnimationActive:w,isUpdateAnimationActive:j}=t;if(p!==+p||y!==+y||v!==+v||m!==+m||0===v||0===m)return null;var E=(0,a.$)("recharts-rectangle",b);return j?n.createElement(c.i,{canBegin:d>0,from:{width:v,height:m,x:p,y:y},to:{width:v,height:m,x:p,y:y},duration:x,animationEasing:g,isActive:j},e=>{var{width:a,height:o,x:u,y:f}=e;return n.createElement(c.i,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:O,duration:x,isActive:w,easing:g},n.createElement("path",s({},(0,i.J9)(t,!0),{className:E,d:l(u,f,a,o,h),ref:r})))}):n.createElement("path",s({},(0,i.J9)(t,!0),{className:E,d:l(p,y,v,m,h)}))}},5525:(e,t,r)=>{"use strict";r.d(t,{m:()=>ce});var n=r(5043),a=r(7950),i=r(3821),o=r.n(i),c=r(8387),s=r(6307);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(null,arguments)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){f(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e){return Array.isArray(e)&&(0,s.vh)(e[0])&&(0,s.vh)(e[1])?e.join(" ~ "):e}var y=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:a={},labelStyle:i={},payload:u,formatter:f,itemSorter:y,wrapperClassName:v,labelClassName:m,label:h,labelFormatter:b,accessibilityLayer:g=!1}=e,x=d({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),O=d({margin:0},i),w=!(0,s.uy)(h),j=w?h:"",E=(0,c.$)("recharts-default-tooltip",v),P=(0,c.$)("recharts-tooltip-label",m);w&&b&&void 0!==u&&null!==u&&(j=b(h,u));var A=g?{role:"status","aria-live":"assertive"}:{};return n.createElement("div",l({className:E,style:x},A),n.createElement("p",{className:P,style:O},n.isValidElement(j)?j:"".concat(j)),(()=>{if(u&&u.length){var e=(y?o()(u,y):u).map((e,r)=>{if("none"===e.type)return null;var i=e.formatter||f||p,{value:o,name:c}=e,l=o,y=c;if(i){var v=i(o,c,e,r,u);if(Array.isArray(v))[l,y]=v;else{if(null==v)return null;l=v}}var m=d({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},a);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:m},(0,s.vh)(y)?n.createElement("span",{className:"recharts-tooltip-item-name"},y):null,(0,s.vh)(y)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},l),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},v="recharts-tooltip-wrapper",m={visibility:"hidden"};function h(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,c.$)(v,{["".concat(v,"-right")]:(0,s.Et)(r)&&t&&(0,s.Et)(t.x)&&r>=t.x,["".concat(v,"-left")]:(0,s.Et)(r)&&t&&(0,s.Et)(t.x)&&r<t.x,["".concat(v,"-bottom")]:(0,s.Et)(n)&&t&&(0,s.Et)(t.y)&&n>=t.y,["".concat(v,"-top")]:(0,s.Et)(n)&&t&&(0,s.Et)(t.y)&&n<t.y})}function b(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:a,position:i,reverseDirection:o,tooltipDimension:c,viewBox:l,viewBoxDimension:u}=e;if(i&&(0,s.Et)(i[n]))return i[n];var d=r[n]-c-(a>0?a:0),f=r[n]+a;if(t[n])return o[n]?d:f;var p=l[n];return null==p?0:o[n]?d<p?Math.max(f,p):Math.max(d,p):null==u?0:f+c>p+u?Math.max(d,p):Math.max(f,p)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function O(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class w extends n.PureComponent{constructor(){super(...arguments),O(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),O(this,"handleKeyDown",e=>{var t,r,n,a;"Escape"===e.key&&this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(t=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==t?t:0,y:null!==(n=null===(a=this.props.coordinate)||void 0===a?void 0:a.y)&&void 0!==n?n:0}})})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)===this.state.dismissedAtCoordinate.x&&(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:a,children:i,coordinate:o,hasPayload:c,isAnimationActive:s,offset:l,position:u,reverseDirection:d,useTranslate3d:f,viewBox:p,wrapperStyle:y,lastBoundingBox:v,innerRef:g,hasPortalFromProps:O}=this.props,{cssClasses:w,cssProperties:j}=function(e){var t,r,n,{allowEscapeViewBox:a,coordinate:i,offsetTopLeft:o,position:c,reverseDirection:s,tooltipBox:l,useTranslate3d:u,viewBox:d}=e;return t=l.height>0&&l.width>0&&i?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=b({allowEscapeViewBox:a,coordinate:i,key:"x",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:l.width,viewBox:d,viewBoxDimension:d.width}),translateY:n=b({allowEscapeViewBox:a,coordinate:i,key:"y",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:l.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:u}):m,{cssProperties:t,cssClasses:h({translateX:r,translateY:n,coordinate:i})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:l,position:u,reverseDirection:d,tooltipBox:{height:v.height,width:v.width},useTranslate3d:f,viewBox:p}),E=O?{}:x(x({transition:s&&e?"transform ".concat(r,"ms ").concat(a):void 0},j),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&c?"visible":"hidden",position:"absolute",top:0,left:0}),P=x(x({},E),{},{visibility:!this.state.dismissed&&e&&c?"visible":"hidden"},y);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:w,style:P,ref:g},i)}}var j=r(6015),E=r(2598),P=r(8796),A=r(9949),S=r(982),k=r(8471),D=r(240),I=["x","y","top","left","width","height","className"];function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},N.apply(null,arguments)}function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var T=(e,t,r,n,a,i)=>"M".concat(e,",").concat(a,"v").concat(n,"M").concat(i,",").concat(t,"h").concat(r),B=e=>{var{x:t=0,y:r=0,top:a=0,left:i=0,width:o=0,height:l=0,className:u}=e,d=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){z(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:a,left:i,width:o,height:l},function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,I));return(0,s.Et)(t)&&(0,s.Et)(r)&&(0,s.Et)(o)&&(0,s.Et)(l)&&(0,s.Et)(a)&&(0,s.Et)(i)?n.createElement("path",N({},(0,D.J9)(d,!0),{className:(0,c.$)("recharts-cross",u),d:T(t,r,o,l,a,i)})):null};var C=r(4342),R=r(165);function L(e){var{cx:t,cy:r,radius:n,startAngle:a,endAngle:i}=e;return{points:[(0,R.IZ)(t,r,n,a),(0,R.IZ)(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:a,endAngle:i}}var K=r(677);function $(e,t,r){var n,a,i,o;if("horizontal"===e)i=n=t.x,a=r.top,o=r.top+r.height;else if("vertical"===e)o=a=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return L(t);var{cx:c,cy:s,innerRadius:l,outerRadius:u,angle:d}=t,f=(0,R.IZ)(c,s,l,d),p=(0,R.IZ)(c,s,u,d);n=f.x,a=f.y,i=p.x,o=p.y}return[{x:n,y:a},{x:i,y:o}]}var Z=r(3374),W=r(1428);function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},V.apply(null,arguments)}function G(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?G(Object(r),!0).forEach(function(t){U(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):G(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function U(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function J(e){var t,r,{coordinate:a,payload:i,index:o,offset:s,tooltipAxisBandSize:l,layout:u,cursor:d,tooltipEventType:f,chartName:p}=e,y=a,v=i,m=o;if(!d||!y||"ScatterChart"!==p&&"axis"!==f)return null;if("ScatterChart"===p)t=y,r=B;else if("BarChart"===p)t=function(e,t,r,n){var a=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===e?t.x-a:r.left+.5,y:"horizontal"===e?r.top+.5:t.y-a,width:"horizontal"===e?n:r.width-1,height:"horizontal"===e?r.height-1:n}}(u,y,s,l),r=C.M;else if("radial"===u){var{cx:h,cy:b,radius:g,startAngle:x,endAngle:O}=L(y);t={cx:h,cy:b,startAngle:x,endAngle:O,innerRadius:g,outerRadius:g},r=K.h}else t={points:$(u,y,s)},r=k.I;var w="object"===typeof d&&"className"in d?d.className:void 0,j=F(F(F(F({stroke:"#ccc",pointerEvents:"none"},s),t),(0,D.J9)(d,!1)),{},{payload:v,payloadIndex:m,className:(0,c.$)("recharts-tooltip-cursor",w)});return(0,n.isValidElement)(d)?(0,n.cloneElement)(d,j):(0,n.createElement)(r,j)}function X(e){var t=(0,Z.O)(),r=(0,P.hj)(),a=(0,P.WX)(),i=(0,W.fW)();return n.createElement(J,V({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:a,tooltipAxisBandSize:t,chartName:i}))}var H=r(317),Y=r(787),_=r(2768),Q=r(425),q=r(2277),ee=r(6371);function te(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function re(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?te(Object(r),!0).forEach(function(t){ne(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):te(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ne(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ae(e){return e.dataKey}var ie=[],oe={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!j.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function ce(e){var t=(0,ee.e)(e,oe),{active:r,allowEscapeViewBox:i,animationDuration:o,animationEasing:c,content:s,filterNull:l,isAnimationActive:u,offset:d,payloadUniqBy:f,position:p,reverseDirection:v,useTranslate3d:m,wrapperStyle:h,cursor:b,shared:g,trigger:x,defaultIndex:O,portal:j,axisId:k}=t,D=(0,Y.j)(),I="number"===typeof O?String(O):O;(0,n.useEffect)(()=>{D((0,_.UF)({shared:g,trigger:x,axisId:k,active:r,defaultIndex:I}))},[D,g,x,k,r,I]);var N=(0,P.sk)(),M=(0,A.$)(),z=(0,q.Td)(g),{activeIndex:T,isActive:B}=(0,Y.G)(e=>(0,W.yn)(e,z,x,I)),C=(0,Y.G)(e=>(0,W.u9)(e,z,x,I)),R=(0,Y.G)(e=>(0,W.BZ)(e,z,x,I)),L=(0,Y.G)(e=>(0,W.dS)(e,z,x,I)),K=C,$=(0,H.X)(),Z=null!==r&&void 0!==r?r:B,[V,G]=(0,S.V)([K,Z]),F="axis"===z?R:void 0;(0,Q.m7)(z,x,L,F,T,Z);var U=null!==j&&void 0!==j?j:$;if(null==U)return null;var J=null!==K&&void 0!==K?K:ie;Z||(J=ie),l&&J.length&&(J=(0,E.s)(K.filter(e=>null!=e.value&&(!0!==e.hide||t.includeHidden)),f,ae));var te=J.length>0,ne=n.createElement(w,{allowEscapeViewBox:i,animationDuration:o,animationEasing:c,isAnimationActive:u,active:Z,coordinate:L,hasPayload:te,offset:d,position:p,reverseDirection:v,useTranslate3d:m,viewBox:N,wrapperStyle:h,lastBoundingBox:V,innerRef:G,hasPortalFromProps:Boolean(j)},function(e,t){return n.isValidElement(e)?n.cloneElement(e,t):"function"===typeof e?n.createElement(e,t):n.createElement(y,t)}(s,re(re({},t),{},{payload:J,label:F,active:Z,coordinate:L,accessibilityLayer:M})));return n.createElement(n.Fragment,null,(0,a.createPortal)(ne,U),Z&&n.createElement(X,{cursor:b,tooltipEventType:z,coordinate:L,payload:K,index:T}))}},5715:(e,t,r)=>{"use strict";r.d(t,{y:()=>We,L:()=>Ze});var n=r(5043),a=r(8387),i=r(4020),o=r(8056),c=e=>null;c.displayName="Cell";var s=r(1519),l=r(6307),u=r(240),d=r(6015),f=r(4804),p=r(7287),y="Invariant failed";var v=r(9397),m=r.n(v),h=r(4342),b=r(6371),g=r(5654);function x(){return x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},x.apply(null,arguments)}var O=(e,t,r,n,a)=>{var i,o=r-n;return i="M ".concat(e,",").concat(t),i+="L ".concat(e+r,",").concat(t),i+="L ".concat(e+r-o/2,",").concat(t+a),i+="L ".concat(e+r-o/2-n,",").concat(t+a),i+="L ".concat(e,",").concat(t," Z")},w={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},j=e=>{var t=(0,b.e)(e,w),r=(0,n.useRef)(),[i,o]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&o(e)}catch(t){}},[]);var{x:c,y:s,upperWidth:l,lowerWidth:d,height:f,className:p}=t,{animationEasing:y,animationDuration:v,animationBegin:m,isUpdateAnimationActive:h}=t;if(c!==+c||s!==+s||l!==+l||d!==+d||f!==+f||0===l&&0===d||0===f)return null;var j=(0,a.$)("recharts-trapezoid",p);return h?n.createElement(g.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:f,x:c,y:s},to:{upperWidth:l,lowerWidth:d,height:f,x:c,y:s},duration:v,animationEasing:y,isActive:h},e=>{var{upperWidth:a,lowerWidth:o,height:c,x:s,y:l}=e;return n.createElement(g.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},n.createElement("path",x({},(0,u.J9)(t,!0),{className:j,d:O(s,l,a,o,c),ref:r})))}):n.createElement("g",null,n.createElement("path",x({},(0,u.J9)(t,!0),{className:j,d:O(c,s,l,d,f)})))},E=r(677),P=r(1985),A=["option","shapeType","propTransformer","activeClassName","isActive"];function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){D(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function D(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function I(e,t){return k(k({},t),e)}function N(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(h.M,r);case"trapezoid":return n.createElement(j,r);case"sector":return n.createElement(E.h,r);case"symbols":if(function(e){return"symbols"===e}(t))return n.createElement(P.i,r);break;default:return null}}function M(e){var t,{option:r,shapeType:a,propTransformer:o=I,activeClassName:c="recharts-active-shape",isActive:s}=e,l=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,A);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,k(k({},l),function(e){return(0,n.isValidElement)(e)?e.props:e}(r)));else if("function"===typeof r)t=r(l);else if(m()(r)&&"boolean"!==typeof r){var u=o(r,l);t=n.createElement(N,{shapeType:a,elementProps:u})}else{var d=l;t=n.createElement(N,{shapeType:a,elementProps:d})}return s?n.createElement(i.W,{className:c},t):t}var z=["x","y"];function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},T.apply(null,arguments)}function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function C(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){R(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function R(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function L(e,t){var{x:r,y:n}=e,a=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,z),i="".concat(r),o=parseInt(i,10),c="".concat(n),s=parseInt(c,10),l="".concat(t.height||a.height),u=parseInt(l,10),d="".concat(t.width||a.width),f=parseInt(d,10);return C(C(C(C(C({},t),a),o?{x:o}:{}),s?{y:s}:{}),{},{height:u,width:f,name:t.name,radius:t.radius})}function K(e){return n.createElement(M,T({shapeType:"rectangle",propTransformer:L,activeClassName:"recharts-active-bar"},e))}var $=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,l.Et)(e))return e;var a=(0,l.Et)(r)||(0,l.uy)(r);return a?e(r,n):(a||function(e){if(!e)throw new Error(y)}(!1),t)}},Z=r(787),W=r(2768),V=(e,t)=>{var r=(0,Z.j)();return(n,a)=>i=>{null===e||void 0===e||e(n,a,i),r((0,W.RD)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},G=e=>{var t=(0,Z.j)();return(r,n)=>a=>{null===e||void 0===e||e(r,n,a),t((0,W.oP)())}},F=(e,t)=>{var r=(0,Z.j)();return(n,a)=>i=>{null===e||void 0===e||e(n,a,i),r((0,W.ML)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},U=r(5175),J=r(9256),X=()=>{var e=(0,Z.j)();return(0,n.useEffect)(()=>(e((0,J.lm)()),()=>{e((0,J.Ch)())})),null},H=r(5621),Y=r(8327),_=r(8796),Q=r(2099),q=r(4954),ee=r(5340),te=r(4721),re=r(116),ne=r(2574);function ae(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ie(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(r),!0).forEach(function(t){oe(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ae(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function oe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ce=(e,t,r,n,a)=>a,se=(e,t,r)=>{var n=null!==r&&void 0!==r?r:e;if(!(0,l.uy)(n))return(0,l.F4)(n,t,0)},le=(0,Q.Mz)([_.fz,q.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,a)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===a).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function ue(e){return null!=e.stackId&&null!=e.dataKey}var de=(0,Q.Mz)([le,re.x3,(e,t,r)=>"horizontal"===(0,_.fz)(e)?(0,q.BQ)(e,"xAxis",t):(0,q.BQ)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(ue),a=e.filter(e=>null==e.stackId),i=n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{});return[...Object.entries(i).map(e=>{var[n,a]=e;return{stackId:n,dataKeys:a.map(e=>e.dataKey),barSize:se(t,r,a[0].barSize)}}),...a.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:se(t,r,e.barSize)}))]}),fe=(e,t,r,n)=>{var a,i;return"horizontal"===(0,_.fz)(e)?(a=(0,q.Gx)(e,"xAxis",t,n),i=(0,q.CR)(e,"xAxis",t,n)):(a=(0,q.Gx)(e,"yAxis",r,n),i=(0,q.CR)(e,"yAxis",r,n)),(0,f.Hj)(a,i)};var pe=(0,Q.Mz)([de,re.JN,re._5,re.gY,(e,t,r,n,a)=>{var i,o,c,s,u=(0,_.fz)(e),d=(0,re.JN)(e),{maxBarSize:p}=a,y=(0,l.uy)(p)?d:p;return"horizontal"===u?(c=(0,q.Gx)(e,"xAxis",t,n),s=(0,q.CR)(e,"xAxis",t,n)):(c=(0,q.Gx)(e,"yAxis",r,n),s=(0,q.CR)(e,"yAxis",r,n)),null!==(i=null!==(o=(0,f.Hj)(c,s,!0))&&void 0!==o?o:y)&&void 0!==i?i:0},fe,(e,t,r,n,a)=>a.maxBarSize],(e,t,r,n,a,i,o)=>{var c=(0,l.uy)(o)?t:o,s=function(e,t,r,n,a){var i=n.length;if(!(i<1)){var o,c=(0,l.F4)(e,r,0,!0),s=[];if((0,ne.H)(n[0].barSize)){var u=!1,d=r/i,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(i-1)*c)>=r&&(f-=(i-1)*c,c=0),f>=r&&d>0&&(u=!0,f=i*(d*=.9));var p={offset:((r-f)/2|0)-c,size:0};o=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p.offset+p.size+c,size:u?d:null!==(r=t.barSize)&&void 0!==r?r:0}}];return p=n[n.length-1].position,n},s)}else{var y=(0,l.F4)(t,r,0,!0);r-2*y-(i-1)*c<=0&&(c=0);var v=(r-2*y-(i-1)*c)/i;v>1&&(v>>=0);var m=(0,ne.H)(a)?Math.min(v,a):v;o=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:y+(v+c)*r+(v-m)/2,size:m}}],s)}return o}}(r,n,a!==i?a:i,e,c);return a!==i&&null!=s&&(s=s.map(e=>ie(ie({},e),{},{position:ie(ie({},e.position),{},{offset:e.position.offset-a/2})}))),s}),ye=(0,Q.Mz)([pe,ce],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),ve=(0,Q.Mz)([q.ld,ce],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),me=(0,Q.Mz)([(e,t,r,n)=>"horizontal"===(0,_.fz)(e)?(0,q.TC)(e,"yAxis",r,n):(0,q.TC)(e,"xAxis",t,n),ce],(e,t)=>{if(e&&null!=(null===t||void 0===t?void 0:t.dataKey)){var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:a}=n;if(a)return a.find(e=>e.key===t.dataKey)}}}}),he=(0,Q.Mz)([te.GO,(e,t,r,n)=>(0,q.Gx)(e,"xAxis",t,n),(e,t,r,n)=>(0,q.Gx)(e,"yAxis",r,n),(e,t,r,n)=>(0,q.CR)(e,"xAxis",t,n),(e,t,r,n)=>(0,q.CR)(e,"yAxis",r,n),ye,_.fz,ee.HS,fe,me,ve,(e,t,r,n,a,i)=>i],(e,t,r,n,a,i,o,c,s,l,u,d)=>{var{chartData:f,dataStartIndex:p,dataEndIndex:y}=c;if(null!=u&&null!=i&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=a&&null!=s){var v,{data:m}=u;if(null!=(v=null!=m&&m.length>0?m:null===f||void 0===f?void 0:f.slice(p,y+1)))return Ze({layout:o,barSettings:u,pos:i,bandSize:s,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:a,stackedData:l,displayedData:v,offset:e,cells:d})}}),be=r(3987),ge=r(2711),xe=r(6098),Oe=r(2854),we=["onMouseEnter","onMouseLeave","onClick"],je=["value","background","tooltipPosition"],Ee=["onMouseEnter","onClick","onMouseLeave"];function Pe(){return Pe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pe.apply(null,arguments)}function Ae(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Se(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ae(Object(r),!0).forEach(function(t){ke(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ae(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ke(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function De(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var Ie=e=>{var{dataKey:t,name:r,fill:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:t,type:a,color:n,value:(0,f.uM)(r,t),payload:e}]};function Ne(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:a,name:i,hide:o,unit:c}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:a,dataKey:t,nameKey:void 0,name:(0,f.uM)(i,t),hide:o,type:e.tooltipType,color:e.fill,unit:c}}}function Me(e){var t=(0,Z.G)(ge.A2),{data:r,dataKey:a,background:i,allOtherBarProps:o}=e,{onMouseEnter:c,onMouseLeave:s,onClick:l}=o,d=De(o,we),f=V(c,a),y=G(s),v=F(l,a);if(!i||null==r)return null;var m=(0,u.J9)(i,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:c,tooltipPosition:s}=e,l=De(e,je);if(!c)return null;var u=f(e,r),h=y(e,r),b=v(e,r),g=Se(Se(Se(Se(Se({option:i,isActive:String(r)===t},l),{},{fill:"#eee"},c),m),(0,p.XC)(d,e,r)),{},{onMouseEnter:u,onMouseLeave:h,onClick:b,dataKey:a,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(K,Pe({key:"background-bar-".concat(r)},g))}))}function ze(e){var{data:t,props:r,showLabels:a}=e,o=(0,u.J9)(r,!1),{shape:c,dataKey:l,activeBar:d}=r,f=(0,Z.G)(ge.A2),y=(0,Z.G)(ge.Xb),{onMouseEnter:v,onClick:m,onMouseLeave:h}=r,b=De(r,Ee),g=V(v,l),x=G(h),O=F(m,l);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=d&&String(t)===f&&(null==y||l===y),a=r?d:c,s=Se(Se(Se({},o),e),{},{isActive:r,option:a,index:t,dataKey:l});return n.createElement(i.W,Pe({className:"recharts-bar-rectangle"},(0,p.XC)(b,e,t),{onMouseEnter:g(e,t),onMouseLeave:x(e,t),onClick:O(e,t),key:"rectangle-".concat(null===e||void 0===e?void 0:e.x,"-").concat(null===e||void 0===e?void 0:e.y,"-").concat(null===e||void 0===e?void 0:e.value,"-").concat(t)}),n.createElement(K,s))}),a&&s.Z.renderCallByParent(r,t)):null}function Te(e){var{props:t,previousRectanglesRef:r}=e,{data:a,layout:o,isAnimationActive:c,animationBegin:s,animationDuration:u,animationEasing:d,onAnimationEnd:f,onAnimationStart:p}=t,y=r.current,v=(0,Oe.n)(t,"recharts-bar-"),[m,h]=(0,n.useState)(!1),b=(0,n.useCallback)(()=>{"function"===typeof f&&f(),h(!1)},[f]),x=(0,n.useCallback)(()=>{"function"===typeof p&&p(),h(!0)},[p]);return n.createElement(g.i,{begin:s,duration:u,isActive:c,easing:d,from:{t:0},to:{t:1},onAnimationEnd:b,onAnimationStart:x,key:v},e=>{var{t:c}=e,s=1===c?a:a.map((e,t)=>{var r=y&&y[t];if(r){var n=(0,l.Dj)(r.x,e.x),a=(0,l.Dj)(r.y,e.y),i=(0,l.Dj)(r.width,e.width),s=(0,l.Dj)(r.height,e.height);return Se(Se({},e),{},{x:n(c),y:a(c),width:i(c),height:s(c)})}if("horizontal"===o){var u=(0,l.Dj)(0,e.height)(c);return Se(Se({},e),{},{y:e.y+e.height-u,height:u})}var d=(0,l.Dj)(0,e.width)(c);return Se(Se({},e),{},{width:d})});return c>0&&(r.current=s),n.createElement(i.W,null,n.createElement(ze,{props:t,data:s,showLabels:!m}))})}function Be(e){var{data:t,isAnimationActive:r}=e,a=(0,n.useRef)(null);return r&&t&&t.length&&(null==a.current||a.current!==t)?n.createElement(Te,{previousRectanglesRef:a,props:e}):n.createElement(ze,{props:e,data:t,showLabels:!0})}var Ce=0,Re=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,f.kr)(e,t)}};class Le extends n.PureComponent{constructor(){super(...arguments),ke(this,"id",(0,l.NF)("recharts-bar-"))}render(){var{hide:e,data:t,dataKey:r,className:c,xAxisId:s,yAxisId:u,needClip:d,background:f,id:p,layout:y}=this.props;if(e)return null;var v=(0,a.$)("recharts-bar",c),m=(0,l.uy)(p)?this.id:p;return n.createElement(i.W,{className:v},d&&n.createElement("defs",null,n.createElement(Y.Q,{clipPathId:m,xAxisId:s,yAxisId:u})),n.createElement(i.W,{className:"recharts-bar-rectangles",clipPath:d?"url(#clipPath-".concat(m,")"):null},n.createElement(Me,{data:t,dataKey:r,background:f,allOtherBarProps:this.props}),n.createElement(Be,this.props)),n.createElement(o._,{direction:"horizontal"===y?"y":"x"},this.props.children))}}var Ke={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!d.m.isSsr,legendType:"rect",minPointSize:Ce,xAxisId:0,yAxisId:0};function $e(e){var t,{xAxisId:r,yAxisId:a,hide:i,legendType:o,minPointSize:s,activeBar:l,animationBegin:d,animationDuration:p,animationEasing:y,isAnimationActive:v}=(0,b.e)(e,Ke),{needClip:m}=(0,Y.l)(r,a),h=(0,_.WX)(),g=(0,be.r)(),x=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:s,stackId:(0,f.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,s,e.stackId]),O=(0,u.aS)(e.children,c),w=(0,Z.G)(e=>he(e,r,a,g,x,O));if("vertical"!==h&&"horizontal"!==h)return null;var j=null===w||void 0===w?void 0:w[0];return t=null==j||null==j.height||null==j.width?0:"vertical"===h?j.height/2:j.width/2,n.createElement(H.zk,{xAxisId:r,yAxisId:a,data:w,dataPointFormatter:Re,errorBarOffset:t},n.createElement(Le,Pe({},e,{layout:h,needClip:m,data:w,xAxisId:r,yAxisId:a,hide:i,legendType:o,minPointSize:s,activeBar:l,animationBegin:d,animationDuration:p,animationEasing:y,isAnimationActive:v})))}function Ze(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:a,bandSize:i,xAxis:o,yAxis:c,xAxisTicks:s,yAxisTicks:u,stackedData:d,displayedData:p,offset:y,cells:v}=e,m="horizontal"===t?c:o,h=d?m.scale.domain():null,b=(0,f.DW)({numericAxis:m});return p.map((e,p)=>{var m,g,x,O,w,j;d?m=(0,f._f)(d[p],h):(m=(0,f.kr)(e,r),Array.isArray(m)||(m=[b,m]));var E=$(n,Ce)(m[1],p);if("horizontal"===t){var P,[A,S]=[c.scale(m[0]),c.scale(m[1])];g=(0,f.y2)({axis:o,ticks:s,bandSize:i,offset:a.offset,entry:e,index:p}),x=null!==(P=null!==S&&void 0!==S?S:A)&&void 0!==P?P:void 0,O=a.size;var k=A-S;if(w=(0,l.M8)(k)?0:k,j={x:g,y:y.top,width:O,height:y.height},Math.abs(E)>0&&Math.abs(w)<Math.abs(E)){var D=(0,l.sA)(w||E)*(Math.abs(E)-Math.abs(w));x-=D,w+=D}}else{var[I,N]=[o.scale(m[0]),o.scale(m[1])];if(g=I,x=(0,f.y2)({axis:c,ticks:u,bandSize:i,offset:a.offset,entry:e,index:p}),O=N-I,w=a.size,j={x:y.left,y:x,width:y.width,height:w},Math.abs(E)>0&&Math.abs(O)<Math.abs(E))O+=(0,l.sA)(O||E)*(Math.abs(E)-Math.abs(O))}return Se(Se({},e),{},{x:g,y:x,width:O,height:w,value:d?m:m[1],payload:e,background:j,tooltipPosition:{x:g+O/2,y:x+w/2}},v&&v[p]&&v[p].props)})}class We extends n.PureComponent{render(){return n.createElement(H._S,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(X,null),n.createElement(xe.A,{legendPayload:Ie(this.props)}),n.createElement(U.r,{fn:Ne,args:this.props}),n.createElement($e,this.props))}}ke(We,"displayName","Bar"),ke(We,"defaultProps",Ke)},9397:(e,t,r)=>{e.exports=r(2682).isPlainObject}}]);
//# sourceMappingURL=574.efadddd5.chunk.js.map