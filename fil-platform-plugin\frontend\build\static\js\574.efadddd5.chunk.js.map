{"version": 3, "file": "static/js/574.efadddd5.chunk.js", "mappings": "iLAAA,SAASA,IAAa,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAAIF,UAAUD,GAAI,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAAK,CAAE,OAAOL,CAAG,EAAGJ,EAASY,MAAM,KAAMN,UAAY,CAOnR,IAKIO,EAAmBC,IACrB,IAAI,GACFC,EAAE,GACFC,EAAE,OACFC,EAAM,MACNC,EAAK,KACLC,EAAI,WACJC,EAAU,aACVC,EAAY,iBACZC,GACER,EACAS,EAAeF,GAAgBD,EAAa,GAAK,GAAKH,EACtDO,EAAQC,KAAKC,KAAKL,EAAeE,GAAgBI,EAAAA,GACjDC,EAAcN,EAAmBJ,EAAQA,EAAQC,EAAOK,EAKxDK,EAAoBP,EAAmBJ,EAAQC,EAAOK,EAAQN,EAElE,MAAO,CACLY,QAPWC,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIO,EAAcK,GAQlDI,gBANmBD,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIC,EAAQW,GAOpDK,cAJiBF,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIO,EAAeE,KAAKS,IAAIV,EAAQG,EAAAA,IAASE,GAKnFL,UAGAW,EAAgBC,IAClB,IAAI,GACFrB,EAAE,GACFC,EAAE,YACFqB,EAAW,YACXC,EAAW,WACXC,EAAU,SACVC,GACEJ,EACAlB,EAzCcuB,EAACF,EAAYC,KACpBE,EAAAA,EAAAA,IAASF,EAAWD,GACdd,KAAKkB,IAAIlB,KAAKmB,IAAIJ,EAAWD,GAAa,SAuC/CE,CAAcF,EAAYC,GAGlCK,EAAeN,EAAarB,EAC5B4B,GAAkBf,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIsB,EAAaC,GACxDQ,GAAgBhB,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIsB,EAAaO,GACtDG,EAAO,KAAKC,OAAOH,EAAgBI,EAAG,KAAKD,OAAOH,EAAgBK,EAAG,YAAYF,OAAOX,EAAa,KAAKW,OAAOX,EAAa,aAAaW,SAASxB,KAAKmB,IAAI1B,GAAS,KAAM,KAAK+B,SAASV,EAAaM,GAAe,WAAWI,OAAOF,EAAcG,EAAG,KAAKD,OAAOF,EAAcI,EAAG,QAC1R,GAAId,EAAc,EAAG,CACnB,IAAIe,GAAkBrB,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIqB,EAAaE,GACxDc,GAAgBtB,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIqB,EAAaQ,GAC1DG,GAAQ,KAAKC,OAAOI,EAAcH,EAAG,KAAKD,OAAOI,EAAcF,EAAG,oBAAoBF,OAAOZ,EAAa,KAAKY,OAAOZ,EAAa,qBAAqBY,SAASxB,KAAKmB,IAAI1B,GAAS,KAAM,KAAK+B,SAASV,GAAcM,GAAe,mBAAmBI,OAAOG,EAAgBF,EAAG,KAAKD,OAAOG,EAAgBD,EAAG,KAClT,MACEH,GAAQ,KAAKC,OAAOlC,EAAI,KAAKkC,OAAOjC,EAAI,MAE1C,OAAOgC,GA+FLM,EAAe,CACjBvC,GAAI,EACJC,GAAI,EACJqB,YAAa,EACbC,YAAa,EACbC,WAAY,EACZC,SAAU,EACVnB,aAAc,EACdkC,mBAAmB,EACnBjC,kBAAkB,GAETkC,EAASC,IAClB,IAAIC,GAAQC,EAAAA,EAAAA,GAAoBF,EAAaH,IACzC,GACFvC,EAAE,GACFC,EAAE,YACFqB,EAAW,YACXC,EAAW,aACXjB,EAAY,kBACZkC,EAAiB,iBACjBjC,EAAgB,WAChBiB,EAAU,SACVC,EAAQ,UACRoB,GACEF,EACJ,GAAIpB,EAAcD,GAAeE,IAAeC,EAC9C,OAAO,KAET,IAGIQ,EAHAa,GAAaC,EAAAA,EAAAA,GAAK,kBAAmBF,GACrCG,EAAczB,EAAcD,EAC5B2B,GAAKC,EAAAA,EAAAA,IAAgB5C,EAAc0C,EAAa,GAAG,GAwBvD,OArBEf,EADEgB,EAAK,GAAKvC,KAAKmB,IAAIL,EAAaC,GAAY,IA7HxB0B,KACxB,IAAI,GACFnD,EAAE,GACFC,EAAE,YACFqB,EAAW,YACXC,EAAW,aACXjB,EAAY,kBACZkC,EAAiB,iBACjBjC,EAAgB,WAChBiB,EAAU,SACVC,GACE0B,EACA/C,GAAOuB,EAAAA,EAAAA,IAASF,EAAWD,IAE7BP,eAAgBmC,EAChBlC,aAAcmC,EACd5C,MAAO6C,GACLxD,EAAiB,CACnBE,KACAC,KACAC,OAAQqB,EACRpB,MAAOqB,EACPpB,OACAE,eACAC,sBAGAU,eAAgBsC,EAChBrC,aAAcsC,EACd/C,MAAOgD,GACL3D,EAAiB,CACnBE,KACAC,KACAC,OAAQqB,EACRpB,MAAOsB,EACPrB,MAAOA,EACPE,eACAC,qBAEEmD,EAAgBnD,EAAmBG,KAAKmB,IAAIL,EAAaC,GAAYf,KAAKmB,IAAIL,EAAaC,GAAY6B,EAAMG,EACjH,GAAIC,EAAgB,EAClB,OAAIlB,EACK,KAAKN,OAAOmB,EAAKlB,EAAG,KAAKD,OAAOmB,EAAKjB,EAAG,eAAeF,OAAO5B,EAAc,KAAK4B,OAAO5B,EAAc,WAAW4B,OAAsB,EAAf5B,EAAkB,iBAAiB4B,OAAO5B,EAAc,KAAK4B,OAAO5B,EAAc,WAAW4B,OAAuB,GAAf5B,EAAkB,cAEjPc,EAAc,CACnBpB,KACAC,KACAqB,cACAC,cACAC,aACAC,aAGJ,IAAIQ,EAAO,KAAKC,OAAOmB,EAAKlB,EAAG,KAAKD,OAAOmB,EAAKjB,EAAG,WAAWF,OAAO5B,EAAc,KAAK4B,OAAO5B,EAAc,SAAS4B,SAAS9B,EAAO,GAAI,KAAK8B,OAAOkB,EAAKjB,EAAG,KAAKD,OAAOkB,EAAKhB,EAAG,WAAWF,OAAOX,EAAa,KAAKW,OAAOX,EAAa,OAAOW,SAASwB,EAAgB,KAAM,KAAKxB,SAAS9B,EAAO,GAAI,KAAK8B,OAAOqB,EAAKpB,EAAG,KAAKD,OAAOqB,EAAKnB,EAAG,WAAWF,OAAO5B,EAAc,KAAK4B,OAAO5B,EAAc,SAAS4B,SAAS9B,EAAO,GAAI,KAAK8B,OAAOsB,EAAKrB,EAAG,KAAKD,OAAOsB,EAAKpB,EAAG,QAChd,GAAId,EAAc,EAAG,CACnB,IACEL,eAAgB0C,EAChBzC,aAAc0C,EACdnD,MAAOoD,GACL/D,EAAiB,CACnBE,KACAC,KACAC,OAAQoB,EACRnB,MAAOqB,EACPpB,OACAC,YAAY,EACZC,eACAC,sBAGAU,eAAgB6C,EAChB5C,aAAc6C,EACdtD,MAAOuD,GACLlE,EAAiB,CACnBE,KACAC,KACAC,OAAQoB,EACRnB,MAAOsB,EACPrB,MAAOA,EACPC,YAAY,EACZC,eACAC,qBAEE0D,EAAgB1D,EAAmBG,KAAKmB,IAAIL,EAAaC,GAAYf,KAAKmB,IAAIL,EAAaC,GAAYoC,EAAMG,EACjH,GAAIC,EAAgB,GAAsB,IAAjB3D,EACvB,MAAO,GAAG4B,OAAOD,EAAM,KAAKC,OAAOlC,EAAI,KAAKkC,OAAOjC,EAAI,KAEzDgC,GAAQ,IAAIC,OAAO6B,EAAK5B,EAAG,KAAKD,OAAO6B,EAAK3B,EAAG,aAAaF,OAAO5B,EAAc,KAAK4B,OAAO5B,EAAc,SAAS4B,SAAS9B,EAAO,GAAI,KAAK8B,OAAO4B,EAAK3B,EAAG,KAAKD,OAAO4B,EAAK1B,EAAG,aAAaF,OAAOZ,EAAa,KAAKY,OAAOZ,EAAa,OAAOY,SAAS+B,EAAgB,KAAM,KAAK/B,SAAS9B,EAAO,GAAI,KAAK8B,OAAOyB,EAAKxB,EAAG,KAAKD,OAAOyB,EAAKvB,EAAG,aAAaF,OAAO5B,EAAc,KAAK4B,OAAO5B,EAAc,SAAS4B,SAAS9B,EAAO,GAAI,KAAK8B,OAAO0B,EAAKzB,EAAG,KAAKD,OAAO0B,EAAKxB,EAAG,IACpd,MACEH,GAAQ,IAAIC,OAAOlC,EAAI,KAAKkC,OAAOjC,EAAI,KAEzC,OAAOgC,GAmCEiC,CAAoB,CACzBlE,KACAC,KACAqB,cACAC,cACAjB,aAAcI,KAAKkB,IAAIqB,EAAID,EAAc,GACzCR,oBACAjC,mBACAiB,aACAC,aAGKL,EAAc,CACnBpB,KACAC,KACAqB,cACAC,cACAC,aACAC,aAGgB0C,EAAAA,cAAoB,OAAQlF,EAAS,CAAC,GAAGmF,EAAAA,EAAAA,IAAYzB,GAAO,GAAO,CACrFE,UAAWC,EACXuB,EAAGpC,K,iIC9MP,MAAMqC,GAAgBC,EAAAA,EAAAA,GAAiB,MACvCD,EAAcE,YAAc,gBAC5B,MAAMC,EAA4BN,EAAAA,WAAiB,CAAApE,EAKhD2E,KAAQ,IALyC,UAClD7B,EAAS,SACT8B,EACAC,GAAIC,EAAYP,KACb3B,GACJ5C,EAEC,OADA4E,GAAWG,EAAAA,EAAAA,IAAmBH,EAAU,kBACpBI,EAAAA,EAAAA,KAAKF,EAAW,CAClCH,IAAKA,EACL7B,UAAWmC,IAAWnC,EAAW8B,MAC9BhC,MAGP8B,EAAaD,YAAc,eAC3B,U,cChBA,MAAMS,EAAyBd,EAAAA,WAAiB,CAAApE,EAK7C2E,KAAQ,IALsC,UAC/C7B,EAAS,SACT8B,EACAC,GAAIC,EAAYK,EAAAA,KACbvC,GACJ5C,EAEC,OADA4E,GAAWG,EAAAA,EAAAA,IAAmBH,EAAU,eACpBI,EAAAA,EAAAA,KAAKF,EAAW,CAClCH,IAAKA,EACL7B,UAAWmC,IAAWnC,EAAW8B,MAC9BhC,MAGPsC,EAAUT,YAAc,YACxB,U,wBCRA,MAAMW,EAAqBhB,EAAAA,WAAiB,CAACiB,EAAmBV,KAC9D,MAAM,SACJC,EAAQ,KACRU,GAAO,EAAI,WACXC,EAAa,cAAa,aAC1BC,EAAY,UACZ1C,EAAS,SACT2C,EAAQ,QACRC,EAAU,UAAS,QACnBC,EAAO,YACPC,EAAW,WACXC,EAAaC,EAAAA,KACVlD,IACDmD,EAAAA,EAAAA,IAAgBV,EAAmB,CACrCC,KAAM,YAEFU,GAASjB,EAAAA,EAAAA,IAAmBH,EAAU,SACtCqB,GAAcC,EAAAA,EAAAA,GAAiB3G,IAC/BoG,GACFA,GAAQ,EAAOpG,KAGb4G,GAA4B,IAAfN,EAAsBC,EAAAA,EAAOD,EAC1CO,GAAqBC,EAAAA,EAAAA,MAAM,MAAO,CACtCC,KAAM,WACDH,OAAqBI,EAAR3D,EAClB+B,IAAKA,EACL7B,UAAWmC,IAAWnC,EAAWkD,EAAQN,GAAW,GAAGM,KAAUN,IAAWE,GAAe,GAAGI,iBAC9FP,SAAU,CAACG,IAA4BZ,EAAAA,EAAAA,KAAKwB,EAAAA,EAAa,CACvDC,QAASR,EACT,aAAcV,EACdG,QAASF,IACPC,KAEN,OAAKU,GACenB,EAAAA,EAAAA,KAAKmB,EAAY,CACnCO,eAAe,KACZ9D,EACH+B,SAAK4B,EACLI,GAAIrB,EACJG,SAAUW,IANYd,EAAOc,EAAQ,OASzChB,EAAMX,YAAc,QACpB,QAAetF,OAAOC,OAAOgG,EAAO,CAClCwB,KAAM1B,EACN2B,QAASnC,G,iFCvDPoC,EAAsB,CAAC,OAAQ,QACxBC,GAAwBC,EAAAA,EAAAA,YAAW,CAACpE,EAAO+B,IAChCP,EAAAA,cAAoB6C,EAAAA,EAAgB,CACtDC,UAAW,WACXC,wBAAyB,OACzBC,0BAA2BN,EAC3BO,uBAAwBC,EAAAA,GACxBC,sBAAuB3E,EACvB+B,IAAKA,I,4BCVTxF,OAAOqI,eAAeC,EAASC,OAAOC,YAAa,CAAEC,MAAO,WA8B5DH,EAAQI,cA5BR,SAAuBC,GACnB,GAAsB,kBAAXA,EACP,OAAO,EAEX,GAAc,MAAVA,EACA,OAAO,EAEX,GAAsC,OAAlC3I,OAAO4I,eAAeD,GACtB,OAAO,EAEX,GAA+C,oBAA3C3I,OAAO6I,UAAUC,SAASpI,KAAKiI,GAA+B,CAC9D,MAAMI,EAAMJ,EAAOJ,OAAOC,aAC1B,GAAW,MAAPO,EACA,OAAO,EAGX,QADuB/I,OAAOgJ,yBAAyBL,EAAQJ,OAAOC,cAAcS,UAI7EN,EAAOG,aAAe,WAAWC,IAC5C,CACA,IAAIG,EAAQP,EACZ,KAAwC,OAAjC3I,OAAO4I,eAAeM,IACzBA,EAAQlJ,OAAO4I,eAAeM,GAElC,OAAOlJ,OAAO4I,eAAeD,KAAYO,CAC7C,C,mGCxBA,MAAMC,EAAqBlE,EAAAA,WAAiB,CAAApE,EAWzC2E,KAAQ,IAXkC,SAC3CC,EAAQ,UACR9B,EAAS,QACTyF,EAAO,SACPC,EAAQ,WACRC,EAAU,MACVC,EAAK,KACLC,EAAI,QACJjD,EAAO,WACPkD,KACGhG,GACJ5C,EACC,MAAM6I,GAAoB9D,EAAAA,EAAAA,IAAmBH,EAAU,SACjDkE,EAAU7D,IAAWnC,EAAW+F,EAAmBnD,GAAW,GAAGmD,KAAqBnD,IAAWiD,GAAQ,GAAGE,KAAqBF,IAAQJ,GAAW,GAAGM,KAAwC,kBAAZN,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGK,aAA8BJ,GAAc,GAAGI,eAAgCH,GAAS,GAAGG,WACxVE,GAAqB/D,EAAAA,EAAAA,KAAK,QAAS,IACpCpC,EACHE,UAAWgG,EACXnE,IAAKA,IAEP,GAAIiE,EAAY,CACd,IAAII,EAAkB,GAAGH,eAIzB,MAH0B,kBAAfD,IACTI,EAAkB,GAAGA,KAAmBJ,MAEtB5D,EAAAA,EAAAA,KAAK,MAAO,CAC9BlC,UAAWkG,EACXvD,SAAUsD,GAEd,CACA,OAAOA,IAETT,EAAM7D,YAAc,QACpB,S,oGCtCA,SAASvF,IAAa,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAAIF,UAAUD,GAAI,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAAK,CAAE,OAAOL,CAAG,EAAGJ,EAASY,MAAM,KAAMN,UAAY,CAUnR,IAAIyJ,EAAmBA,CAAC7G,EAAGC,EAAG6G,EAAOC,EAAQhJ,KAC3C,IAII+B,EAJAkH,EAAYzI,KAAKkB,IAAIlB,KAAKmB,IAAIoH,GAAS,EAAGvI,KAAKmB,IAAIqH,GAAU,GAC7DE,EAAQF,GAAU,EAAI,GAAK,EAC3BG,EAAQJ,GAAS,EAAI,GAAK,EAC1BK,EAAYJ,GAAU,GAAKD,GAAS,GAAKC,EAAS,GAAKD,EAAQ,EAAI,EAAI,EAE3E,GAAIE,EAAY,GAAKjJ,aAAkBqJ,MAAO,CAE5C,IADA,IAAIC,EAAY,CAAC,EAAG,EAAG,EAAG,GACjBC,EAAI,EAAYA,EAAH,EAAYA,IAChCD,EAAUC,GAAKvJ,EAAOuJ,GAAKN,EAAYA,EAAYjJ,EAAOuJ,GAE5DxH,EAAO,IAAIC,OAAOC,EAAG,KAAKD,OAAOE,EAAIgH,EAAQI,EAAU,IACnDA,EAAU,GAAK,IACjBvH,GAAQ,KAAKC,OAAOsH,EAAU,GAAI,KAAKtH,OAAOsH,EAAU,GAAI,SAAStH,OAAOoH,EAAW,KAAKpH,OAAOC,EAAIkH,EAAQG,EAAU,GAAI,KAAKtH,OAAOE,IAE3IH,GAAQ,KAAKC,OAAOC,EAAI8G,EAAQI,EAAQG,EAAU,GAAI,KAAKtH,OAAOE,GAC9DoH,EAAU,GAAK,IACjBvH,GAAQ,KAAKC,OAAOsH,EAAU,GAAI,KAAKtH,OAAOsH,EAAU,GAAI,SAAStH,OAAOoH,EAAW,eAAepH,OAAOC,EAAI8G,EAAO,KAAK/G,OAAOE,EAAIgH,EAAQI,EAAU,KAE5JvH,GAAQ,KAAKC,OAAOC,EAAI8G,EAAO,KAAK/G,OAAOE,EAAI8G,EAASE,EAAQI,EAAU,IACtEA,EAAU,GAAK,IACjBvH,GAAQ,KAAKC,OAAOsH,EAAU,GAAI,KAAKtH,OAAOsH,EAAU,GAAI,SAAStH,OAAOoH,EAAW,eAAepH,OAAOC,EAAI8G,EAAQI,EAAQG,EAAU,GAAI,KAAKtH,OAAOE,EAAI8G,IAEjKjH,GAAQ,KAAKC,OAAOC,EAAIkH,EAAQG,EAAU,GAAI,KAAKtH,OAAOE,EAAI8G,GAC1DM,EAAU,GAAK,IACjBvH,GAAQ,KAAKC,OAAOsH,EAAU,GAAI,KAAKtH,OAAOsH,EAAU,GAAI,SAAStH,OAAOoH,EAAW,eAAepH,OAAOC,EAAG,KAAKD,OAAOE,EAAI8G,EAASE,EAAQI,EAAU,KAE7JvH,GAAQ,GACV,MAAO,GAAIkH,EAAY,GAAKjJ,KAAYA,GAAUA,EAAS,EAAG,CAC5D,IAAIwJ,EAAahJ,KAAKkB,IAAIuH,EAAWjJ,GACrC+B,EAAO,KAAKC,OAAOC,EAAG,KAAKD,OAAOE,EAAIgH,EAAQM,EAAY,oBAAoBxH,OAAOwH,EAAY,KAAKxH,OAAOwH,EAAY,SAASxH,OAAOoH,EAAW,KAAKpH,OAAOC,EAAIkH,EAAQK,EAAY,KAAKxH,OAAOE,EAAG,oBAAoBF,OAAOC,EAAI8G,EAAQI,EAAQK,EAAY,KAAKxH,OAAOE,EAAG,oBAAoBF,OAAOwH,EAAY,KAAKxH,OAAOwH,EAAY,SAASxH,OAAOoH,EAAW,KAAKpH,OAAOC,EAAI8G,EAAO,KAAK/G,OAAOE,EAAIgH,EAAQM,EAAY,oBAAoBxH,OAAOC,EAAI8G,EAAO,KAAK/G,OAAOE,EAAI8G,EAASE,EAAQM,EAAY,oBAAoBxH,OAAOwH,EAAY,KAAKxH,OAAOwH,EAAY,SAASxH,OAAOoH,EAAW,KAAKpH,OAAOC,EAAI8G,EAAQI,EAAQK,EAAY,KAAKxH,OAAOE,EAAI8G,EAAQ,oBAAoBhH,OAAOC,EAAIkH,EAAQK,EAAY,KAAKxH,OAAOE,EAAI8G,EAAQ,oBAAoBhH,OAAOwH,EAAY,KAAKxH,OAAOwH,EAAY,SAASxH,OAAOoH,EAAW,KAAKpH,OAAOC,EAAG,KAAKD,OAAOE,EAAI8G,EAASE,EAAQM,EAAY,KAC13B,MACEzH,EAAO,KAAKC,OAAOC,EAAG,KAAKD,OAAOE,EAAG,OAAOF,OAAO+G,EAAO,OAAO/G,OAAOgH,EAAQ,OAAOhH,QAAQ+G,EAAO,MAExG,OAAOhH,GAELM,EAAe,CACjBJ,EAAG,EACHC,EAAG,EACH6G,MAAO,EACPC,OAAQ,EAIRhJ,OAAQ,EACRyJ,mBAAmB,EACnBC,yBAAyB,EACzBC,eAAgB,EAChBC,kBAAmB,KACnBC,gBAAiB,QAERC,EAAYC,IACrB,IAAItH,GAAQC,EAAAA,EAAAA,GAAoBqH,EAAgB1H,GAC5C2H,GAAUC,EAAAA,EAAAA,QAAO,OAChBC,EAAaC,IAAkBC,EAAAA,EAAAA,WAAU,IAC9CC,EAAAA,EAAAA,WAAU,KACR,GAAIL,EAAQM,SAAWN,EAAQM,QAAQC,eACrC,IACE,IAAIC,EAAkBR,EAAQM,QAAQC,iBAClCC,GACFL,EAAeK,EAEnB,CAAE,MAAOC,GACP,GAGH,IACH,IAAI,EACFxI,EAAC,EACDC,EAAC,MACD6G,EAAK,OACLC,EAAM,OACNhJ,EAAM,UACN2C,GACEF,GACA,gBACFoH,EAAe,kBACfD,EAAiB,eACjBD,EAAc,kBACdF,EAAiB,wBACjBC,GACEjH,EACJ,GAAIR,KAAOA,GAAKC,KAAOA,GAAK6G,KAAWA,GAASC,KAAYA,GAAoB,IAAVD,GAA0B,IAAXC,EACnF,OAAO,KAET,IAAIpG,GAAaC,EAAAA,EAAAA,GAAK,qBAAsBF,GAC5C,OAAK+G,EAMezF,EAAAA,cAAoByG,EAAAA,EAAS,CAC/CC,SAAUT,EAAc,EACxBU,KAAM,CACJ7B,QACAC,SACA/G,IACAC,KAEF2I,GAAI,CACF9B,QACAC,SACA/G,IACAC,KAEF4I,SAAUlB,EAGVC,gBAAiBA,EACjBkB,SAAUrB,GACT7J,IACD,IACEkJ,MAAOiC,EACPhC,OAAQiC,EACRhJ,EAAGiJ,EACHhJ,EAAGiJ,GACDtL,EACJ,OAAoBoE,EAAAA,cAAoByG,EAAAA,EAAS,CAC/CC,SAAUT,EAAc,EAGxBU,KAAM,OAAO5I,QAAwB,IAAjBkI,EAAqB,EAAIA,EAAa,MAG1DW,GAAI,GAAG7I,OAAOkI,EAAa,UAC3BkB,cAAe,kBACfC,MAAO1B,EACPmB,SAAUlB,EACVmB,SAAUtB,EACV6B,OAAQzB,GACM5F,EAAAA,cAAoB,OAAQlF,EAAS,CAAC,GAAGmF,EAAAA,EAAAA,IAAYzB,GAAO,GAAO,CACjFE,UAAWC,EACXuB,EAAG2E,EAAiBoC,EAAOC,EAAOH,EAAWC,EAAYjL,GACzDwE,IAAKwF,QA/Ca/F,EAAAA,cAAoB,OAAQlF,EAAS,CAAC,GAAGmF,EAAAA,EAAAA,IAAYzB,GAAO,GAAO,CACrFE,UAAWC,EACXuB,EAAG2E,EAAiB7G,EAAGC,EAAG6G,EAAOC,EAAQhJ,M,+GCnG/C,SAASjB,IAAa,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAAIF,UAAUD,GAAI,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAAK,CAAE,OAAOL,CAAG,EAAGJ,EAASY,MAAM,KAAMN,UAAY,CACnR,SAASkM,EAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAC9P,SAASuM,EAAc1M,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,EAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,EAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,EAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CACtb,SAAS4M,EAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CAWnL,SAASsN,EAAiBjF,GACxB,OAAO4B,MAAMsD,QAAQlF,KAAUmF,EAAAA,EAAAA,IAAWnF,EAAM,MAAOmF,EAAAA,EAAAA,IAAWnF,EAAM,IAAMA,EAAMoF,KAAK,OAASpF,CACpG,CACO,IAAIqF,EAAwBrK,IACjC,IAAI,UACFsK,EAAY,MAAK,aACjBC,EAAe,CAAC,EAAC,UACjBC,EAAY,CAAC,EAAC,WACdC,EAAa,CAAC,EAAC,QACfC,EAAO,UACPC,EAAS,WACTC,EAAU,iBACVC,EAAgB,eAChBC,EAAc,MACdC,EAAK,eACLC,EAAc,mBACdC,GAAqB,GACnBjL,EA2DAkL,EAAa7B,EAAc,CAC7B8B,OAAQ,EACRC,QAAS,GACTC,gBAAiB,OACjBC,OAAQ,iBACRC,WAAY,UACXhB,GACCiB,EAAkBnC,EAAc,CAClC8B,OAAQ,GACPV,GACCgB,IAAYC,EAAAA,EAAAA,IAAUX,GACtBY,EAAaF,EAAWV,EAAQ,GAChCa,GAAYxL,EAAAA,EAAAA,GAAK,2BAA4ByK,GAC7CgB,GAAUzL,EAAAA,EAAAA,GAAK,yBAA0B0K,GACzCW,GAAYT,QAA8BrH,IAAZ+G,GAAqC,OAAZA,IACzDiB,EAAaX,EAAeD,EAAOL,IAErC,IAAIoB,EAA0Bb,EAAqB,CACjDvH,KAAM,SACN,YAAa,aACX,CAAC,EACL,OAAoBlC,EAAAA,cAAoB,MAAOlF,EAAS,CACtD4D,UAAW0L,EACXG,MAAOb,GACNY,GAAuCtK,EAAAA,cAAoB,IAAK,CACjEtB,UAAW2L,EACXE,MAAOP,GACOhK,EAAAA,eAAqBmK,GAAcA,EAAa,GAAGpM,OAAOoM,IArFtDK,MAClB,GAAItB,GAAWA,EAAQ7N,OAAQ,CAC7B,IAIIoP,GAASrB,EAAasB,IAAOxB,EAASE,GAAcF,GAASyB,IAAI,CAACC,EAAOtF,KAC3E,GAAmB,SAAfsF,EAAMC,KACR,OAAO,KAET,IAAIC,EAAiBF,EAAMzB,WAAaA,GAAaV,GACjD,MACFjF,EAAK,KACLuH,GACEH,EACAI,EAAaxH,EACbyH,EAAYF,EAChB,GAAID,EAAgB,CAClB,IAAII,EAAYJ,EAAetH,EAAOuH,EAAMH,EAAOtF,EAAG4D,GACtD,GAAI9D,MAAMsD,QAAQwC,IACfF,EAAYC,GAAaC,MACrB,IAAiB,MAAbA,EAGT,OAAO,KAFPF,EAAaE,CAGf,CACF,CACA,IAAIC,EAAiBtD,EAAc,CACjCuD,QAAS,QACTC,WAAY,EACZC,cAAe,EACfC,MAAOX,EAAMW,OAAS,QACrBvC,GACH,OAGEhJ,EAAAA,cAAoB,KAAM,CACxBtB,UAAW,wBACX8M,IAAK,gBAAgBzN,OAAOuH,GAC5BiF,MAAOY,IACNxC,EAAAA,EAAAA,IAAWsC,GAA0BjL,EAAAA,cAAoB,OAAQ,CAClEtB,UAAW,8BACVuM,GAAa,MAAMtC,EAAAA,EAAAA,IAAWsC,GAA0BjL,EAAAA,cAAoB,OAAQ,CACrFtB,UAAW,mCACVoK,GAAa,KAAmB9I,EAAAA,cAAoB,OAAQ,CAC7DtB,UAAW,+BACVsM,GAA0BhL,EAAAA,cAAoB,OAAQ,CACvDtB,UAAW,8BACVkM,EAAMa,MAAQ,OAGrB,OAAoBzL,EAAAA,cAAoB,KAAM,CAC5CtB,UAAW,6BACX6L,MAnDc,CACdX,QAAS,EACTD,OAAQ,IAkDPc,EACL,CACA,OAAO,MA6B+ED,KCnHtFkB,EAAmB,2BACnBC,EAAiB,CACnBC,WAAY,UAEP,SAASC,EAAuBjQ,GACrC,IAAI,WACFkQ,EAAU,WACVC,EAAU,WACVC,GACEpQ,EACJ,OAAOgD,EAAAA,EAAAA,GAAK8M,EAAkB,CAC5B,CAAC,GAAG3N,OAAO2N,EAAkB,YAAYO,EAAAA,EAAAA,IAASF,IAAeD,IAAcG,EAAAA,EAAAA,IAASH,EAAW9N,IAAM+N,GAAcD,EAAW9N,EAClI,CAAC,GAAGD,OAAO2N,EAAkB,WAAWO,EAAAA,EAAAA,IAASF,IAAeD,IAAcG,EAAAA,EAAAA,IAASH,EAAW9N,IAAM+N,EAAaD,EAAW9N,EAChI,CAAC,GAAGD,OAAO2N,EAAkB,aAAaO,EAAAA,EAAAA,IAASD,IAAeF,IAAcG,EAAAA,EAAAA,IAASH,EAAW7N,IAAM+N,GAAcF,EAAW7N,EACnI,CAAC,GAAGF,OAAO2N,EAAkB,UAAUO,EAAAA,EAAAA,IAASD,IAAeF,IAAcG,EAAAA,EAAAA,IAASH,EAAW7N,IAAM+N,EAAaF,EAAW7N,GAEnI,CACO,SAASiO,EAAsBhP,GACpC,IAAI,mBACFiP,EAAkB,WAClBL,EAAU,IACVN,EAAG,cACHY,EAAa,SACbC,EAAQ,iBACRC,EAAgB,iBAChBC,EAAgB,QAChBC,EAAO,iBACPC,GACEvP,EACJ,GAAImP,IAAYJ,EAAAA,EAAAA,IAASI,EAASb,IAChC,OAAOa,EAASb,GAElB,IAAIkB,EAAWZ,EAAWN,GAAOe,GAAoBH,EAAgB,EAAIA,EAAgB,GACrFO,EAAWb,EAAWN,GAAOY,EACjC,GAAID,EAAmBX,GACrB,OAAOc,EAAiBd,GAAOkB,EAAWC,EAE5C,IAAIC,EAAaJ,EAAQhB,GACzB,OAAkB,MAAdoB,EACK,EAELN,EAAiBd,GACIkB,EACAE,EAEdrQ,KAAKsQ,IAAIF,EAAUC,GAErBrQ,KAAKsQ,IAAIH,EAAUE,GAEJ,MAApBH,EACK,EAEaE,EAAWJ,EACXK,EAAaH,EAE1BlQ,KAAKsQ,IAAIH,EAAUE,GAErBrQ,KAAKsQ,IAAIF,EAAUC,EAC5B,CC5DA,SAAStF,EAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAC9P,SAASuM,EAAc1M,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,EAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,EAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,EAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CACtb,SAAS4M,EAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CAM5K,MAAM2R,UAA2BC,EAAAA,cACtCC,WAAAA,GACEC,SAAS7R,WACT2M,EAAgBmF,KAAM,QAAS,CAC7BC,WAAW,EACXC,sBAAuB,CACrBpP,EAAG,EACHC,EAAG,KAGP8J,EAAgBmF,KAAM,gBAAiBG,IAEnC,IAAIC,EAAuBC,EAAwBC,EAAwBC,EAD3D,WAAdJ,EAAM7B,KAER0B,KAAKQ,SAAS,CACZP,WAAW,EACXC,sBAAuB,CACrBpP,EAAoK,QAAhKsP,EAA6E,QAApDC,EAAyBL,KAAK1O,MAAMsN,kBAAmD,IAA3ByB,OAAoC,EAASA,EAAuBvP,SAAyC,IAA1BsP,EAAmCA,EAAwB,EACvOrP,EAAqK,QAAjKuP,EAA8E,QAApDC,EAAyBP,KAAK1O,MAAMsN,kBAAmD,IAA3B2B,OAAoC,EAASA,EAAuBxP,SAA0C,IAA3BuP,EAAoCA,EAAyB,MAKpP,CACAG,iBAAAA,GACEC,SAASC,iBAAiB,UAAWX,KAAKY,cAC5C,CACAC,oBAAAA,GACEH,SAASI,oBAAoB,UAAWd,KAAKY,cAC/C,CACAG,kBAAAA,GACE,IAAIC,EAAwBC,EACvBjB,KAAKkB,MAAMjB,aAG0C,QAApDe,EAAyBhB,KAAK1O,MAAMsN,kBAAmD,IAA3BoC,OAAoC,EAASA,EAAuBlQ,KAAOkP,KAAKkB,MAAMhB,sBAAsBpP,IAA2D,QAApDmQ,EAAyBjB,KAAK1O,MAAMsN,kBAAmD,IAA3BqC,OAAoC,EAASA,EAAuBlQ,KAAOiP,KAAKkB,MAAMhB,sBAAsBnP,IAC3ViP,KAAKkB,MAAMjB,WAAY,GAE3B,CACAkB,MAAAA,GACE,IAAI,OACFC,EAAM,mBACNnC,EAAkB,kBAClBxG,EAAiB,gBACjBC,EAAe,SACfvE,EAAQ,WACRyK,EAAU,WACVyC,EAAU,kBACV/I,EAAiB,OACjBgJ,EAAM,SACNnC,EAAQ,iBACRC,EAAgB,eAChBmC,EAAc,QACdjC,EAAO,aACPkC,EAAY,gBACZC,EAAe,SACfC,EAAQ,mBACRC,GACE3B,KAAK1O,OACL,WACFsQ,EAAU,cACVC,GDGC,SAA6BC,GAClC,IAUID,EAAehD,EAAYC,GAV3B,mBACFG,EAAkB,WAClBL,EAAU,cACVM,EAAa,SACbC,EAAQ,iBACRC,EAAgB,WAChB2C,EAAU,eACVR,EAAc,QACdjC,GACEwC,EAiCJ,OARED,EAvBEE,EAAWlK,OAAS,GAAKkK,EAAWnK,MAAQ,GAAKgH,EAtBhD,SAA2B9M,GAChC,IAAI,WACF+M,EAAU,WACVC,EAAU,eACVyC,GACEzP,EACJ,MAAO,CACLkQ,UAAWT,EAAiB,eAAe1Q,OAAOgO,EAAY,QAAQhO,OAAOiO,EAAY,UAAY,aAAajO,OAAOgO,EAAY,QAAQhO,OAAOiO,EAAY,OAEpK,CAoCoBmD,CAAkB,CAChCpD,WAvBFA,EAAaG,EAAsB,CACjCC,qBACAL,aACAN,IAAK,IACLY,gBACAC,WACAC,mBACAC,iBAAkB0C,EAAWnK,MAC7B0H,UACAC,iBAAkBD,EAAQ1H,QAe1BkH,WAbFA,EAAaE,EAAsB,CACjCC,qBACAL,aACAN,IAAK,IACLY,gBACAC,WACAC,mBACAC,iBAAkB0C,EAAWlK,OAC7ByH,UACAC,iBAAkBD,EAAQzH,SAK1B0J,mBAGc9C,EAEX,CACLoD,gBACAD,WAAYjD,EAAuB,CACjCE,aACAC,aACAF,eAGN,CCrDQsD,CAAoB,CACtBjD,qBACAL,aACAM,cAAeoC,EACfnC,WACAC,mBACA2C,WAAY,CACVlK,OAAQ4J,EAAgB5J,OACxBD,MAAO6J,EAAgB7J,OAEzB2J,iBACAjC,YAIE6C,EAAiBR,EAAqB,CAAC,EAAIhH,EAAcA,EAAc,CACzEpG,WAAY+D,GAAqB8I,EAAS,aAAavQ,OAAO4H,EAAmB,OAAO5H,OAAO6H,QAAmBzD,GACjH4M,GAAgB,CAAC,EAAG,CACrBO,cAAe,OACf1D,YAAasB,KAAKkB,MAAMjB,WAAamB,GAAUC,EAAa,UAAY,SACxElC,SAAU,WACVkD,IAAK,EACLC,KAAM,IAEJC,EAAa5H,EAAcA,EAAc,CAAC,EAAGwH,GAAiB,CAAC,EAAG,CACpEzD,YAAasB,KAAKkB,MAAMjB,WAAamB,GAAUC,EAAa,UAAY,UACvEG,GACH,OAGE1O,EAAAA,cAAoB,MAAO,CAEzB0P,MAAO,+BACPC,UAAW,EACXjR,UAAWoQ,EACXvE,MAAOkF,EACPlP,IAAKqO,GACJvN,EAEP,E,wEC5GEuO,EAAY,CAAC,IAAK,IAAK,MAAO,OAAQ,QAAS,SAAU,aAC7D,SAAS9U,IAAa,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAAIF,UAAUD,GAAI,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAAK,CAAE,OAAOL,CAAG,EAAGJ,EAASY,MAAM,KAAMN,UAAY,CACnR,SAASkM,EAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAE9P,SAASyM,EAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CAYnL,IAAI0U,EAAUA,CAAC7R,EAAGC,EAAG6G,EAAOC,EAAQwK,EAAKC,IAChC,IAAIzR,OAAOC,EAAG,KAAKD,OAAOwR,EAAK,KAAKxR,OAAOgH,EAAQ,KAAKhH,OAAOyR,EAAM,KAAKzR,OAAOE,EAAG,KAAKF,OAAO+G,GAE9FgL,EAAQlU,IACjB,IAAI,EACAoC,EAAI,EAAC,EACLC,EAAI,EAAC,IACLsR,EAAM,EAAC,KACPC,EAAO,EAAC,MACR1K,EAAQ,EAAC,OACTC,EAAS,EAAC,UACVrG,GACE9C,EAEF4C,EA3BN,SAAuBrD,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,EAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,EAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,EAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CA2Bxa0M,CAAc,CACxB7J,IACAC,IACAsR,MACAC,OACA1K,QACAC,UA7BJ,SAAkC5J,EAAGG,GAAK,GAAI,MAAQH,EAAG,MAAO,CAAC,EAAG,IAAIsM,EAAGlM,EAAG+J,EAC9E,SAAuC/J,EAAGJ,GAAK,GAAI,MAAQI,EAAG,MAAO,CAAC,EAAG,IAAID,EAAI,CAAC,EAAG,IAAK,IAAIJ,KAAKK,EAAG,GAAI,CAAC,EAAEC,eAAeC,KAAKF,EAAGL,GAAI,CAAE,IAAK,IAAMC,EAAE4U,QAAQ7U,GAAI,SAAUI,EAAEJ,GAAKK,EAAEL,EAAI,CAAE,OAAOI,CAAG,CADpH0U,CAA8B7U,EAAGG,GAAI,GAAIP,OAAOyM,sBAAuB,CAAE,IAAItM,EAAIH,OAAOyM,sBAAsBrM,GAAI,IAAKI,EAAI,EAAGA,EAAIL,EAAEG,OAAQE,IAAKkM,EAAIvM,EAAEK,IAAK,IAAMD,EAAEyU,QAAQtI,IAAM,CAAC,EAAEwI,qBAAqBxU,KAAKN,EAAGsM,KAAOnC,EAAEmC,GAAKtM,EAAEsM,GAAK,CAAE,OAAOnC,CAAG,CAsB1T4K,CAAyBtU,EAAMgU,IASxC,OAAK3D,EAAAA,EAAAA,IAASjO,KAAOiO,EAAAA,EAAAA,IAAShO,KAAOgO,EAAAA,EAAAA,IAASnH,KAAWmH,EAAAA,EAAAA,IAASlH,KAAYkH,EAAAA,EAAAA,IAASsD,KAAStD,EAAAA,EAAAA,IAASuD,GAGrFxP,EAAAA,cAAoB,OAAQlF,EAAS,CAAC,GAAGmF,EAAAA,EAAAA,IAAYzB,GAAO,GAAO,CACrFE,WAAWE,EAAAA,EAAAA,GAAK,iBAAkBF,GAClCwB,EAAG2P,EAAQ7R,EAAGC,EAAG6G,EAAOC,EAAQwK,EAAKC,MAJ9B,M,uBCjCJ,SAASW,EAAsBC,GACpC,IAAI,GACFvU,EAAE,GACFC,EAAE,OACFC,EAAM,WACNsB,EAAU,SACVC,GACE8S,EAGJ,MAAO,CACLC,OAAQ,EAHOxT,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIC,EAAQsB,IACnCR,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIC,EAAQuB,IAG9CzB,KACAC,KACAC,SACAsB,aACAC,WAEJ,C,aCtBO,SAASgT,EAAgBC,EAAQH,EAAkB5B,GACxD,IAAIgC,EAAIC,EAAIC,EAAIC,EAChB,GAAe,eAAXJ,EAEFG,EADAF,EAAKJ,EAAiBpS,EAEtByS,EAAKjC,EAAOe,IACZoB,EAAKnC,EAAOe,IAAMf,EAAOzJ,YACpB,GAAe,aAAXwL,EAETI,EADAF,EAAKL,EAAiBnS,EAEtBuS,EAAKhC,EAAOgB,KACZkB,EAAKlC,EAAOgB,KAAOhB,EAAO1J,WACrB,GAA2B,MAAvBsL,EAAiBvU,IAAqC,MAAvBuU,EAAiBtU,GAAY,CACrE,GAAe,YAAXyU,EAgBF,OAAOJ,EAAsBC,GAf7B,IAAI,GACFvU,EAAE,GACFC,EAAE,YACFqB,EAAW,YACXC,EAAW,MACXpB,GACEoU,EACAQ,GAAa/T,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIqB,EAAanB,GACnD6U,GAAahU,EAAAA,EAAAA,IAAiBhB,EAAIC,EAAIsB,EAAapB,GACvDwU,EAAKI,EAAW5S,EAChByS,EAAKG,EAAW3S,EAChByS,EAAKG,EAAW7S,EAChB2S,EAAKE,EAAW5S,CAKpB,CACA,MAAO,CAAC,CACND,EAAGwS,EACHvS,EAAGwS,GACF,CACDzS,EAAG0S,EACHzS,EAAG0S,GAEP,C,wBCzCA,SAAS7V,IAAa,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAAIF,UAAUD,GAAI,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAAK,CAAE,OAAOL,CAAG,EAAGJ,EAASY,MAAM,KAAMN,UAAY,CACnR,SAASkM,EAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAC9P,SAASuM,EAAc1M,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,EAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,EAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,EAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CACtb,SAAS4M,EAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CAwB5K,SAAS2V,EAAetS,GAC7B,IAmBIuS,EAAWC,GAnBX,WACFlF,EAAU,QACV5C,EAAO,MACP+H,EAAK,OACLzC,EAAM,oBACN0C,EAAmB,OACnBX,EAAM,OACNY,EAAM,iBACNC,EAAgB,UAChBtO,GACEtE,EAGA4R,EAAmBtE,EACnBuF,EAAgBnI,EAChBoI,EAAqBL,EACzB,IAAKE,IAAWf,GAAkC,iBAAdtN,GAAqD,SAArBsO,EAClE,OAAO,KAGT,GAAkB,iBAAdtO,EACFiO,EAAYX,EACZY,EAAalB,OACR,GAAkB,aAAdhN,EACTiO,ECpDG,SAA4BR,EAAQH,EAAkB5B,EAAQ0C,GACnE,IAAIK,EAAWL,EAAsB,EACrC,MAAO,CACLM,OAAQ,OACRC,KAAM,OACNzT,EAAc,eAAXuS,EAA0BH,EAAiBpS,EAAIuT,EAAW/C,EAAOgB,KAAO,GAC3EvR,EAAc,eAAXsS,EAA0B/B,EAAOe,IAAM,GAAMa,EAAiBnS,EAAIsT,EACrEzM,MAAkB,eAAXyL,EAA0BW,EAAsB1C,EAAO1J,MAAQ,EACtEC,OAAmB,eAAXwL,EAA0B/B,EAAOzJ,OAAS,EAAImM,EAE1D,CD0CgBQ,CAAmBnB,EAAQH,EAAkB5B,EAAQ0C,GACjEF,EAAanL,EAAAA,OACR,GAAe,WAAX0K,EAAqB,CAE9B,IAAI,GACF1U,EAAE,GACFC,EAAE,OACFC,EAAM,WACNsB,EAAU,SACVC,GACE6S,EAAsBC,GAC1BW,EAAY,CACVlV,KACAC,KACAuB,aACAC,WACAH,YAAapB,EACbqB,YAAarB,GAEfiV,EAAa1S,EAAAA,CACf,MACEyS,EAAY,CACVV,OAAQC,EAAgBC,EAAQH,EAAkB5B,IAEpDwC,EAAaW,EAAAA,EAEf,IAAIC,EAAmC,kBAAXT,GAAuB,cAAeA,EAASA,EAAOzS,eAAYyD,EAC1F0P,EAAchK,EAAcA,EAAcA,EAAcA,EAAc,CACxE2J,OAAQ,OACRlC,cAAe,QACdd,GAASuC,IAAY9Q,EAAAA,EAAAA,IAAYkR,GAAQ,IAAS,CAAC,EAAG,CACvDjI,QAASmI,EACTS,aAAcR,EACd5S,WAAWE,EAAAA,EAAAA,GAAK,0BAA2BgT,KAE7C,OAAoBG,EAAAA,EAAAA,gBAAeZ,IAAuBa,EAAAA,EAAAA,cAAab,EAAQU,IAA4BI,EAAAA,EAAAA,eAAcjB,EAAYa,EACvI,CAUO,SAASK,EAAO1T,GACrB,IAAI0S,GAAsBiB,EAAAA,EAAAA,KACtB3D,GAAS4D,EAAAA,EAAAA,MACT7B,GAAS8B,EAAAA,EAAAA,MACTvP,GAAYwP,EAAAA,EAAAA,MAChB,OAAoBtS,EAAAA,cAAoB8Q,EAAgBhW,EAAS,CAAC,EAAG0D,EAAO,CAC1EsN,WAAYtN,EAAMsN,WAClBmF,MAAOzS,EAAMyS,MACb/H,QAAS1K,EAAM0K,QACfsF,OAAQA,EACR+B,OAAQA,EACRW,oBAAqBA,EACrBpO,UAAWA,IAEf,C,8DEhHA,SAASwE,GAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAC9P,SAASuM,GAAc1M,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,GAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,GAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,GAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CACtb,SAAS4M,GAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CAqBnL,SAASoX,GAAc3H,GACrB,OAAOA,EAAM4H,OACf,CAUA,IAAIC,GAAe,GACfC,GAAsB,CACxBvG,mBAAoB,CAClBnO,GAAG,EACHC,GAAG,GAEL0H,kBAAmB,IACnBC,gBAAiB,OACjB+M,OAAQ,EACR5J,aAAc,CAAC,EACfoI,QAAQ,EACRyB,YAAY,EACZpN,mBAAoBqN,EAAAA,EAAOC,MAC3B1J,WAAY,OACZJ,UAAW,CAAC,EACZC,WAAY,CAAC,EACbuF,OAAQ,GACRlC,iBAAkB,CAChBtO,GAAG,EACHC,GAAG,GAEL6K,UAAW,MACXiK,QAAS,QACTtE,gBAAgB,EAChBC,aAAc,CAAC,GAEV,SAASsE,GAAQC,GACtB,IAAIzU,GAAQC,EAAAA,GAAAA,GAAoBwU,EAAcP,KAE5CpE,OAAQ4E,EAAe,mBACvB/G,EAAkB,kBAClBxG,EAAiB,gBACjBC,EAAe,QACfuN,EAAO,WACPP,EAAU,kBACVpN,EAAiB,OACjBgJ,EAAM,cACN4E,EAAa,SACb/G,EAAQ,iBACRC,EAAgB,eAChBmC,EAAc,aACdC,EAAY,OACZyC,EAAM,OACNkC,EAAM,QACNN,EAAO,aACPO,EACAC,OAAQC,EAAe,OACvBb,GACEnU,EACAiV,GAAWC,EAAAA,EAAAA,KACXC,EAA+C,kBAAjBL,EAA4BlL,OAAOkL,GAAgBA,GACrFlN,EAAAA,EAAAA,WAAU,KACRqN,GAASG,EAAAA,EAAAA,IAAwB,CAC/BP,SACAN,UACAJ,SACArE,OAAQ4E,EACRI,aAAcK,MAEf,CAACF,EAAUJ,EAAQN,EAASJ,EAAQO,EAAiBS,IACxD,IAAInH,GAAUqH,EAAAA,EAAAA,MACVpK,GAAqBqK,EAAAA,EAAAA,KACrB1C,GAAmB2C,EAAAA,EAAAA,IAAoBV,IACvC,YACFW,EAAW,SACXlN,IACEmN,EAAAA,EAAAA,GAAe7F,IAAS8F,EAAAA,EAAAA,IAAsB9F,EAAOgD,EAAkB2B,EAASY,IAChFQ,GAAmBF,EAAAA,EAAAA,GAAe7F,IAASgG,EAAAA,EAAAA,IAAqBhG,EAAOgD,EAAkB2B,EAASY,IAClGU,GAAiBJ,EAAAA,EAAAA,GAAe7F,IAASkG,EAAAA,EAAAA,IAAkBlG,EAAOgD,EAAkB2B,EAASY,IAC7F7H,GAAamI,EAAAA,EAAAA,GAAe7F,IAASmG,EAAAA,EAAAA,IAAuBnG,EAAOgD,EAAkB2B,EAASY,IAC9FzK,EAAUiL,EACVK,GAA2BC,EAAAA,EAAAA,KAO3BC,EAAoC,OAApBxB,QAAgD,IAApBA,EAA6BA,EAAkBpM,GAC1F6H,EAAiBgG,IAAqBC,EAAAA,EAAAA,GAAiB,CAAC1L,EAASwL,IAClEvK,EAAkC,SAArBiH,EAA8BiD,OAAiBlS,GAChE0S,EAAAA,EAAAA,IAA+BzD,EAAkB2B,EAASjH,EAAY3B,EAAY6J,EAAaU,GAC/F,IAAII,EAAoC,OAApBtB,QAAgD,IAApBA,EAA6BA,EAAkBgB,EAC/F,GAAqB,MAAjBM,EACF,OAAO,KAET,IAAIC,EAA2B,OAAZ7L,QAAgC,IAAZA,EAAqBA,EAAUuJ,GACjEiC,IACHK,EAAetC,IAEbG,GAAcmC,EAAa1Z,SAC7B0Z,GAAeC,EAAAA,EAAAA,GAAe9L,EAAQxB,OAAOkD,GAAwB,MAAfA,EAAMpH,SAAiC,IAAfoH,EAAMqK,MAAiBzW,EAAM0W,gBAAiB9B,EAAeb,KAE7I,IAAIhE,GAAawG,EAAa1Z,OAAS,EACnC8Z,GAA8BnV,EAAAA,cAAoB8M,EAAoB,CACxEX,mBAAoBA,EACpBxG,kBAAmBA,EACnBC,gBAAiBA,EACjBJ,kBAAmBA,EACnB8I,OAAQoG,EACR5I,WAAYA,EACZyC,WAAYA,GACZC,OAAQA,EACRnC,SAAUA,EACVC,iBAAkBA,EAClBmC,eAAgBA,EAChBjC,QAASA,EACTkC,aAAcA,EACdC,gBAAiBA,EACjBC,SAAU+F,EACV9F,mBAAoBuG,QAAQ5B,IAvHhC,SAAuBL,EAAS3U,GAC9B,OAAiBwB,EAAAA,eAAqBmT,GAChBnT,EAAAA,aAAmBmT,EAAS3U,GAE3B,oBAAZ2U,EACWnT,EAAAA,cAAoBmT,EAAS3U,GAE/BwB,EAAAA,cAAoB6I,EAAuBrK,EACjE,CAgHKgM,CAAc2I,EAAStL,GAAcA,GAAc,CAAC,EAAGrJ,GAAQ,CAAC,EAAG,CAEpE0K,QAAS6L,EACTxL,MAAOY,EACPmE,OAAQoG,EACR5I,aACArC,yBAEF,OAAoBzJ,EAAAA,cAAoBA,EAAAA,SAAgB,MAAmBqV,EAAAA,EAAAA,cAAaF,GAAgBL,GAAgBJ,GAA8B1U,EAAAA,cAAoBkS,EAAQ,CAChLf,OAAQA,EACRC,iBAAkBA,EAClBtF,WAAYA,EACZ5C,QAASA,EACT+H,MAAO+C,IAEX,C,qGC7JWsB,EAAOC,GAAU,KAC5BD,EAAKjV,YAAc,O,+DCJfuB,EAAS,mB,qDCDb,SAAS9G,IAAa,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAAIF,UAAUD,GAAI,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAAK,CAAE,OAAOL,CAAG,EAAGJ,EAASY,MAAM,KAAMN,UAAY,CAUnR,IAAIoa,EAAmBA,CAACxX,EAAGC,EAAGwX,EAAYC,EAAY3Q,KACpD,IACIjH,EADA6X,EAAWF,EAAaC,EAO5B,OALA5X,EAAO,KAAKC,OAAOC,EAAG,KAAKD,OAAOE,GAClCH,GAAQ,KAAKC,OAAOC,EAAIyX,EAAY,KAAK1X,OAAOE,GAChDH,GAAQ,KAAKC,OAAOC,EAAIyX,EAAaE,EAAW,EAAG,KAAK5X,OAAOE,EAAI8G,GACnEjH,GAAQ,KAAKC,OAAOC,EAAIyX,EAAaE,EAAW,EAAID,EAAY,KAAK3X,OAAOE,EAAI8G,GAChFjH,GAAQ,KAAKC,OAAOC,EAAG,KAAKD,OAAOE,EAAG,OAGpCG,EAAe,CACjBJ,EAAG,EACHC,EAAG,EACHwX,WAAY,EACZC,WAAY,EACZ3Q,OAAQ,EACRU,yBAAyB,EACzBC,eAAgB,EAChBC,kBAAmB,KACnBC,gBAAiB,QAERgQ,EAAYpX,IACrB,IAAIqX,GAAiBpX,EAAAA,EAAAA,GAAoBD,EAAOJ,GAC5C2H,GAAUC,EAAAA,EAAAA,WACTC,EAAaC,IAAkBC,EAAAA,EAAAA,WAAU,IAC9CC,EAAAA,EAAAA,WAAU,KACR,GAAIL,EAAQM,SAAWN,EAAQM,QAAQC,eACrC,IACE,IAAIC,EAAkBR,EAAQM,QAAQC,iBAClCC,GACFL,EAAeK,EAEnB,CAAE,MAAOC,GACP,GAGH,IACH,IAAI,EACFxI,EAAC,EACDC,EAAC,WACDwX,EAAU,WACVC,EAAU,OACV3Q,EAAM,UACNrG,GACEmX,GACA,gBACFjQ,EAAe,kBACfD,EAAiB,eACjBD,EAAc,wBACdD,GACEoQ,EACJ,GAAI7X,KAAOA,GAAKC,KAAOA,GAAKwX,KAAgBA,GAAcC,KAAgBA,GAAc3Q,KAAYA,GAAyB,IAAf0Q,GAAmC,IAAfC,GAA+B,IAAX3Q,EACpJ,OAAO,KAET,IAAIpG,GAAaC,EAAAA,EAAAA,GAAK,qBAAsBF,GAC5C,OAAK+G,EAMezF,EAAAA,cAAoByG,EAAAA,EAAS,CAC/CC,SAAUT,EAAc,EACxBU,KAAM,CACJ8O,WAAY,EACZC,WAAY,EACZ3Q,SACA/G,IACAC,KAEF2I,GAAI,CACF6O,aACAC,aACA3Q,SACA/G,IACAC,KAEF4I,SAAUlB,EAGVC,gBAAiBA,EACjBkB,SAAUrB,GACT7J,IACD,IACE6Z,WAAYK,EACZJ,WAAYK,EACZhR,OAAQiC,EACRhJ,EAAGiJ,EACHhJ,EAAGiJ,GACDtL,EACJ,OAAoBoE,EAAAA,cAAoByG,EAAAA,EAAS,CAC/CC,SAAUT,EAAc,EAGxBU,KAAM,OAAO5I,QAAwB,IAAjBkI,EAAqB,EAAIA,EAAa,MAG1DW,GAAI,GAAG7I,OAAOkI,EAAa,UAC3BkB,cAAe,kBACfC,MAAO1B,EACPmB,SAAUlB,EACV0B,OAAQzB,GACM5F,EAAAA,cAAoB,OAAQlF,EAAS,CAAC,GAAGmF,EAAAA,EAAAA,IAAY4V,GAAgB,GAAO,CAC1FnX,UAAWC,EACXuB,EAAGsV,EAAiBvO,EAAOC,EAAO4O,EAAgBC,EAAgB/O,GAClEzG,IAAKwF,QAjDa/F,EAAAA,cAAoB,IAAK,KAAmBA,EAAAA,cAAoB,OAAQlF,EAAS,CAAC,GAAGmF,EAAAA,EAAAA,IAAY4V,GAAgB,GAAO,CAC1InX,UAAWC,EACXuB,EAAGsV,EAAiBxX,EAAGC,EAAGwX,EAAYC,EAAY3Q,Q,mBCpEpD6K,EAAY,CAAC,SAAU,YAAa,kBAAmB,kBAAmB,YAG9E,SAAStI,EAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAC9P,SAASuM,EAAc1M,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,EAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,EAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,EAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CACtb,SAAS4M,EAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CA0BnL,SAAS6a,EAAuBC,EAAQzX,GACtC,OAAOqJ,EAAcA,EAAc,CAAC,EAAGrJ,GAAQyX,EACjD,CAIA,SAASC,EAActa,GACrB,IAAI,UACFua,EAAS,aACTC,GACExa,EACJ,OAAQua,GACN,IAAK,YACH,OAAoBnW,EAAAA,cAAoB6F,EAAAA,EAAWuQ,GACrD,IAAK,YACH,OAAoBpW,EAAAA,cAAoB4V,EAAWQ,GACrD,IAAK,SACH,OAAoBpW,EAAAA,cAAoB1B,EAAAA,EAAQ8X,GAClD,IAAK,UACH,GAhBN,SAAwBD,GACtB,MAAqB,YAAdA,CACT,CAcUE,CAAeF,GACjB,OAAoBnW,EAAAA,cAAoBsW,EAAAA,EAASF,GAEnD,MACF,QACE,OAAO,KAEb,CAOO,SAASG,EAAMrZ,GACpB,IAQIsZ,GARA,OACAP,EAAM,UACNE,EAAS,gBACTM,EAAkBT,EAAsB,gBACxCU,EAAkB,wBAAuB,SACzC5P,GACE5J,EACJsB,EAvEJ,SAAkCrD,EAAGG,GAAK,GAAI,MAAQH,EAAG,MAAO,CAAC,EAAG,IAAIsM,EAAGlM,EAAG+J,EAC9E,SAAuC/J,EAAGJ,GAAK,GAAI,MAAQI,EAAG,MAAO,CAAC,EAAG,IAAID,EAAI,CAAC,EAAG,IAAK,IAAIJ,KAAKK,EAAG,GAAI,CAAC,EAAEC,eAAeC,KAAKF,EAAGL,GAAI,CAAE,IAAK,IAAMC,EAAE4U,QAAQ7U,GAAI,SAAUI,EAAEJ,GAAKK,EAAEL,EAAI,CAAE,OAAOI,CAAG,CADpH0U,CAA8B7U,EAAGG,GAAI,GAAIP,OAAOyM,sBAAuB,CAAE,IAAItM,EAAIH,OAAOyM,sBAAsBrM,GAAI,IAAKI,EAAI,EAAGA,EAAIL,EAAEG,OAAQE,IAAKkM,EAAIvM,EAAEK,IAAK,IAAMD,EAAEyU,QAAQtI,IAAM,CAAC,EAAEwI,qBAAqBxU,KAAKN,EAAGsM,KAAOnC,EAAEmC,GAAKtM,EAAEsM,GAAK,CAAE,OAAOnC,CAAG,CAuEzT4K,CAAyBhT,EAAO0S,GAE1C,IAAiBmC,EAAAA,EAAAA,gBAAekE,GAC9BO,GAAqBxE,EAAAA,EAAAA,cAAaiE,EAAQpO,EAAcA,EAAc,CAAC,EAAGrJ,GAjBvE,SAAiCyX,GACtC,OAAiBlE,EAAAA,EAAAA,gBAAekE,GACvBA,EAAOzX,MAETyX,CACT,CAYsFU,CAAwBV,UACrG,GAAsB,oBAAXA,EAChBO,EAAQP,EAAOzX,QACV,GAAIiF,IAAcwS,IAA6B,mBAAXA,EAAsB,CAC/D,IAAIW,EAAYH,EAAgBR,EAAQzX,GACxCgY,EAAqBxW,EAAAA,cAAoBkW,EAAe,CACtDC,UAAWA,EACXC,aAAcQ,GAElB,KAAO,CACL,IAAIR,EAAe5X,EACnBgY,EAAqBxW,EAAAA,cAAoBkW,EAAe,CACtDC,UAAWA,EACXC,aAAcA,GAElB,CACA,OAAItP,EACkB9G,EAAAA,cAAoB6W,EAAAA,EAAO,CAC7CnY,UAAWgY,GACVF,GAEEA,CACT,CCjGA,IAAI5G,EAAY,CAAC,IAAK,KACtB,SAAS9U,IAAa,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAAIF,UAAUD,GAAI,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAAK,CAAE,OAAOL,CAAG,EAAGJ,EAASY,MAAM,KAAMN,UAAY,CACnR,SAASkM,EAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAC9P,SAASuM,EAAc1M,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,EAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,EAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,EAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CACtb,SAAS4M,EAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CAenL,SAAS2b,EAA2Blb,EAAM4C,GACxC,IACIR,EAAG+Y,EACH9Y,EAAG+Y,GACDpb,EACJqa,EAjBJ,SAAkC9a,EAAGG,GAAK,GAAI,MAAQH,EAAG,MAAO,CAAC,EAAG,IAAIsM,EAAGlM,EAAG+J,EAC9E,SAAuC/J,EAAGJ,GAAK,GAAI,MAAQI,EAAG,MAAO,CAAC,EAAG,IAAID,EAAI,CAAC,EAAG,IAAK,IAAIJ,KAAKK,EAAG,GAAI,CAAC,EAAEC,eAAeC,KAAKF,EAAGL,GAAI,CAAE,IAAK,IAAMC,EAAE4U,QAAQ7U,GAAI,SAAUI,EAAEJ,GAAKK,EAAEL,EAAI,CAAE,OAAOI,CAAG,CADpH0U,CAA8B7U,EAAGG,GAAI,GAAIP,OAAOyM,sBAAuB,CAAE,IAAItM,EAAIH,OAAOyM,sBAAsBrM,GAAI,IAAKI,EAAI,EAAGA,EAAIL,EAAEG,OAAQE,IAAKkM,EAAIvM,EAAEK,IAAK,IAAMD,EAAEyU,QAAQtI,IAAM,CAAC,EAAEwI,qBAAqBxU,KAAKN,EAAGsM,KAAOnC,EAAEmC,GAAKtM,EAAEsM,GAAK,CAAE,OAAOnC,CAAG,CAiBxT4K,CAAyBtU,EAAMgU,GACtCqH,EAAS,GAAGlZ,OAAOgZ,GACnB/Y,EAAIkZ,SAASD,EAAQ,IACrBE,EAAS,GAAGpZ,OAAOiZ,GACnB/Y,EAAIiZ,SAASC,EAAQ,IACrBC,EAAc,GAAGrZ,OAAOS,EAAMuG,QAAUkR,EAAOlR,QAC/CA,EAASmS,SAASE,EAAa,IAC/BC,EAAa,GAAGtZ,OAAOS,EAAMsG,OAASmR,EAAOnR,OAC7CA,EAAQoS,SAASG,EAAY,IACjC,OAAOxP,EAAcA,EAAcA,EAAcA,EAAcA,EAAc,CAAC,EAAGrJ,GAAQyX,GAASjY,EAAI,CACpGA,KACE,CAAC,GAAIC,EAAI,CACXA,KACE,CAAC,GAAI,CAAC,EAAG,CACX8G,SACAD,QACAiG,KAAMvM,EAAMuM,KACZhP,OAAQyC,EAAMzC,QAElB,CACO,SAASub,EAAa9Y,GAC3B,OAAoBwB,EAAAA,cAAoBuW,EAAOzb,EAAS,CACtDqb,UAAW,YACXM,gBAAiBK,EACjBJ,gBAAiB,uBAChBlY,GACL,CAOO,IAAI+Y,EAAuB,SAA8BC,GAC9D,IAAIC,EAAerc,UAAUC,OAAS,QAAsB8G,IAAjB/G,UAAU,GAAmBA,UAAU,GAAK,EACvF,MAAO,CAACoI,EAAOyN,KACb,IAAIhF,EAAAA,EAAAA,IAASuL,GAAe,OAAOA,EACnC,IAAIE,GAAqBzL,EAAAA,EAAAA,IAASzI,KAAU0G,EAAAA,EAAAA,IAAU1G,GACtD,OAAIkU,EACKF,EAAahU,EAAOyN,IAE5ByG,GH/DL,SAAmBC,GACf,IAAIA,EAIA,MAAM,IAAIC,MAAMhW,EAKxB,CGqDwOiW,EAAU,GACvOJ,GAEX,E,mBClEWK,EAA4BA,CAACC,EAAuBvF,KAC7D,IAAIiB,GAAWC,EAAAA,EAAAA,KACf,MAAO,CAACsE,EAAM/G,IAAU5D,IACI,OAA1B0K,QAA4D,IAA1BA,GAAoCA,EAAsBC,EAAM/G,EAAO5D,GACzGoG,GAASwE,EAAAA,EAAAA,IAA4B,CACnCjE,YAAa5L,OAAO6I,GACpBiH,cAAe1F,EACfpC,iBAAkB4H,EAAKG,qBAIlBC,EAA4BC,IACrC,IAAI5E,GAAWC,EAAAA,EAAAA,KACf,MAAO,CAACsE,EAAM/G,IAAU5D,IACI,OAA1BgL,QAA4D,IAA1BA,GAAoCA,EAAsBL,EAAM/G,EAAO5D,GACzGoG,GAAS6E,EAAAA,EAAAA,SAGFC,EAA4BA,CAACC,EAAuBhG,KAC7D,IAAIiB,GAAWC,EAAAA,EAAAA,KACf,MAAO,CAACsE,EAAM/G,IAAU5D,IACI,OAA1BmL,QAA4D,IAA1BA,GAAoCA,EAAsBR,EAAM/G,EAAO5D,GACzGoG,GAASgF,EAAAA,EAAAA,IAAwB,CAC/BzE,YAAa5L,OAAO6I,GACpBiH,cAAe1F,EACfpC,iBAAkB4H,EAAKG,qB,oBCxBlBO,EAAYA,KACrB,IAAIjF,GAAWC,EAAAA,EAAAA,KAOf,OANAtN,EAAAA,EAAAA,WAAU,KACRqN,GAASkF,EAAAA,EAAAA,OACF,KACLlF,GAASmF,EAAAA,EAAAA,UAGN,M,6FCXT,SAAStR,GAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAC9P,SAASuM,GAAc1M,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,GAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,GAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,GAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CACtb,SAAS4M,GAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CAanL,IAGI0d,GAAkBA,CAACC,EAAQC,EAAUC,EAAUC,EAAaC,IAAgBA,EAG5EC,GAAaA,CAACC,EAAYC,EAAWC,KACvC,IAAIC,EAAuB,OAAbD,QAAkC,IAAbA,EAAsBA,EAAWF,EACpE,KAAIlP,EAAAA,EAAAA,IAAUqP,GAGd,OAAOxa,EAAAA,EAAAA,IAAgBwa,EAASF,EAAW,IAElCG,IAAuBC,EAAAA,EAAAA,IAAe,CAACC,EAAAA,GAAmBC,EAAAA,GAbnDC,CAACd,EAAQe,IAAYA,EACrBC,CAAChB,EAAQC,EAAUgB,IAAYA,EAC5BC,CAAClB,EAAQC,EAAUC,EAAUiB,IAAeA,GAW+E,CAAC1J,EAAQ2J,EAAUL,EAASE,EAASE,IAAeC,EAASxS,OAAOpC,GACnM,eAAXiL,EACKjL,EAAEuU,UAAYA,EAEhBvU,EAAEyU,UAAYA,GACpBrS,OAAOpC,GAAKA,EAAE2U,aAAeA,GAAYvS,OAAOpC,IAAgB,IAAXA,EAAE2P,MAAgBvN,OAAOpC,GAAgB,QAAXA,EAAEuF,OA2BxF,SAASsP,GAAUC,GACjB,OAAgC,MAAzBA,EAAcC,SAA4C,MAAzBD,EAAc5H,OACxD,CACO,IAgCI8H,IAAoBb,EAAAA,EAAAA,IAAe,CAACD,GAAsBe,GAAAA,GAtD7BC,CAACpM,EAAOyL,EAASE,IAExC,gBADFL,EAAAA,EAAAA,IAAkBtL,IAEtBqM,EAAAA,EAAAA,IAAwBrM,EAAO,QAASyL,IAE1CY,EAAAA,EAAAA,IAAwBrM,EAAO,QAAS2L,IAiBjBW,CAACC,EAASvB,EAAYC,KACpD,IACIuB,EAAcD,EAAQjT,OAAOyS,IAC7BU,EAAgBF,EAAQjT,OAAOoT,GAAkB,MAAbA,EAAET,SACtCU,EAAeH,EAAYI,OAAO,CAACC,EAAKC,KACrCD,EAAIC,EAAIb,WACXY,EAAIC,EAAIb,SAAW,IAErBY,EAAIC,EAAIb,SAASzS,KAAKsT,GACfD,GARU,CAAC,GA6BpB,MAAO,IAnBelgB,OAAOogB,QAAQJ,GAAcpQ,IAAI/O,IACrD,IAAKye,EAASe,GAAQxf,EAGtB,MAAO,CACLye,UACAgB,SAJaD,EAAKzQ,IAAImQ,GAAKA,EAAEtI,SAK7B+G,QAJYJ,GAAWC,EAAYC,EAAW+B,EAAK,GAAG7B,eAOlCsB,EAAclQ,IAAImQ,IAGjC,CACLT,aAASlY,EACTkZ,SAJa,CAACP,EAAEtI,SAAS9K,OAAO4T,GAAY,MAANA,GAKtC/B,QAJYJ,GAAWC,EAAYC,EAAWyB,EAAEvB,eA4BlDgC,GAAqBA,CAACnN,EAAOyL,EAASE,EAASE,KACjD,IACIuB,EAAMC,EAQV,MAPe,gBAFF/B,EAAAA,EAAAA,IAAkBtL,IAG7BoN,GAAOE,EAAAA,EAAAA,IAAoBtN,EAAO,QAASyL,EAASI,GACpDwB,GAAQE,EAAAA,EAAAA,IAA2BvN,EAAO,QAASyL,EAASI,KAE5DuB,GAAOE,EAAAA,EAAAA,IAAoBtN,EAAO,QAAS2L,EAASE,GACpDwB,GAAQE,EAAAA,EAAAA,IAA2BvN,EAAO,QAAS2L,EAASE,KAEvD2B,EAAAA,EAAAA,IAAkBJ,EAAMC,IAmE1B,IAYII,IAAwBpC,EAAAA,EAAAA,IAAe,CAACa,GAAmBwB,GAAAA,GAAsBC,GAAAA,GAAcC,GAAAA,GA3G3EC,CAAC7N,EAAOyL,EAASE,EAASE,EAAYf,KACnE,IAAIhc,EAAOgf,EAOPV,EAAMC,EANNlL,GAASmJ,EAAAA,EAAAA,IAAkBtL,GAC3B+N,GAAmBL,EAAAA,GAAAA,IAAqB1N,IAE1CgO,WAAYC,GACVnD,EACAkD,GAAalS,EAAAA,EAAAA,IAAUmS,GAAmBF,EAAmBE,EASjE,MAPe,eAAX9L,GACFiL,GAAOE,EAAAA,EAAAA,IAAoBtN,EAAO,QAASyL,EAASI,GACpDwB,GAAQE,EAAAA,EAAAA,IAA2BvN,EAAO,QAASyL,EAASI,KAE5DuB,GAAOE,EAAAA,EAAAA,IAAoBtN,EAAO,QAAS2L,EAASE,GACpDwB,GAAQE,EAAAA,EAAAA,IAA2BvN,EAAO,QAAS2L,EAASE,IAE+F,QAArJ/c,EAAwE,QAA/Dgf,GAAqBN,EAAAA,EAAAA,IAAkBJ,EAAMC,GAAO,UAA0C,IAAvBS,EAAgCA,EAAqBE,SAAkC,IAAVlf,EAAmBA,EAAQ,GA2F/Cqe,GAxL9He,CAACxD,EAAQC,EAAUC,EAAUC,EAAaC,IAAgBA,EAAYkD,YA4KvDG,CAACC,EAAUL,EAAkBM,EAAQC,EAAgBC,EAAaC,EAAUP,KAC9G,IAAID,GAAalS,EAAAA,EAAAA,IAAUmS,GAAmBF,EAAmBE,EAC7DQ,EAnEN,SAAyBJ,EAAQC,EAAgBE,EAAUJ,EAAUJ,GACnE,IAAIU,EAAMN,EAASnhB,OACnB,KAAIyhB,EAAM,GAAV,CAGA,IACIC,EADAC,GAAaje,EAAAA,EAAAA,IAAgB0d,EAAQG,EAAU,GAAG,GAElDK,EAAe,GAInB,IAAIC,EAAAA,GAAAA,GAAoBV,EAAS,GAAGjD,SAAU,CAC5C,IAAI4D,GAAU,EACVC,EAAcR,EAAWE,EACzBO,EAAMb,EAASxB,OAAO,CAACsC,EAAK1S,IAAU0S,GAAO1S,EAAM2O,SAAW,GAAI,IACtE8D,IAAQP,EAAM,GAAKE,IACRJ,IACTS,IAAQP,EAAM,GAAKE,EACnBA,EAAa,GAEXK,GAAOT,GAAYQ,EAAc,IACnCD,GAAU,EAEVE,EAAMP,GADNM,GAAe,KAGjB,IACIG,EAAO,CACT/O,SAFYoO,EAAWS,GAAO,EAAK,GAElBL,EACjBzY,KAAM,GAERwY,EAASP,EAASxB,OAAO,CAACsC,EAAK1S,KAC7B,IAAI4S,EASAC,EAAS,IAAIH,EARC,CAChBjD,QAASzP,EAAMyP,QACfgB,SAAUzQ,EAAMyQ,SAChBhP,SAAU,CACRmC,OAAQ+O,EAAK/O,OAAS+O,EAAKhZ,KAAOyY,EAClCzY,KAAM4Y,EAAUC,EAAmD,QAApCI,EAAiB5S,EAAM2O,eAAwC,IAAnBiE,EAA4BA,EAAiB,KAK5H,OADAD,EAAOE,EAAOA,EAAOpiB,OAAS,GAAGgR,SAC1BoR,GACNR,EACL,KAAO,CACL,IAAIS,GAAU3e,EAAAA,EAAAA,IAAgB2d,EAAgBE,EAAU,GAAG,GACvDA,EAAW,EAAIc,GAAWZ,EAAM,GAAKE,GAAc,IACrDA,EAAa,GAEf,IAAIW,GAAgBf,EAAW,EAAIc,GAAWZ,EAAM,GAAKE,GAAcF,EACnEa,EAAe,IACjBA,IAAiB,GAEnB,IAAIpZ,GAAO2Y,EAAAA,GAAAA,GAAoBd,GAAc7f,KAAKkB,IAAIkgB,EAAcvB,GAAcuB,EAClFZ,EAASP,EAASxB,OAAO,CAACsC,EAAK1S,EAAOtF,IAAM,IAAIgY,EAAK,CACnDjD,QAASzP,EAAMyP,QACfgB,SAAUzQ,EAAMyQ,SAChBhP,SAAU,CACRmC,OAAQkP,GAAWC,EAAeX,GAAc1X,GAAKqY,EAAepZ,GAAQ,EAC5EA,UAEA0Y,EACN,CACA,OAAOF,CA3DP,CA4DF,CAGwBa,CAAgBnB,EAAQC,EAAgBC,IAAgBC,EAAWD,EAAcC,EAAUJ,EAAUJ,GAQ3H,OAPIO,IAAgBC,GAA+B,MAAnBC,IAC9BA,EAAkBA,EAAgBlS,IAAIkT,GAAOhW,GAAcA,GAAc,CAAC,EAAGgW,GAAM,CAAC,EAAG,CACrFxR,SAAUxE,GAAcA,GAAc,CAAC,EAAGgW,EAAIxR,UAAW,CAAC,EAAG,CAC3DmC,OAAQqP,EAAIxR,SAASmC,OAASmO,EAAc,QAI3CE,IAOEiB,IAAoBrE,EAAAA,EAAAA,IAAe,CAACoC,GAAuBhD,IAAkB,CAACgE,EAAiB3D,KACxG,GAAuB,MAAnB2D,EAAJ,CAGA,IAAIxQ,EAAWwQ,EAAgBkB,KAAKC,GAAKA,EAAE3D,UAAYnB,EAAYmB,SAAW2D,EAAE3C,SAAS4C,SAAS/E,EAAY1G,UAC9G,GAAgB,MAAZnG,EAGJ,OAAOA,EAASA,QALhB,IA8BE6R,IAAgCzE,EAAAA,EAAAA,IAAe,CAACE,EAAAA,GAAgCd,IAAkB,CAACsF,EAAgBC,KACrH,GAAID,EAAeE,KAAKC,GAAsB,QAAdA,EAAKzT,MAAkBuT,EAAqB5L,UAAY8L,EAAK9L,SAAW4L,EAAqB/D,UAAYiE,EAAKjE,SAE9I+D,EAAqB/D,UAAYiE,EAAKjE,SACpC,OAAO+D,IAIPG,IAA0B9E,EAAAA,EAAAA,IAAe,CAvNlB+E,CAACpQ,EAAOyL,EAASE,EAASE,IAEpC,gBADFP,EAAAA,EAAAA,IAAkBtL,IAEtBqQ,EAAAA,EAAAA,IAAkBrQ,EAAO,QAAS2L,EAASE,IAE7CwE,EAAAA,EAAAA,IAAkBrQ,EAAO,QAASyL,EAASI,GAkNgBpB,IA/BpC6F,CAACC,EAAazF,KAC5C,GAAKyF,GAAkG,OAAlE,OAAhBzF,QAAwC,IAAhBA,OAAyB,EAASA,EAAY1G,SAA3F,CAGA,IAAI,QACF6H,GACEnB,EACJ,GAAe,MAAXmB,EAAJ,CAGA,IAAIuE,EAAaD,EAAYtE,GAC7B,GAAKuE,EAAL,CAGA,IAAI,YACFC,GACED,EACJ,GAAKC,EAIL,OADYA,EAAYd,KAAKe,GAAMA,EAAGtT,MAAQ0N,EAAY1G,QAP1D,CAJA,CANA,IA6BSuM,IAAsBtF,EAAAA,EAAAA,IAAe,CAACuF,GAAAA,GA9CtBC,CAAC7Q,EAAOyL,EAASb,EAAUiB,KAAeyB,EAAAA,EAAAA,IAAoBtN,EAAO,QAASyL,EAASI,GACvFiF,CAAC9Q,EAAO2K,EAAUgB,EAASE,KAAeyB,EAAAA,EAAAA,IAAoBtN,EAAO,QAAS2L,EAASE,GAC3FkF,CAAC/Q,EAAOyL,EAASb,EAAUiB,KAAe0B,EAAAA,EAAAA,IAA2BvN,EAAO,QAASyL,EAASI,GAC9FmF,CAAChR,EAAO2K,EAAUgB,EAASE,KAAe0B,EAAAA,EAAAA,IAA2BvN,EAAO,QAAS2L,EAASE,GA2C+B6D,GAAmBpE,EAAAA,GAAmB2F,GAAAA,GAA2C9D,GAAoBgD,GAAyBL,GAtOlQoB,CAACxG,EAAQC,EAAUC,EAAUC,EAAasG,EAAcC,IAAUA,GAsO2O,CAAChR,EAAQiR,EAAOC,EAAOC,EAAYC,EAAY/B,EAAKtN,EAAQvR,EAAO4d,EAAUiC,EAAa3F,EAAasG,KACla,IAAI,UACFK,EAAS,eACTC,EAAc,aACdC,GACE/gB,EACJ,GAAmB,MAAfka,GAA8B,MAAP2E,IAA0B,eAAXtN,GAAsC,aAAXA,IAAkC,MAATkP,GAA0B,MAATC,GAA+B,MAAdC,GAAoC,MAAdC,GAAkC,MAAZhD,EAA5K,CAGA,IAGIoD,GAHA,KACFhI,GACEkB,EAOJ,GAAqB,OAJnB8G,EADU,MAARhI,GAAgBA,EAAK3c,OAAS,EAChB2c,EAEc,OAAd6H,QAAoC,IAAdA,OAAuB,EAASA,EAAUI,MAAMH,EAAgBC,EAAe,IAKvH,OAAOG,GAAqB,CAC1B3P,SACA2I,cACA2E,MACAjB,WACA6C,QACAC,QACAC,aACAC,aACAf,cACAmB,gBACAxR,SACAgR,SAzBF,I,4CClQE5P,GAAY,CAAC,eAAgB,eAAgB,WAC/CuQ,GAAa,CAAC,QAAS,aAAc,mBACrCC,GAAa,CAAC,eAAgB,UAAW,gBAC3C,SAAStlB,KAAa,OAAOA,GAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAAIF,UAAUD,GAAI,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAAK,CAAE,OAAOL,CAAG,EAAGJ,GAASY,MAAM,KAAMN,UAAY,CACnR,SAASkM,GAAQnM,EAAGI,GAAK,IAAID,EAAIP,OAAOwM,KAAKpM,GAAI,GAAIJ,OAAOyM,sBAAuB,CAAE,IAAIC,EAAI1M,OAAOyM,sBAAsBrM,GAAII,IAAMkM,EAAIA,EAAEC,OAAO,SAAUnM,GAAK,OAAOR,OAAOgJ,yBAAyB5I,EAAGI,GAAGoM,UAAY,IAAKrM,EAAEsM,KAAKlM,MAAMJ,EAAGmM,EAAI,CAAE,OAAOnM,CAAG,CAC9P,SAASuM,GAAc1M,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAI+L,GAAQvM,OAAOO,IAAI,GAAIwM,QAAQ,SAAUvM,GAAKwM,GAAgB5M,EAAGI,EAAGD,EAAEC,GAAK,GAAKR,OAAOiN,0BAA4BjN,OAAOkN,iBAAiB9M,EAAGJ,OAAOiN,0BAA0B1M,IAAMgM,GAAQvM,OAAOO,IAAIwM,QAAQ,SAAUvM,GAAKR,OAAOqI,eAAejI,EAAGI,EAAGR,OAAOgJ,yBAAyBzI,EAAGC,GAAK,EAAI,CAAE,OAAOJ,CAAG,CACtb,SAAS4M,GAAgB5M,EAAGI,EAAGD,GAAK,OAAQC,EAC5C,SAAwBD,GAAK,IAAIgK,EACjC,SAAsBhK,EAAGC,GAAK,GAAI,iBAAmBD,IAAMA,EAAG,OAAOA,EAAG,IAAIH,EAAIG,EAAEgI,OAAO4E,aAAc,QAAI,IAAW/M,EAAG,CAAE,IAAImK,EAAInK,EAAEM,KAAKH,EAAGC,GAAK,WAAY,GAAI,iBAAmB+J,EAAG,OAAOA,EAAG,MAAM,IAAI6C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5M,EAAI6M,OAASC,QAAQ/M,EAAI,CADlRgN,CAAahN,EAAG,UAAW,MAAO,iBAAmBgK,EAAIA,EAAIA,EAAI,EAAI,CAD1DiD,CAAehN,MAAOJ,EAAIJ,OAAOqI,eAAejI,EAAGI,EAAG,CAAEiI,MAAOlI,EAAGqM,YAAY,EAAIa,cAAc,EAAIxE,UAAU,IAAQ7I,EAAEI,GAAKD,EAAGH,CAAG,CAGnL,SAAS+U,GAAyB/U,EAAGG,GAAK,GAAI,MAAQH,EAAG,MAAO,CAAC,EAAG,IAAIsM,EAAGlM,EAAG+J,EAC9E,SAAuC/J,EAAGJ,GAAK,GAAI,MAAQI,EAAG,MAAO,CAAC,EAAG,IAAID,EAAI,CAAC,EAAG,IAAK,IAAIJ,KAAKK,EAAG,GAAI,CAAC,EAAEC,eAAeC,KAAKF,EAAGL,GAAI,CAAE,IAAK,IAAMC,EAAE4U,QAAQ7U,GAAI,SAAUI,EAAEJ,GAAKK,EAAEL,EAAI,CAAE,OAAOI,CAAG,CADpH0U,CAA8B7U,EAAGG,GAAI,GAAIP,OAAOyM,sBAAuB,CAAE,IAAItM,EAAIH,OAAOyM,sBAAsBrM,GAAI,IAAKI,EAAI,EAAGA,EAAIL,EAAEG,OAAQE,IAAKkM,EAAIvM,EAAEK,IAAK,IAAMD,EAAEyU,QAAQtI,IAAM,CAAC,EAAEwI,qBAAqBxU,KAAKN,EAAGsM,KAAOnC,EAAEmC,GAAKtM,EAAEsM,GAAK,CAAE,OAAOnC,CAAG,CAiCrU,IAAI+a,GAAkC7hB,IACpC,IAAI,QACFgU,EAAO,KACPzH,EAAI,KACJ0G,EAAI,WACJ6O,EAAU,KACVrL,GACEzW,EACJ,MAAO,CAAC,CACN+hB,SAAUtL,EACVzC,UACA3H,KAAMyV,EACN/U,MAAOkG,EACPjO,OAAOgd,EAAAA,EAAAA,IAAmBzV,EAAMyH,GAChCtJ,QAAS1K,KAGb,SAASiiB,GAAwBjiB,GAC/B,IAAI,QACFgU,EAAO,OACPhB,EAAM,YACNkP,EAAW,KACXjP,EAAI,KACJ1G,EAAI,KACJkK,EAAI,KACJxJ,GACEjN,EACJ,MAAO,CACLmiB,uBAAmBxe,EACnBye,eAAWze,EACX0e,SAAU,CACRrP,SACAkP,cACAjP,OACAe,UACAsO,aAAS3e,EACT4I,MAAMyV,EAAAA,EAAAA,IAAmBzV,EAAMyH,GAC/ByC,OACApK,KAAMrM,EAAMuiB,YACZxV,MAAO/M,EAAMiT,KACbhG,QAGN,CACA,SAASuV,GAAcxiB,GACrB,IAAIwV,GAAcC,EAAAA,EAAAA,GAAegN,GAAAA,KAC7B,KACFjJ,EAAI,QACJxF,EACA0O,WAAYC,EAAmB,iBAC/BC,GACE5iB,GAEA6iB,aAActJ,EACduJ,aAAcjJ,EACdhW,QAASkf,GACPH,EACJI,EAAsBtR,GAAyBkR,EAAkBxR,IAC/D6R,EAA0B3J,EAA0BC,EAAuBvF,GAC3EkP,EAA0BtJ,EAA0BC,GACpDsJ,EAAqBpJ,EAA0BgJ,EAAsB/O,GACzE,IAAK2O,GAA+B,MAARnJ,EAC1B,OAAO,KAET,IAAI4J,GAAkB3hB,EAAAA,EAAAA,IAAYkhB,GAAqB,GACvD,OAAoBnhB,EAAAA,cAAoBA,EAAAA,SAAgB,KAAMgY,EAAKrN,IAAI,CAACC,EAAOtF,KAC7E,IAAI,MACA9B,EACA0d,WAAYW,EAAuB,gBACnC1J,GACEvN,EACJkX,EAAO5R,GAAyBtF,EAAOuV,IACzC,IAAK0B,EACH,OAAO,KAIT,IAAIR,EAAeI,EAAwB7W,EAAOtF,GAE9Cgc,EAAeI,EAAwB9W,EAAOtF,GAE9CjD,EAAUsf,EAAmB/W,EAAOtF,GACpCyc,EAAoBla,GAAcA,GAAcA,GAAcA,GAAcA,GAAc,CAC5FoO,OAAQkL,EACRra,SAAUsB,OAAO9C,KAAO0O,GACvB8N,GAAO,CAAC,EAAG,CAEZrQ,KAAM,QACLoQ,GAA0BD,IAAkBI,EAAAA,EAAAA,IAAmBR,EAAqB5W,EAAOtF,IAAK,CAAC,EAAG,CACrG+b,eACAC,eACAjf,UACAmQ,UACAvB,MAAO3L,EACP5G,UAAW,sCAEb,OAAoBsB,EAAAA,cAAoBsX,EAAcxc,GAAS,CAC7D0Q,IAAK,kBAAkBzN,OAAOuH,IAC7Byc,MAEP,CACA,SAASE,GAAcrmB,GACrB,IAAI,KACFoc,EAAI,MACJxZ,EAAK,WACL0jB,GACEtmB,EACAumB,GAAYliB,EAAAA,EAAAA,IAAYzB,GAAO,IAC/B,MACFgY,EAAK,QACLhE,EAAO,UACP4P,GACE5jB,EACAwV,GAAcC,EAAAA,EAAAA,GAAegN,GAAAA,IAC7B/I,GAAgBjE,EAAAA,EAAAA,GAAeoO,GAAAA,KAE/BhB,aAActJ,EACd1V,QAASkf,EACTD,aAAcjJ,GACZ7Z,EACJgjB,EAAsBtR,GAAyB1R,EAAO4hB,IACpDqB,EAA0B3J,EAA0BC,EAAuBvF,GAC3EkP,EAA0BtJ,EAA0BC,GACpDsJ,EAAqBpJ,EAA0BgJ,EAAsB/O,GACzE,OAAKwF,EAGehY,EAAAA,cAAoBA,EAAAA,SAAgB,KAAMgY,EAAKrN,IAAI,CAACC,EAAOtF,KAU7E,IAAIwB,EAAWsb,GAAaha,OAAO9C,KAAO0O,IAAiC,MAAjBkE,GAAyB1F,IAAY0F,GAC3FjC,EAASnP,EAAWsb,EAAY5L,EAChCuL,EAAoBla,GAAcA,GAAcA,GAAc,CAAC,EAAGsa,GAAYvX,GAAQ,CAAC,EAAG,CAC5F9D,WACAmP,SACAhF,MAAO3L,EACPkN,YAEF,OAAoBxS,EAAAA,cAAoB6W,EAAAA,EAAO/b,GAAS,CACtD4D,UAAW,2BACVsjB,EAAAA,EAAAA,IAAmBR,EAAqB5W,EAAOtF,GAAI,CAEpD+b,aAAcI,EAAwB7W,EAAOtF,GAG7Cgc,aAAcI,EAAwB9W,EAAOtF,GAG7CjD,QAASsf,EAAmB/W,EAAOtF,GAInCkG,IAAK,aAAazN,OAAiB,OAAV6M,QAA4B,IAAVA,OAAmB,EAASA,EAAM5M,EAAG,KAAKD,OAAiB,OAAV6M,QAA4B,IAAVA,OAAmB,EAASA,EAAM3M,EAAG,KAAKF,OAAiB,OAAV6M,QAA4B,IAAVA,OAAmB,EAASA,EAAMpH,MAAO,KAAKzF,OAAOuH,KACvNtF,EAAAA,cAAoBsX,EAAcyK,MACjDG,GAAcI,EAAAA,EAAUC,mBAAmB/jB,EAAOwZ,IApC7C,IAqCX,CACA,SAASwK,GAAwBtlB,GAC/B,IAAI,MACFsB,EAAK,sBACLikB,GACEvlB,GACA,KACF8a,EAAI,OACJzH,EAAM,kBACN/K,EAAiB,eACjBE,EAAc,kBACdC,EAAiB,gBACjBC,EAAe,eACf8c,EAAc,iBACdC,GACEnkB,EACAokB,EAAWH,EAAsBpc,QACjCwc,GAAcC,EAAAA,GAAAA,GAAetkB,EAAO,kBACnCukB,EAAaC,IAAkB7c,EAAAA,EAAAA,WAAS,GACzC8c,GAAqBC,EAAAA,EAAAA,aAAY,KACL,oBAAnBR,GACTA,IAEFM,GAAe,IACd,CAACN,IACAS,GAAuBD,EAAAA,EAAAA,aAAY,KACL,oBAArBP,GACTA,IAEFK,GAAe,IACd,CAACL,IACJ,OAAoB3iB,EAAAA,cAAoByG,EAAAA,EAAS,CAC/CW,MAAO1B,EACPmB,SAAUlB,EACVmB,SAAUtB,EACV6B,OAAQzB,EACRe,KAAM,CACJrL,EAAG,GAELsL,GAAI,CACFtL,EAAG,GAELonB,eAAgBO,EAChBN,iBAAkBQ,EAClB3X,IAAKqX,GACJ7jB,IACD,IAAI,EACF1D,GACE0D,EACAokB,EAAiB,IAAN9nB,EAAU0c,EAAOA,EAAKrN,IAAI,CAACC,EAAOqG,KAC/C,IAAIsM,EAAOqF,GAAYA,EAAS3R,GAChC,GAAIsM,EAAM,CACR,IAAI8F,GAAgBC,EAAAA,EAAAA,IAAkB/F,EAAKvf,EAAG4M,EAAM5M,GAChDulB,GAAgBD,EAAAA,EAAAA,IAAkB/F,EAAKtf,EAAG2M,EAAM3M,GAChDulB,GAAoBF,EAAAA,EAAAA,IAAkB/F,EAAKzY,MAAO8F,EAAM9F,OACxD2e,GAAqBH,EAAAA,EAAAA,IAAkB/F,EAAKxY,OAAQ6F,EAAM7F,QAC9D,OAAO8C,GAAcA,GAAc,CAAC,EAAG+C,GAAQ,CAAC,EAAG,CACjD5M,EAAGqlB,EAAc/nB,GACjB2C,EAAGslB,EAAcjoB,GACjBwJ,MAAO0e,EAAkBloB,GACzByJ,OAAQ0e,EAAmBnoB,IAE/B,CACA,GAAe,eAAXiV,EAAyB,CAC3B,IACImT,GADsBJ,EAAAA,EAAAA,IAAkB,EAAG1Y,EAAM7F,OAC7C4e,CAAoBroB,GAC5B,OAAOuM,GAAcA,GAAc,CAAC,EAAG+C,GAAQ,CAAC,EAAG,CACjD3M,EAAG2M,EAAM3M,EAAI2M,EAAM7F,OAAS2e,EAC5B3e,OAAQ2e,GAEZ,CACA,IACIE,GADeN,EAAAA,EAAAA,IAAkB,EAAG1Y,EAAM9F,MACtC+e,CAAavoB,GACrB,OAAOuM,GAAcA,GAAc,CAAC,EAAG+C,GAAQ,CAAC,EAAG,CACjD9F,MAAO8e,MAOX,OAJItoB,EAAI,IAENmnB,EAAsBpc,QAAU+c,GAEdpjB,EAAAA,cAAoB6W,EAAAA,EAAO,KAAmB7W,EAAAA,cAAoBiiB,GAAe,CACnGzjB,MAAOA,EACPwZ,KAAMoL,EACNlB,YAAaa,MAGnB,CACA,SAASe,GAAiBtlB,GACxB,IAAI,KACFwZ,EAAI,kBACJxS,GACEhH,EACAikB,GAAwBzc,EAAAA,EAAAA,QAAO,MACnC,OAAIR,GAAqBwS,GAAQA,EAAK3c,SAA4C,MAAjConB,EAAsBpc,SAAmBoc,EAAsBpc,UAAY2R,GACtGhY,EAAAA,cAAoBwiB,GAAyB,CAC/DC,sBAAuBA,EACvBjkB,MAAOA,IAGSwB,EAAAA,cAAoBiiB,GAAe,CACrDzjB,MAAOA,EACPwZ,KAAMA,EACNkK,YAAY,GAEhB,CACA,IAAI6B,GAAsB,EACtBC,GAA6BA,CAACC,EAAWzR,KAK3C,IAAIhP,EAAQ4B,MAAMsD,QAAQub,EAAUzgB,OAASygB,EAAUzgB,MAAM,GAAKygB,EAAUzgB,MAC5E,MAAO,CACLxF,EAAGimB,EAAUjmB,EACbC,EAAGgmB,EAAUhmB,EACbuF,QAEA0gB,UAAUC,EAAAA,EAAAA,IAAkBF,EAAWzR,KAG3C,MAAM4R,WAAqBrX,EAAAA,cACzBC,WAAAA,GACEC,SAAS7R,WACT2M,GAAgBmF,KAAM,MAAMmX,EAAAA,EAAAA,IAAS,iBACvC,CACAhW,MAAAA,GACE,IAAI,KACF4G,EAAI,KACJ+C,EAAI,QACJxF,EAAO,UACP9T,EAAS,QACTmb,EAAO,QACPE,EAAO,SACPuK,EAAQ,WACRpD,EAAU,GACVqD,EAAE,OACFhU,GACErD,KAAK1O,MACT,GAAIyW,EACF,OAAO,KAET,IAAItW,GAAaC,EAAAA,EAAAA,GAAK,eAAgBF,GAClC8lB,GAAata,EAAAA,EAAAA,IAAUqa,GAAMrX,KAAKqX,GAAKA,EAC3C,OAAoBvkB,EAAAA,cAAoB6W,EAAAA,EAAO,CAC7CnY,UAAWC,GACV2lB,GAAyBtkB,EAAAA,cAAoB,OAAQ,KAAmBA,EAAAA,cAAoBykB,EAAAA,EAAuB,CACpHD,WAAYA,EACZ3K,QAASA,EACTE,QAASA,KACO/Z,EAAAA,cAAoB6W,EAAAA,EAAO,CAC3CnY,UAAW,0BACXgmB,SAAUJ,EAAW,iBAAiBvmB,OAAOymB,EAAY,KAAO,MAClDxkB,EAAAA,cAAoBghB,GAAe,CACjDhJ,KAAMA,EACNxF,QAASA,EACT0O,WAAYA,EACZE,iBAAkBlU,KAAK1O,QACRwB,EAAAA,cAAoB8jB,GAAkB5W,KAAK1O,QAAsBwB,EAAAA,cAAoB2kB,EAAAA,EAA+B,CACnIC,UAAsB,eAAXrU,EAA0B,IAAM,KAC1CrD,KAAK1O,MAAM6C,UAChB,EAEF,IAAIwjB,GAAkB,CACpBzC,WAAW,EACX1c,eAAgB,EAChBC,kBAAmB,IACnBC,gBAAiB,OACjBqP,MAAM,EACNzP,mBAAoBqN,EAAAA,EAAOC,MAC3BwN,WAAY,OACZ9I,aAAcuM,GACdlK,QAAS,EACTE,QAAS,GAEX,SAAS+K,GAAQtmB,GACf,IA8BIumB,GA9BA,QACFlL,EAAO,QACPE,EAAO,KACP9E,EAAI,WACJqL,EAAU,aACV9I,EAAY,UACZ4K,EAAS,eACT1c,EAAc,kBACdC,EAAiB,gBACjBC,EAAe,kBACfJ,IACE/G,EAAAA,EAAAA,GAAoBD,EAAOqmB,KAC3B,SACFP,IACEU,EAAAA,EAAAA,GAAanL,EAASE,GACtBxJ,GAAS8B,EAAAA,EAAAA,MACT4H,GAAagL,EAAAA,GAAAA,KACb/L,GAAcgM,EAAAA,EAAAA,SAAQ,KAAM,CAC9B3L,QAAS/a,EAAM+a,QACfvB,UAAM7V,EACNqQ,QAAShU,EAAMgU,QACf4J,WAAY5d,EAAM4d,WAClB5E,eACA6C,SAAS8K,EAAAA,EAAAA,IAAqB3mB,EAAM6b,WAClC,CAAC7b,EAAM+a,QAAS/a,EAAMgU,QAAShU,EAAM4d,WAAY5E,EAAchZ,EAAM6b,UACrEmF,GAAQ4F,EAAAA,EAAAA,IAAc5mB,EAAM6C,SAAUiU,GACtC+P,GAAQpR,EAAAA,EAAAA,GAAe7F,GAAS2Q,GAAoB3Q,EAAOyL,EAASE,EAASE,EAAYf,EAAasG,IAC1G,GAAe,aAAXjP,GAAoC,eAAXA,EAC3B,OAAO,KAGT,IAAI+U,EAA2B,OAAVD,QAA4B,IAAVA,OAAmB,EAASA,EAAM,GAMzE,OAJEN,EADoB,MAAlBO,GAAmD,MAAzBA,EAAevgB,QAA0C,MAAxBugB,EAAexgB,MAC3D,EAEW,aAAXyL,EAAwB+U,EAAevgB,OAAS,EAAIugB,EAAexgB,MAAQ,EAE1E9E,EAAAA,cAAoBulB,EAAAA,GAAoB,CAC1D1L,QAASA,EACTE,QAASA,EACT/B,KAAMqN,EACNG,mBAAoBxB,GACpBe,eAAgBA,GACF/kB,EAAAA,cAAoBokB,GAActpB,GAAS,CAAC,EAAG0D,EAAO,CACpE+R,OAAQA,EACR+T,SAAUA,EACVtM,KAAMqN,EACNxL,QAASA,EACTE,QAASA,EACT9E,KAAMA,EACNqL,WAAYA,EACZ9I,aAAcA,EACd4K,UAAWA,EACX1c,eAAgBA,EAChBC,kBAAmBA,EACnBC,gBAAiBA,EACjBJ,kBAAmBA,KAEvB,CACO,SAAS0a,GAAqBlR,GACnC,IAAI,OACFuB,EACA2I,aAAa,QACX1G,EACAgF,aAAciO,GACf,IACD5H,EAAG,SACHjB,EAAQ,MACR6C,EAAK,MACLC,EAAK,WACLC,EAAU,WACVC,EAAU,YACVf,EAAW,cACXmB,EAAa,OACbxR,EAAM,MACNgR,GACExQ,EACA0W,EAAyB,eAAXnV,EAA0BmP,EAAQD,EAEhDkG,EAAgB9G,EAAc6G,EAAYE,MAAMC,SAAW,KAC3DC,GAAYC,EAAAA,EAAAA,IAAkB,CAChCL,gBAEF,OAAO1F,EAAcrV,IAAI,CAACC,EAAOqG,KAC/B,IAAIzN,EAAOxF,EAAGC,EAAG6G,EAAOC,EAAQmc,EAC5BrC,EAEFrb,GAAQwiB,EAAAA,EAAAA,IAAiBnH,EAAY5N,GAAQ0U,IAE7CniB,GAAQ2gB,EAAAA,EAAAA,IAAkBvZ,EAAO4H,GAC5BpN,MAAMsD,QAAQlF,KACjBA,EAAQ,CAACsiB,EAAWtiB,KAGxB,IAAIgU,EAAeD,EAAqBkO,EAAkB1B,GAAvCxM,CAA4D/T,EAAM,GAAIyN,GACzF,GAAe,eAAXV,EAAyB,CAC3B,IAAI0V,GACCC,EAAgBC,GAAqB,CAACzG,EAAMkG,MAAMpiB,EAAM,IAAKkc,EAAMkG,MAAMpiB,EAAM,KACpFxF,GAAIooB,EAAAA,EAAAA,IAAuB,CACzB5K,KAAMiE,EACNhE,MAAOkE,EACP/C,WACApO,OAAQqP,EAAIrP,OACZ5D,QACAqG,UAEFhT,EAAkH,QAA7GgoB,EAA8B,OAAtBE,QAAoD,IAAtBA,EAA+BA,EAAoBD,SAAsC,IAAVD,EAAmBA,OAAQ9jB,EACrJ2C,EAAQ+Y,EAAItZ,KACZ,IAAI8hB,EAAiBH,EAAiBC,EAQtC,GAPAphB,GAASuhB,EAAAA,EAAAA,IAAMD,GAAkB,EAAIA,EACrCnF,EAAa,CACXljB,IACAC,EAAGuQ,EAAOe,IACVzK,QACAC,OAAQyJ,EAAOzJ,QAEbxI,KAAKmB,IAAI8Z,GAAgB,GAAKjb,KAAKmB,IAAIqH,GAAUxI,KAAKmB,IAAI8Z,GAAe,CAC3E,IAAI+O,GAAQ/oB,EAAAA,EAAAA,IAASuH,GAAUyS,IAAiBjb,KAAKmB,IAAI8Z,GAAgBjb,KAAKmB,IAAIqH,IAClF9G,GAAKsoB,EACLxhB,GAAUwhB,CACZ,CACF,KAAO,CACL,IAAKC,EAAiBC,GAAsB,CAAChH,EAAMmG,MAAMpiB,EAAM,IAAKic,EAAMmG,MAAMpiB,EAAM,KAkBtF,GAjBAxF,EAAIwoB,EACJvoB,GAAImoB,EAAAA,EAAAA,IAAuB,CACzB5K,KAAMkE,EACNjE,MAAOmE,EACPhD,WACApO,OAAQqP,EAAIrP,OACZ5D,QACAqG,UAEFnM,EAAQ2hB,EAAqBD,EAC7BzhB,EAAS8Y,EAAItZ,KACb2c,EAAa,CACXljB,EAAGwQ,EAAOgB,KACVvR,IACA6G,MAAO0J,EAAO1J,MACdC,UAEExI,KAAKmB,IAAI8Z,GAAgB,GAAKjb,KAAKmB,IAAIoH,GAASvI,KAAKmB,IAAI8Z,GAE3D1S,IADatH,EAAAA,EAAAA,IAASsH,GAAS0S,IAAiBjb,KAAKmB,IAAI8Z,GAAgBjb,KAAKmB,IAAIoH,GAGtF,CACA,OAAO+C,GAAcA,GAAc,CAAC,EAAG+C,GAAQ,CAAC,EAAG,CACjD5M,IACAC,IACA6G,QACAC,SACAvB,MAAOqb,EAAcrb,EAAQA,EAAM,GACnC0F,QAAS0B,EACTsW,aACA/I,gBAAiB,CACfna,EAAGA,EAAI8G,EAAQ,EACf7G,EAAGA,EAAI8G,EAAS,IAEjBya,GAASA,EAAMvO,IAAUuO,EAAMvO,GAAOzS,QAE7C,CACO,MAAMkoB,WAAY3Z,EAAAA,cACvBsB,MAAAA,GAEE,OAAoBrO,EAAAA,cAAoB2mB,EAAAA,GAA+B,CACrE9b,KAAM,MAGNmN,KAAM,KACN6B,QAAS3M,KAAK1O,MAAMqb,QACpBE,QAAS7M,KAAK1O,MAAMub,QACpB6M,QAAS,EACTpU,QAAStF,KAAK1O,MAAMgU,QACpB6H,QAASnN,KAAK1O,MAAM6b,QACpBpF,KAAM/H,KAAK1O,MAAMyW,KACjBsE,QAASrM,KAAK1O,MAAM+a,SACNvZ,EAAAA,cAAoB0Y,EAAW,MAAoB1Y,EAAAA,cAAoB6mB,GAAAA,EAAkB,CACvGC,cAAezG,GAAgCnT,KAAK1O,SACrCwB,EAAAA,cAAoB+mB,EAAAA,EAAyB,CAC5DC,GAAIvG,GACJwG,KAAM/Z,KAAK1O,QACIwB,EAAAA,cAAoB8kB,GAAS5X,KAAK1O,OACrD,EAEFuJ,GAAgB2e,GAAK,cAAe,OACpC3e,GAAgB2e,GAAK,eAAgB7B,G,iBCpjBrCqC,EAAO7jB,QAAU,EAAjB6jB,MAAAA,a", "sources": ["../node_modules/recharts/es6/shape/Sector.js", "../node_modules/react-bootstrap/esm/AlertHeading.js", "../node_modules/react-bootstrap/esm/AlertLink.js", "../node_modules/react-bootstrap/esm/Alert.js", "../node_modules/recharts/es6/chart/BarChart.js", "../node_modules/es-toolkit/dist/compat/predicate/isPlainObject.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/recharts/es6/shape/Rectangle.js", "../node_modules/recharts/es6/component/DefaultTooltipContent.js", "../node_modules/recharts/es6/util/tooltip/translate.js", "../node_modules/recharts/es6/component/TooltipBoundingBox.js", "../node_modules/recharts/es6/shape/Cross.js", "../node_modules/recharts/es6/util/cursor/getRadialCursorPoints.js", "../node_modules/recharts/es6/util/cursor/getCursorPoints.js", "../node_modules/recharts/es6/component/Cursor.js", "../node_modules/recharts/es6/util/cursor/getCursorRectangle.js", "../node_modules/recharts/es6/component/Tooltip.js", "../node_modules/recharts/es6/component/Cell.js", "../node_modules/tiny-invariant/dist/esm/tiny-invariant.js", "../node_modules/recharts/es6/shape/Trapezoid.js", "../node_modules/recharts/es6/util/ActiveShapeUtils.js", "../node_modules/recharts/es6/util/BarUtils.js", "../node_modules/recharts/es6/context/tooltipContext.js", "../node_modules/recharts/es6/state/ReportBar.js", "../node_modules/recharts/es6/state/selectors/barSelectors.js", "../node_modules/recharts/es6/cartesian/Bar.js", "../node_modules/es-toolkit/compat/isPlainObject.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nimport { polarToCartesian, RADIAN } from '../util/PolarUtils';\nimport { getPercentValue, mathSign } from '../util/DataUtils';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nvar getDeltaAngle = (startAngle, endAngle) => {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 359.999);\n  return sign * deltaAngle;\n};\nvar getTangentCircle = _ref => {\n  var {\n    cx,\n    cy,\n    radius,\n    angle,\n    sign,\n    isExternal,\n    cornerRadius,\n    cornerIsExternal\n  } = _ref;\n  var centerRadius = cornerRadius * (isExternal ? 1 : -1) + radius;\n  var theta = Math.asin(cornerRadius / centerRadius) / RADIAN;\n  var centerAngle = cornerIsExternal ? angle : angle + sign * theta;\n  var center = polarToCartesian(cx, cy, centerRadius, centerAngle);\n  // The coordinate of point which is tangent to the circle\n  var circleTangency = polarToCartesian(cx, cy, radius, centerAngle);\n  // The coordinate of point which is tangent to the radius line\n  var lineTangencyAngle = cornerIsExternal ? angle - sign * theta : angle;\n  var lineTangency = polarToCartesian(cx, cy, centerRadius * Math.cos(theta * RADIAN), lineTangencyAngle);\n  return {\n    center,\n    circleTangency,\n    lineTangency,\n    theta\n  };\n};\nvar getSectorPath = _ref2 => {\n  var {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle\n  } = _ref2;\n  var angle = getDeltaAngle(startAngle, endAngle);\n\n  // When the angle of sector equals to 360, star point and end point coincide\n  var tempEndAngle = startAngle + angle;\n  var outerStartPoint = polarToCartesian(cx, cy, outerRadius, startAngle);\n  var outerEndPoint = polarToCartesian(cx, cy, outerRadius, tempEndAngle);\n  var path = \"M \".concat(outerStartPoint.x, \",\").concat(outerStartPoint.y, \"\\n    A \").concat(outerRadius, \",\").concat(outerRadius, \",0,\\n    \").concat(+(Math.abs(angle) > 180), \",\").concat(+(startAngle > tempEndAngle), \",\\n    \").concat(outerEndPoint.x, \",\").concat(outerEndPoint.y, \"\\n  \");\n  if (innerRadius > 0) {\n    var innerStartPoint = polarToCartesian(cx, cy, innerRadius, startAngle);\n    var innerEndPoint = polarToCartesian(cx, cy, innerRadius, tempEndAngle);\n    path += \"L \".concat(innerEndPoint.x, \",\").concat(innerEndPoint.y, \"\\n            A \").concat(innerRadius, \",\").concat(innerRadius, \",0,\\n            \").concat(+(Math.abs(angle) > 180), \",\").concat(+(startAngle <= tempEndAngle), \",\\n            \").concat(innerStartPoint.x, \",\").concat(innerStartPoint.y, \" Z\");\n  } else {\n    path += \"L \".concat(cx, \",\").concat(cy, \" Z\");\n  }\n  return path;\n};\nvar getSectorWithCorner = _ref3 => {\n  var {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    forceCornerRadius,\n    cornerIsExternal,\n    startAngle,\n    endAngle\n  } = _ref3;\n  var sign = mathSign(endAngle - startAngle);\n  var {\n    circleTangency: soct,\n    lineTangency: solt,\n    theta: sot\n  } = getTangentCircle({\n    cx,\n    cy,\n    radius: outerRadius,\n    angle: startAngle,\n    sign,\n    cornerRadius,\n    cornerIsExternal\n  });\n  var {\n    circleTangency: eoct,\n    lineTangency: eolt,\n    theta: eot\n  } = getTangentCircle({\n    cx,\n    cy,\n    radius: outerRadius,\n    angle: endAngle,\n    sign: -sign,\n    cornerRadius,\n    cornerIsExternal\n  });\n  var outerArcAngle = cornerIsExternal ? Math.abs(startAngle - endAngle) : Math.abs(startAngle - endAngle) - sot - eot;\n  if (outerArcAngle < 0) {\n    if (forceCornerRadius) {\n      return \"M \".concat(solt.x, \",\").concat(solt.y, \"\\n        a\").concat(cornerRadius, \",\").concat(cornerRadius, \",0,0,1,\").concat(cornerRadius * 2, \",0\\n        a\").concat(cornerRadius, \",\").concat(cornerRadius, \",0,0,1,\").concat(-cornerRadius * 2, \",0\\n      \");\n    }\n    return getSectorPath({\n      cx,\n      cy,\n      innerRadius,\n      outerRadius,\n      startAngle,\n      endAngle\n    });\n  }\n  var path = \"M \".concat(solt.x, \",\").concat(solt.y, \"\\n    A\").concat(cornerRadius, \",\").concat(cornerRadius, \",0,0,\").concat(+(sign < 0), \",\").concat(soct.x, \",\").concat(soct.y, \"\\n    A\").concat(outerRadius, \",\").concat(outerRadius, \",0,\").concat(+(outerArcAngle > 180), \",\").concat(+(sign < 0), \",\").concat(eoct.x, \",\").concat(eoct.y, \"\\n    A\").concat(cornerRadius, \",\").concat(cornerRadius, \",0,0,\").concat(+(sign < 0), \",\").concat(eolt.x, \",\").concat(eolt.y, \"\\n  \");\n  if (innerRadius > 0) {\n    var {\n      circleTangency: sict,\n      lineTangency: silt,\n      theta: sit\n    } = getTangentCircle({\n      cx,\n      cy,\n      radius: innerRadius,\n      angle: startAngle,\n      sign,\n      isExternal: true,\n      cornerRadius,\n      cornerIsExternal\n    });\n    var {\n      circleTangency: eict,\n      lineTangency: eilt,\n      theta: eit\n    } = getTangentCircle({\n      cx,\n      cy,\n      radius: innerRadius,\n      angle: endAngle,\n      sign: -sign,\n      isExternal: true,\n      cornerRadius,\n      cornerIsExternal\n    });\n    var innerArcAngle = cornerIsExternal ? Math.abs(startAngle - endAngle) : Math.abs(startAngle - endAngle) - sit - eit;\n    if (innerArcAngle < 0 && cornerRadius === 0) {\n      return \"\".concat(path, \"L\").concat(cx, \",\").concat(cy, \"Z\");\n    }\n    path += \"L\".concat(eilt.x, \",\").concat(eilt.y, \"\\n      A\").concat(cornerRadius, \",\").concat(cornerRadius, \",0,0,\").concat(+(sign < 0), \",\").concat(eict.x, \",\").concat(eict.y, \"\\n      A\").concat(innerRadius, \",\").concat(innerRadius, \",0,\").concat(+(innerArcAngle > 180), \",\").concat(+(sign > 0), \",\").concat(sict.x, \",\").concat(sict.y, \"\\n      A\").concat(cornerRadius, \",\").concat(cornerRadius, \",0,0,\").concat(+(sign < 0), \",\").concat(silt.x, \",\").concat(silt.y, \"Z\");\n  } else {\n    path += \"L\".concat(cx, \",\").concat(cy, \"Z\");\n  }\n  return path;\n};\nvar defaultProps = {\n  cx: 0,\n  cy: 0,\n  innerRadius: 0,\n  outerRadius: 0,\n  startAngle: 0,\n  endAngle: 0,\n  cornerRadius: 0,\n  forceCornerRadius: false,\n  cornerIsExternal: false\n};\nexport var Sector = sectorProps => {\n  var props = resolveDefaultProps(sectorProps, defaultProps);\n  var {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    forceCornerRadius,\n    cornerIsExternal,\n    startAngle,\n    endAngle,\n    className\n  } = props;\n  if (outerRadius < innerRadius || startAngle === endAngle) {\n    return null;\n  }\n  var layerClass = clsx('recharts-sector', className);\n  var deltaRadius = outerRadius - innerRadius;\n  var cr = getPercentValue(cornerRadius, deltaRadius, 0, true);\n  var path;\n  if (cr > 0 && Math.abs(startAngle - endAngle) < 360) {\n    path = getSectorWithCorner({\n      cx,\n      cy,\n      innerRadius,\n      outerRadius,\n      cornerRadius: Math.min(cr, deltaRadius / 2),\n      forceCornerRadius,\n      cornerIsExternal,\n      startAngle,\n      endAngle\n    });\n  } else {\n    path = getSectorPath({\n      cx,\n      cy,\n      innerRadius,\n      outerRadius,\n      startAngle,\n      endAngle\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(props, true), {\n    className: layerClass,\n    d: path\n  }));\n};", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});", "import * as React from 'react';\nimport { forwardRef } from 'react';\nimport { arrayTooltipSearcher } from '../state/optionsSlice';\nimport { CartesianChart } from './CartesianChart';\nvar allowedTooltipTypes = ['axis', 'item'];\nexport var BarChart = /*#__PURE__*/forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(CartesianChart, {\n    chartName: \"BarChart\",\n    defaultTooltipEventType: \"axis\",\n    validateTooltipEventTypes: allowedTooltipTypes,\n    tooltipPayloadSearcher: arrayTooltipSearcher,\n    categoricalChartProps: props,\n    ref: ref\n  });\n});", "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isPlainObject(object) {\n    if (typeof object !== 'object') {\n        return false;\n    }\n    if (object == null) {\n        return false;\n    }\n    if (Object.getPrototypeOf(object) === null) {\n        return true;\n    }\n    if (Object.prototype.toString.call(object) !== '[object Object]') {\n        const tag = object[Symbol.toStringTag];\n        if (tag == null) {\n            return false;\n        }\n        const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;\n        if (isTagReadonly) {\n            return false;\n        }\n        return object.toString() === `[object ${tag}]`;\n    }\n    let proto = object;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(object) === proto;\n}\n\nexports.isPlainObject = isPlainObject;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n/**\n * @fileOverview Rectangle\n */\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar getRectanglePath = (x, y, width, height, radius) => {\n  var maxRadius = Math.min(Math.abs(width) / 2, Math.abs(height) / 2);\n  var ySign = height >= 0 ? 1 : -1;\n  var xSign = width >= 0 ? 1 : -1;\n  var clockWise = height >= 0 && width >= 0 || height < 0 && width < 0 ? 1 : 0;\n  var path;\n  if (maxRadius > 0 && radius instanceof Array) {\n    var newRadius = [0, 0, 0, 0];\n    for (var i = 0, len = 4; i < len; i++) {\n      newRadius[i] = radius[i] > maxRadius ? maxRadius : radius[i];\n    }\n    path = \"M\".concat(x, \",\").concat(y + ySign * newRadius[0]);\n    if (newRadius[0] > 0) {\n      path += \"A \".concat(newRadius[0], \",\").concat(newRadius[0], \",0,0,\").concat(clockWise, \",\").concat(x + xSign * newRadius[0], \",\").concat(y);\n    }\n    path += \"L \".concat(x + width - xSign * newRadius[1], \",\").concat(y);\n    if (newRadius[1] > 0) {\n      path += \"A \".concat(newRadius[1], \",\").concat(newRadius[1], \",0,0,\").concat(clockWise, \",\\n        \").concat(x + width, \",\").concat(y + ySign * newRadius[1]);\n    }\n    path += \"L \".concat(x + width, \",\").concat(y + height - ySign * newRadius[2]);\n    if (newRadius[2] > 0) {\n      path += \"A \".concat(newRadius[2], \",\").concat(newRadius[2], \",0,0,\").concat(clockWise, \",\\n        \").concat(x + width - xSign * newRadius[2], \",\").concat(y + height);\n    }\n    path += \"L \".concat(x + xSign * newRadius[3], \",\").concat(y + height);\n    if (newRadius[3] > 0) {\n      path += \"A \".concat(newRadius[3], \",\").concat(newRadius[3], \",0,0,\").concat(clockWise, \",\\n        \").concat(x, \",\").concat(y + height - ySign * newRadius[3]);\n    }\n    path += 'Z';\n  } else if (maxRadius > 0 && radius === +radius && radius > 0) {\n    var _newRadius = Math.min(maxRadius, radius);\n    path = \"M \".concat(x, \",\").concat(y + ySign * _newRadius, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + xSign * _newRadius, \",\").concat(y, \"\\n            L \").concat(x + width - xSign * _newRadius, \",\").concat(y, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + width, \",\").concat(y + ySign * _newRadius, \"\\n            L \").concat(x + width, \",\").concat(y + height - ySign * _newRadius, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + width - xSign * _newRadius, \",\").concat(y + height, \"\\n            L \").concat(x + xSign * _newRadius, \",\").concat(y + height, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x, \",\").concat(y + height - ySign * _newRadius, \" Z\");\n  } else {\n    path = \"M \".concat(x, \",\").concat(y, \" h \").concat(width, \" v \").concat(height, \" h \").concat(-width, \" Z\");\n  }\n  return path;\n};\nvar defaultProps = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  // The radius of border\n  // The radius of four corners when radius is a number\n  // The radius of left-top, right-top, right-bottom, left-bottom when radius is an array\n  radius: 0,\n  isAnimationActive: false,\n  isUpdateAnimationActive: false,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n};\nexport var Rectangle = rectangleProps => {\n  var props = resolveDefaultProps(rectangleProps, defaultProps);\n  var pathRef = useRef(null);\n  var [totalLength, setTotalLength] = useState(-1);\n  useEffect(() => {\n    if (pathRef.current && pathRef.current.getTotalLength) {\n      try {\n        var pathTotalLength = pathRef.current.getTotalLength();\n        if (pathTotalLength) {\n          setTotalLength(pathTotalLength);\n        }\n      } catch (_unused) {\n        // calculate total length error\n      }\n    }\n  }, []);\n  var {\n    x,\n    y,\n    width,\n    height,\n    radius,\n    className\n  } = props;\n  var {\n    animationEasing,\n    animationDuration,\n    animationBegin,\n    isAnimationActive,\n    isUpdateAnimationActive\n  } = props;\n  if (x !== +x || y !== +y || width !== +width || height !== +height || width === 0 || height === 0) {\n    return null;\n  }\n  var layerClass = clsx('recharts-rectangle', className);\n  if (!isUpdateAnimationActive) {\n    return /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(props, true), {\n      className: layerClass,\n      d: getRectanglePath(x, y, width, height, radius)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Animate, {\n    canBegin: totalLength > 0,\n    from: {\n      width,\n      height,\n      x,\n      y\n    },\n    to: {\n      width,\n      height,\n      x,\n      y\n    },\n    duration: animationDuration\n    // @ts-expect-error TODO - fix the type error\n    ,\n    animationEasing: animationEasing,\n    isActive: isUpdateAnimationActive\n  }, _ref => {\n    var {\n      width: currWidth,\n      height: currHeight,\n      x: currX,\n      y: currY\n    } = _ref;\n    return /*#__PURE__*/React.createElement(Animate, {\n      canBegin: totalLength > 0\n      // @ts-expect-error TODO - fix the type error\n      ,\n      from: \"0px \".concat(totalLength === -1 ? 1 : totalLength, \"px\")\n      // @ts-expect-error TODO - fix the type error\n      ,\n      to: \"\".concat(totalLength, \"px 0px\"),\n      attributeName: \"strokeDasharray\",\n      begin: animationBegin,\n      duration: animationDuration,\n      isActive: isAnimationActive,\n      easing: animationEasing\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(props, true), {\n      className: layerClass,\n      d: getRectanglePath(currX, currY, currWidth, currHeight, radius),\n      ref: pathRef\n    })));\n  });\n};", "function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Default Tooltip Content\n */\n\nimport * as React from 'react';\nimport sortBy from 'es-toolkit/compat/sortBy';\nimport { clsx } from 'clsx';\nimport { isNullish, isNumOrStr } from '../util/DataUtils';\nfunction defaultFormatter(value) {\n  return Array.isArray(value) && isNumOrStr(value[0]) && isNumOrStr(value[1]) ? value.join(' ~ ') : value;\n}\nexport var DefaultTooltipContent = props => {\n  var {\n    separator = ' : ',\n    contentStyle = {},\n    itemStyle = {},\n    labelStyle = {},\n    payload,\n    formatter,\n    itemSorter,\n    wrapperClassName,\n    labelClassName,\n    label,\n    labelFormatter,\n    accessibilityLayer = false\n  } = props;\n  var renderContent = () => {\n    if (payload && payload.length) {\n      var listStyle = {\n        padding: 0,\n        margin: 0\n      };\n      var items = (itemSorter ? sortBy(payload, itemSorter) : payload).map((entry, i) => {\n        if (entry.type === 'none') {\n          return null;\n        }\n        var finalFormatter = entry.formatter || formatter || defaultFormatter;\n        var {\n          value,\n          name\n        } = entry;\n        var finalValue = value;\n        var finalName = name;\n        if (finalFormatter) {\n          var formatted = finalFormatter(value, name, entry, i, payload);\n          if (Array.isArray(formatted)) {\n            [finalValue, finalName] = formatted;\n          } else if (formatted != null) {\n            finalValue = formatted;\n          } else {\n            return null;\n          }\n        }\n        var finalItemStyle = _objectSpread({\n          display: 'block',\n          paddingTop: 4,\n          paddingBottom: 4,\n          color: entry.color || '#000'\n        }, itemStyle);\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(\"li\", {\n            className: \"recharts-tooltip-item\",\n            key: \"tooltip-item-\".concat(i),\n            style: finalItemStyle\n          }, isNumOrStr(finalName) ? /*#__PURE__*/React.createElement(\"span\", {\n            className: \"recharts-tooltip-item-name\"\n          }, finalName) : null, isNumOrStr(finalName) ? /*#__PURE__*/React.createElement(\"span\", {\n            className: \"recharts-tooltip-item-separator\"\n          }, separator) : null, /*#__PURE__*/React.createElement(\"span\", {\n            className: \"recharts-tooltip-item-value\"\n          }, finalValue), /*#__PURE__*/React.createElement(\"span\", {\n            className: \"recharts-tooltip-item-unit\"\n          }, entry.unit || ''))\n        );\n      });\n      return /*#__PURE__*/React.createElement(\"ul\", {\n        className: \"recharts-tooltip-item-list\",\n        style: listStyle\n      }, items);\n    }\n    return null;\n  };\n  var finalStyle = _objectSpread({\n    margin: 0,\n    padding: 10,\n    backgroundColor: '#fff',\n    border: '1px solid #ccc',\n    whiteSpace: 'nowrap'\n  }, contentStyle);\n  var finalLabelStyle = _objectSpread({\n    margin: 0\n  }, labelStyle);\n  var hasLabel = !isNullish(label);\n  var finalLabel = hasLabel ? label : '';\n  var wrapperCN = clsx('recharts-default-tooltip', wrapperClassName);\n  var labelCN = clsx('recharts-tooltip-label', labelClassName);\n  if (hasLabel && labelFormatter && payload !== undefined && payload !== null) {\n    finalLabel = labelFormatter(label, payload);\n  }\n  var accessibilityAttributes = accessibilityLayer ? {\n    role: 'status',\n    'aria-live': 'assertive'\n  } : {};\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: wrapperCN,\n    style: finalStyle\n  }, accessibilityAttributes), /*#__PURE__*/React.createElement(\"p\", {\n    className: labelCN,\n    style: finalLabelStyle\n  }, /*#__PURE__*/React.isValidElement(finalLabel) ? finalLabel : \"\".concat(finalLabel)), renderContent());\n};", "import { clsx } from 'clsx';\nimport { isNumber } from '../DataUtils';\nvar CSS_CLASS_PREFIX = 'recharts-tooltip-wrapper';\nvar TOOLTIP_HIDDEN = {\n  visibility: 'hidden'\n};\nexport function getTooltipCSSClassName(_ref) {\n  var {\n    coordinate,\n    translateX,\n    translateY\n  } = _ref;\n  return clsx(CSS_CLASS_PREFIX, {\n    [\"\".concat(CSS_CLASS_PREFIX, \"-right\")]: isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX >= coordinate.x,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-left\")]: isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX < coordinate.x,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-bottom\")]: isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY >= coordinate.y,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-top\")]: isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY < coordinate.y\n  });\n}\nexport function getTooltipTranslateXY(_ref2) {\n  var {\n    allowEscapeViewBox,\n    coordinate,\n    key,\n    offsetTopLeft,\n    position,\n    reverseDirection,\n    tooltipDimension,\n    viewBox,\n    viewBoxDimension\n  } = _ref2;\n  if (position && isNumber(position[key])) {\n    return position[key];\n  }\n  var negative = coordinate[key] - tooltipDimension - (offsetTopLeft > 0 ? offsetTopLeft : 0);\n  var positive = coordinate[key] + offsetTopLeft;\n  if (allowEscapeViewBox[key]) {\n    return reverseDirection[key] ? negative : positive;\n  }\n  var viewBoxKey = viewBox[key];\n  if (viewBoxKey == null) {\n    return 0;\n  }\n  if (reverseDirection[key]) {\n    var _tooltipBoundary = negative;\n    var _viewBoxBoundary = viewBoxKey;\n    if (_tooltipBoundary < _viewBoxBoundary) {\n      return Math.max(positive, viewBoxKey);\n    }\n    return Math.max(negative, viewBoxKey);\n  }\n  if (viewBoxDimension == null) {\n    return 0;\n  }\n  var tooltipBoundary = positive + tooltipDimension;\n  var viewBoxBoundary = viewBoxKey + viewBoxDimension;\n  if (tooltipBoundary > viewBoxBoundary) {\n    return Math.max(negative, viewBoxKey);\n  }\n  return Math.max(positive, viewBoxKey);\n}\nexport function getTransformStyle(_ref3) {\n  var {\n    translateX,\n    translateY,\n    useTranslate3d\n  } = _ref3;\n  return {\n    transform: useTranslate3d ? \"translate3d(\".concat(translateX, \"px, \").concat(translateY, \"px, 0)\") : \"translate(\".concat(translateX, \"px, \").concat(translateY, \"px)\")\n  };\n}\nexport function getTooltipTranslate(_ref4) {\n  var {\n    allowEscapeViewBox,\n    coordinate,\n    offsetTopLeft,\n    position,\n    reverseDirection,\n    tooltipBox,\n    useTranslate3d,\n    viewBox\n  } = _ref4;\n  var cssProperties, translateX, translateY;\n  if (tooltipBox.height > 0 && tooltipBox.width > 0 && coordinate) {\n    translateX = getTooltipTranslateXY({\n      allowEscapeViewBox,\n      coordinate,\n      key: 'x',\n      offsetTopLeft,\n      position,\n      reverseDirection,\n      tooltipDimension: tooltipBox.width,\n      viewBox,\n      viewBoxDimension: viewBox.width\n    });\n    translateY = getTooltipTranslateXY({\n      allowEscapeViewBox,\n      coordinate,\n      key: 'y',\n      offsetTopLeft,\n      position,\n      reverseDirection,\n      tooltipDimension: tooltipBox.height,\n      viewBox,\n      viewBoxDimension: viewBox.height\n    });\n    cssProperties = getTransformStyle({\n      translateX,\n      translateY,\n      useTranslate3d\n    });\n  } else {\n    cssProperties = TOOLTIP_HIDDEN;\n  }\n  return {\n    cssProperties,\n    cssClasses: getTooltipCSSClassName({\n      translateX,\n      translateY,\n      coordinate\n    })\n  };\n}", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { PureComponent } from 'react';\nimport { getTooltipTranslate } from '../util/tooltip/translate';\nexport class TooltipBoundingBox extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      dismissed: false,\n      dismissedAtCoordinate: {\n        x: 0,\n        y: 0\n      }\n    });\n    _defineProperty(this, \"handleKeyDown\", event => {\n      if (event.key === 'Escape') {\n        var _this$props$coordinat, _this$props$coordinat2, _this$props$coordinat3, _this$props$coordinat4;\n        this.setState({\n          dismissed: true,\n          dismissedAtCoordinate: {\n            x: (_this$props$coordinat = (_this$props$coordinat2 = this.props.coordinate) === null || _this$props$coordinat2 === void 0 ? void 0 : _this$props$coordinat2.x) !== null && _this$props$coordinat !== void 0 ? _this$props$coordinat : 0,\n            y: (_this$props$coordinat3 = (_this$props$coordinat4 = this.props.coordinate) === null || _this$props$coordinat4 === void 0 ? void 0 : _this$props$coordinat4.y) !== null && _this$props$coordinat3 !== void 0 ? _this$props$coordinat3 : 0\n          }\n        });\n      }\n    });\n  }\n  componentDidMount() {\n    document.addEventListener('keydown', this.handleKeyDown);\n  }\n  componentWillUnmount() {\n    document.removeEventListener('keydown', this.handleKeyDown);\n  }\n  componentDidUpdate() {\n    var _this$props$coordinat5, _this$props$coordinat6;\n    if (!this.state.dismissed) {\n      return;\n    }\n    if (((_this$props$coordinat5 = this.props.coordinate) === null || _this$props$coordinat5 === void 0 ? void 0 : _this$props$coordinat5.x) !== this.state.dismissedAtCoordinate.x || ((_this$props$coordinat6 = this.props.coordinate) === null || _this$props$coordinat6 === void 0 ? void 0 : _this$props$coordinat6.y) !== this.state.dismissedAtCoordinate.y) {\n      this.state.dismissed = false;\n    }\n  }\n  render() {\n    var {\n      active,\n      allowEscapeViewBox,\n      animationDuration,\n      animationEasing,\n      children,\n      coordinate,\n      hasPayload,\n      isAnimationActive,\n      offset,\n      position,\n      reverseDirection,\n      useTranslate3d,\n      viewBox,\n      wrapperStyle,\n      lastBoundingBox,\n      innerRef,\n      hasPortalFromProps\n    } = this.props;\n    var {\n      cssClasses,\n      cssProperties\n    } = getTooltipTranslate({\n      allowEscapeViewBox,\n      coordinate,\n      offsetTopLeft: offset,\n      position,\n      reverseDirection,\n      tooltipBox: {\n        height: lastBoundingBox.height,\n        width: lastBoundingBox.width\n      },\n      useTranslate3d,\n      viewBox\n    });\n\n    // do not use absolute styles if the user has passed a custom portal prop\n    var positionStyles = hasPortalFromProps ? {} : _objectSpread(_objectSpread({\n      transition: isAnimationActive && active ? \"transform \".concat(animationDuration, \"ms \").concat(animationEasing) : undefined\n    }, cssProperties), {}, {\n      pointerEvents: 'none',\n      visibility: !this.state.dismissed && active && hasPayload ? 'visible' : 'hidden',\n      position: 'absolute',\n      top: 0,\n      left: 0\n    });\n    var outerStyle = _objectSpread(_objectSpread({}, positionStyles), {}, {\n      visibility: !this.state.dismissed && active && hasPayload ? 'visible' : 'hidden'\n    }, wrapperStyle);\n    return (\n      /*#__PURE__*/\n      // This element allow listening to the `Escape` key. See https://github.com/recharts/recharts/pull/2925\n      React.createElement(\"div\", {\n        // @ts-expect-error typescript library does not recognize xmlns attribute, but it's required for an HTML chunk inside SVG.\n        xmlns: \"http://www.w3.org/1999/xhtml\",\n        tabIndex: -1,\n        className: cssClasses,\n        style: outerStyle,\n        ref: innerRef\n      }, children)\n    );\n  }\n}", "var _excluded = [\"x\", \"y\", \"top\", \"left\", \"width\", \"height\", \"className\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Cross\n */\nimport * as React from 'react';\nimport { clsx } from 'clsx';\nimport { isNumber } from '../util/DataUtils';\nimport { filterProps } from '../util/ReactUtils';\nvar getPath = (x, y, width, height, top, left) => {\n  return \"M\".concat(x, \",\").concat(top, \"v\").concat(height, \"M\").concat(left, \",\").concat(y, \"h\").concat(width);\n};\nexport var Cross = _ref => {\n  var {\n      x = 0,\n      y = 0,\n      top = 0,\n      left = 0,\n      width = 0,\n      height = 0,\n      className\n    } = _ref,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var props = _objectSpread({\n    x,\n    y,\n    top,\n    left,\n    width,\n    height\n  }, rest);\n  if (!isNumber(x) || !isNumber(y) || !isNumber(width) || !isNumber(height) || !isNumber(top) || !isNumber(left)) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(props, true), {\n    className: clsx('recharts-cross', className),\n    d: getPath(x, y, width, height, top, left)\n  }));\n};", "import { polarToCartesian } from '../PolarUtils';\n/**\n * Only applicable for radial layouts\n * @param {Object} activeCoordinate ChartCoordinate\n * @returns {Object} RadialCursorPoints\n */\nexport function getRadialCursorPoints(activeCoordinate) {\n  var {\n    cx,\n    cy,\n    radius,\n    startAngle,\n    endAngle\n  } = activeCoordinate;\n  var startPoint = polarToCartesian(cx, cy, radius, startAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, endAngle);\n  return {\n    points: [startPoint, endPoint],\n    cx,\n    cy,\n    radius,\n    startAngle,\n    endAngle\n  };\n}", "import { polarToCartesian } from '../PolarUtils';\nimport { getRadialCursorPoints } from './getRadialCursorPoints';\nexport function getCursorPoints(layout, activeCoordinate, offset) {\n  var x1, y1, x2, y2;\n  if (layout === 'horizontal') {\n    x1 = activeCoordinate.x;\n    x2 = x1;\n    y1 = offset.top;\n    y2 = offset.top + offset.height;\n  } else if (layout === 'vertical') {\n    y1 = activeCoordinate.y;\n    y2 = y1;\n    x1 = offset.left;\n    x2 = offset.left + offset.width;\n  } else if (activeCoordinate.cx != null && activeCoordinate.cy != null) {\n    if (layout === 'centric') {\n      var {\n        cx,\n        cy,\n        innerRadius,\n        outerRadius,\n        angle\n      } = activeCoordinate;\n      var innerPoint = polarToCartesian(cx, cy, innerRadius, angle);\n      var outerPoint = polarToCartesian(cx, cy, outerRadius, angle);\n      x1 = innerPoint.x;\n      y1 = innerPoint.y;\n      x2 = outerPoint.x;\n      y2 = outerPoint.y;\n    } else {\n      // @ts-expect-error TODO the state is marked as containing Coordinate but actually in polar charts it contains PolarCoordinate, we should keep the polar state separate\n      return getRadialCursorPoints(activeCoordinate);\n    }\n  }\n  return [{\n    x: x1,\n    y: y1\n  }, {\n    x: x2,\n    y: y2\n  }];\n}", "function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { cloneElement, createElement, isValidElement } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Cross } from '../shape/Cross';\nimport { getCursorRectangle } from '../util/cursor/getCursorRectangle';\nimport { Rectangle } from '../shape/Rectangle';\nimport { getRadialCursorPoints } from '../util/cursor/getRadialCursorPoints';\nimport { Sector } from '../shape/Sector';\nimport { getCursorPoints } from '../util/cursor/getCursorPoints';\nimport { filterProps } from '../util/ReactUtils';\nimport { useChartLayout, useOffset } from '../context/chartLayoutContext';\nimport { useTooltipAxisBandSize } from '../context/useTooltipAxis';\nimport { useChartName } from '../state/selectors/selectors';\n\n/**\n * If set false, no cursor will be drawn when tooltip is active.\n * If set an object, the option is the configuration of cursor.\n * If set a React element, the option is the custom react element of drawing cursor\n */\n\nexport function CursorInternal(props) {\n  var {\n    coordinate,\n    payload,\n    index,\n    offset,\n    tooltipAxisBandSize,\n    layout,\n    cursor,\n    tooltipEventType,\n    chartName\n  } = props;\n\n  // The cursor is a part of the Tooltip, and it should be shown (by default) when the Tooltip is active.\n  var activeCoordinate = coordinate;\n  var activePayload = payload;\n  var activeTooltipIndex = index;\n  if (!cursor || !activeCoordinate || chartName !== 'ScatterChart' && tooltipEventType !== 'axis') {\n    return null;\n  }\n  var restProps, cursorComp;\n  if (chartName === 'ScatterChart') {\n    restProps = activeCoordinate;\n    cursorComp = Cross;\n  } else if (chartName === 'BarChart') {\n    restProps = getCursorRectangle(layout, activeCoordinate, offset, tooltipAxisBandSize);\n    cursorComp = Rectangle;\n  } else if (layout === 'radial') {\n    // @ts-expect-error TODO the state is marked as containing Coordinate but actually in polar charts it contains PolarCoordinate, we should keep the polar state separate\n    var {\n      cx,\n      cy,\n      radius,\n      startAngle,\n      endAngle\n    } = getRadialCursorPoints(activeCoordinate);\n    restProps = {\n      cx,\n      cy,\n      startAngle,\n      endAngle,\n      innerRadius: radius,\n      outerRadius: radius\n    };\n    cursorComp = Sector;\n  } else {\n    restProps = {\n      points: getCursorPoints(layout, activeCoordinate, offset)\n    };\n    cursorComp = Curve;\n  }\n  var extraClassName = typeof cursor === 'object' && 'className' in cursor ? cursor.className : undefined;\n  var cursorProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n    stroke: '#ccc',\n    pointerEvents: 'none'\n  }, offset), restProps), filterProps(cursor, false)), {}, {\n    payload: activePayload,\n    payloadIndex: activeTooltipIndex,\n    className: clsx('recharts-tooltip-cursor', extraClassName)\n  });\n  return /*#__PURE__*/isValidElement(cursor) ? /*#__PURE__*/cloneElement(cursor, cursorProps) : /*#__PURE__*/createElement(cursorComp, cursorProps);\n}\n\n/*\n * Cursor is the background, or a highlight,\n * that shows when user mouses over or activates\n * an area.\n *\n * It usually shows together with a tooltip\n * to emphasise which part of the chart does the tooltip refer to.\n */\nexport function Cursor(props) {\n  var tooltipAxisBandSize = useTooltipAxisBandSize();\n  var offset = useOffset();\n  var layout = useChartLayout();\n  var chartName = useChartName();\n  return /*#__PURE__*/React.createElement(CursorInternal, _extends({}, props, {\n    coordinate: props.coordinate,\n    index: props.index,\n    payload: props.payload,\n    offset: offset,\n    layout: layout,\n    tooltipAxisBandSize: tooltipAxisBandSize,\n    chartName: chartName\n  }));\n}", "export function getCursorRectangle(layout, activeCoordinate, offset, tooltipAxisBandSize) {\n  var halfSize = tooltipAxisBandSize / 2;\n  return {\n    stroke: 'none',\n    fill: '#ccc',\n    x: layout === 'horizontal' ? activeCoordinate.x - halfSize : offset.left + 0.5,\n    y: layout === 'horizontal' ? offset.top + 0.5 : activeCoordinate.y - halfSize,\n    width: layout === 'horizontal' ? tooltipAxisBandSize : offset.width - 1,\n    height: layout === 'horizontal' ? offset.height - 1 : tooltipAxisBandSize\n  };\n}", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { DefaultTooltipContent } from './DefaultTooltipContent';\nimport { TooltipBoundingBox } from './TooltipBoundingBox';\nimport { Global } from '../util/Global';\nimport { getUniqPayload } from '../util/payload/getUniqPayload';\nimport { useViewBox } from '../context/chartLayoutContext';\nimport { useAccessibilityLayer } from '../context/accessibilityContext';\nimport { useElementOffset } from '../util/useElementOffset';\nimport { Cursor } from './Cursor';\nimport { selectActiveCoordinate, selectActiveLabel, selectIsTooltipActive, selectTooltipPayload } from '../state/selectors/selectors';\nimport { useTooltipPortal } from '../context/tooltipPortalContext';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { setTooltipSettingsState } from '../state/tooltipSlice';\nimport { useTooltipChartSynchronisation } from '../synchronisation/useChartSynchronisation';\nimport { useTooltipEventType } from '../state/selectors/selectTooltipEventType';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nfunction defaultUniqBy(entry) {\n  return entry.dataKey;\n}\nfunction renderContent(content, props) {\n  if (/*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.cloneElement(content, props);\n  }\n  if (typeof content === 'function') {\n    return /*#__PURE__*/React.createElement(content, props);\n  }\n  return /*#__PURE__*/React.createElement(DefaultTooltipContent, props);\n}\nvar emptyPayload = [];\nvar defaultTooltipProps = {\n  allowEscapeViewBox: {\n    x: false,\n    y: false\n  },\n  animationDuration: 400,\n  animationEasing: 'ease',\n  axisId: 0,\n  contentStyle: {},\n  cursor: true,\n  filterNull: true,\n  isAnimationActive: !Global.isSsr,\n  itemSorter: 'name',\n  itemStyle: {},\n  labelStyle: {},\n  offset: 10,\n  reverseDirection: {\n    x: false,\n    y: false\n  },\n  separator: ' : ',\n  trigger: 'hover',\n  useTranslate3d: false,\n  wrapperStyle: {}\n};\nexport function Tooltip(outsideProps) {\n  var props = resolveDefaultProps(outsideProps, defaultTooltipProps);\n  var {\n    active: activeFromProps,\n    allowEscapeViewBox,\n    animationDuration,\n    animationEasing,\n    content,\n    filterNull,\n    isAnimationActive,\n    offset,\n    payloadUniqBy,\n    position,\n    reverseDirection,\n    useTranslate3d,\n    wrapperStyle,\n    cursor,\n    shared,\n    trigger,\n    defaultIndex,\n    portal: portalFromProps,\n    axisId\n  } = props;\n  var dispatch = useAppDispatch();\n  var defaultIndexAsString = typeof defaultIndex === 'number' ? String(defaultIndex) : defaultIndex;\n  useEffect(() => {\n    dispatch(setTooltipSettingsState({\n      shared,\n      trigger,\n      axisId,\n      active: activeFromProps,\n      defaultIndex: defaultIndexAsString\n    }));\n  }, [dispatch, shared, trigger, axisId, activeFromProps, defaultIndexAsString]);\n  var viewBox = useViewBox();\n  var accessibilityLayer = useAccessibilityLayer();\n  var tooltipEventType = useTooltipEventType(shared);\n  var {\n    activeIndex,\n    isActive\n  } = useAppSelector(state => selectIsTooltipActive(state, tooltipEventType, trigger, defaultIndexAsString));\n  var payloadFromRedux = useAppSelector(state => selectTooltipPayload(state, tooltipEventType, trigger, defaultIndexAsString));\n  var labelFromRedux = useAppSelector(state => selectActiveLabel(state, tooltipEventType, trigger, defaultIndexAsString));\n  var coordinate = useAppSelector(state => selectActiveCoordinate(state, tooltipEventType, trigger, defaultIndexAsString));\n  var payload = payloadFromRedux;\n  var tooltipPortalFromContext = useTooltipPortal();\n  /*\n   * The user can set `active=true` on the Tooltip in which case the Tooltip will stay always active,\n   * or `active=false` in which case the Tooltip never shows.\n   *\n   * If the `active` prop is not defined then it will show and hide based on mouse or keyboard activity.\n   */\n  var finalIsActive = activeFromProps !== null && activeFromProps !== void 0 ? activeFromProps : isActive;\n  var [lastBoundingBox, updateBoundingBox] = useElementOffset([payload, finalIsActive]);\n  var finalLabel = tooltipEventType === 'axis' ? labelFromRedux : undefined;\n  useTooltipChartSynchronisation(tooltipEventType, trigger, coordinate, finalLabel, activeIndex, finalIsActive);\n  var tooltipPortal = portalFromProps !== null && portalFromProps !== void 0 ? portalFromProps : tooltipPortalFromContext;\n  if (tooltipPortal == null) {\n    return null;\n  }\n  var finalPayload = payload !== null && payload !== void 0 ? payload : emptyPayload;\n  if (!finalIsActive) {\n    finalPayload = emptyPayload;\n  }\n  if (filterNull && finalPayload.length) {\n    finalPayload = getUniqPayload(payload.filter(entry => entry.value != null && (entry.hide !== true || props.includeHidden)), payloadUniqBy, defaultUniqBy);\n  }\n  var hasPayload = finalPayload.length > 0;\n  var tooltipElement = /*#__PURE__*/React.createElement(TooltipBoundingBox, {\n    allowEscapeViewBox: allowEscapeViewBox,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive,\n    active: finalIsActive,\n    coordinate: coordinate,\n    hasPayload: hasPayload,\n    offset: offset,\n    position: position,\n    reverseDirection: reverseDirection,\n    useTranslate3d: useTranslate3d,\n    viewBox: viewBox,\n    wrapperStyle: wrapperStyle,\n    lastBoundingBox: lastBoundingBox,\n    innerRef: updateBoundingBox,\n    hasPortalFromProps: Boolean(portalFromProps)\n  }, renderContent(content, _objectSpread(_objectSpread({}, props), {}, {\n    // @ts-expect-error renderContent method expects the payload to be mutable, TODO make it immutable\n    payload: finalPayload,\n    label: finalLabel,\n    active: finalIsActive,\n    coordinate,\n    accessibilityLayer\n  })));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/createPortal(tooltipElement, tooltipPortal), finalIsActive && /*#__PURE__*/React.createElement(Cursor, {\n    cursor: cursor,\n    tooltipEventType: tooltipEventType,\n    coordinate: coordinate,\n    payload: payload,\n    index: activeIndex\n  }));\n}", "/**\n * @fileOverview Cross\n */\n\nexport var Cell = _props => null;\nCell.displayName = 'Cell';", "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n", "function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n/**\n * @fileOverview Rectangle\n */\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar getTrapezoidPath = (x, y, upperWidth, lowerWidth, height) => {\n  var widthGap = upperWidth - lowerWidth;\n  var path;\n  path = \"M \".concat(x, \",\").concat(y);\n  path += \"L \".concat(x + upperWidth, \",\").concat(y);\n  path += \"L \".concat(x + upperWidth - widthGap / 2, \",\").concat(y + height);\n  path += \"L \".concat(x + upperWidth - widthGap / 2 - lowerWidth, \",\").concat(y + height);\n  path += \"L \".concat(x, \",\").concat(y, \" Z\");\n  return path;\n};\nvar defaultProps = {\n  x: 0,\n  y: 0,\n  upperWidth: 0,\n  lowerWidth: 0,\n  height: 0,\n  isUpdateAnimationActive: false,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n};\nexport var Trapezoid = props => {\n  var trapezoidProps = resolveDefaultProps(props, defaultProps);\n  var pathRef = useRef();\n  var [totalLength, setTotalLength] = useState(-1);\n  useEffect(() => {\n    if (pathRef.current && pathRef.current.getTotalLength) {\n      try {\n        var pathTotalLength = pathRef.current.getTotalLength();\n        if (pathTotalLength) {\n          setTotalLength(pathTotalLength);\n        }\n      } catch (_unused) {\n        // calculate total length error\n      }\n    }\n  }, []);\n  var {\n    x,\n    y,\n    upperWidth,\n    lowerWidth,\n    height,\n    className\n  } = trapezoidProps;\n  var {\n    animationEasing,\n    animationDuration,\n    animationBegin,\n    isUpdateAnimationActive\n  } = trapezoidProps;\n  if (x !== +x || y !== +y || upperWidth !== +upperWidth || lowerWidth !== +lowerWidth || height !== +height || upperWidth === 0 && lowerWidth === 0 || height === 0) {\n    return null;\n  }\n  var layerClass = clsx('recharts-trapezoid', className);\n  if (!isUpdateAnimationActive) {\n    return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(trapezoidProps, true), {\n      className: layerClass,\n      d: getTrapezoidPath(x, y, upperWidth, lowerWidth, height)\n    })));\n  }\n  return /*#__PURE__*/React.createElement(Animate, {\n    canBegin: totalLength > 0,\n    from: {\n      upperWidth: 0,\n      lowerWidth: 0,\n      height,\n      x,\n      y\n    },\n    to: {\n      upperWidth,\n      lowerWidth,\n      height,\n      x,\n      y\n    },\n    duration: animationDuration\n    // @ts-expect-error TODO - fix the type error\n    ,\n    animationEasing: animationEasing,\n    isActive: isUpdateAnimationActive\n  }, _ref => {\n    var {\n      upperWidth: currUpperWidth,\n      lowerWidth: currLowerWidth,\n      height: currHeight,\n      x: currX,\n      y: currY\n    } = _ref;\n    return /*#__PURE__*/React.createElement(Animate, {\n      canBegin: totalLength > 0\n      // @ts-expect-error TODO - fix the type error\n      ,\n      from: \"0px \".concat(totalLength === -1 ? 1 : totalLength, \"px\")\n      // @ts-expect-error TODO - fix the type error\n      ,\n      to: \"\".concat(totalLength, \"px 0px\"),\n      attributeName: \"strokeDasharray\",\n      begin: animationBegin,\n      duration: animationDuration,\n      easing: animationEasing\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(trapezoidProps, true), {\n      className: layerClass,\n      d: getTrapezoidPath(currX, currY, currUpperWidth, currLowerWidth, currHeight),\n      ref: pathRef\n    })));\n  });\n};", "var _excluded = [\"option\", \"shapeType\", \"propTransformer\", \"activeClassName\", \"isActive\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { cloneElement, isValidElement } from 'react';\nimport isPlainObject from 'es-toolkit/compat/isPlainObject';\nimport { Rectangle } from '../shape/Rectangle';\nimport { Trapezoid } from '../shape/Trapezoid';\nimport { Sector } from '../shape/Sector';\nimport { Layer } from '../container/Layer';\nimport { Symbols } from '../shape/Symbols';\n\n/**\n * This is an abstraction for rendering a user defined prop for a customized shape in several forms.\n *\n * <Shape /> is the root and will handle taking in:\n *  - an object of svg properties\n *  - a boolean\n *  - a render prop(inline function that returns jsx)\n *  - a React element\n *\n * <ShapeSelector /> is a subcomponent of <Shape /> and used to match a component\n * to the value of props.shapeType that is passed to the root.\n *\n */\n\nfunction defaultPropTransformer(option, props) {\n  return _objectSpread(_objectSpread({}, props), option);\n}\nfunction isSymbolsProps(shapeType, _elementProps) {\n  return shapeType === 'symbols';\n}\nfunction ShapeSelector(_ref) {\n  var {\n    shapeType,\n    elementProps\n  } = _ref;\n  switch (shapeType) {\n    case 'rectangle':\n      return /*#__PURE__*/React.createElement(Rectangle, elementProps);\n    case 'trapezoid':\n      return /*#__PURE__*/React.createElement(Trapezoid, elementProps);\n    case 'sector':\n      return /*#__PURE__*/React.createElement(Sector, elementProps);\n    case 'symbols':\n      if (isSymbolsProps(shapeType, elementProps)) {\n        return /*#__PURE__*/React.createElement(Symbols, elementProps);\n      }\n      break;\n    default:\n      return null;\n  }\n}\nexport function getPropsFromShapeOption(option) {\n  if (/*#__PURE__*/isValidElement(option)) {\n    return option.props;\n  }\n  return option;\n}\nexport function Shape(_ref2) {\n  var {\n      option,\n      shapeType,\n      propTransformer = defaultPropTransformer,\n      activeClassName = 'recharts-active-shape',\n      isActive\n    } = _ref2,\n    props = _objectWithoutProperties(_ref2, _excluded);\n  var shape;\n  if (/*#__PURE__*/isValidElement(option)) {\n    shape = /*#__PURE__*/cloneElement(option, _objectSpread(_objectSpread({}, props), getPropsFromShapeOption(option)));\n  } else if (typeof option === 'function') {\n    shape = option(props);\n  } else if (isPlainObject(option) && typeof option !== 'boolean') {\n    var nextProps = propTransformer(option, props);\n    shape = /*#__PURE__*/React.createElement(ShapeSelector, {\n      shapeType: shapeType,\n      elementProps: nextProps\n    });\n  } else {\n    var elementProps = props;\n    shape = /*#__PURE__*/React.createElement(ShapeSelector, {\n      shapeType: shapeType,\n      elementProps: elementProps\n    });\n  }\n  if (isActive) {\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: activeClassName\n    }, shape);\n  }\n  return shape;\n}", "var _excluded = [\"x\", \"y\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport invariant from 'tiny-invariant';\nimport { Shape } from './ActiveShapeUtils';\nimport { isNullish, isNumber } from './DataUtils';\n\n// Rectangle props is expecting x, y, height, width as numbers, name as a string, and radius as a custom type\n// When props are being spread in from a user defined component in Bar,\n// the prop types of an SVGElement have these typed as something else.\n// This function will return the passed in props\n// along with x, y, height as numbers, name as a string, and radius as number | [number, number, number, number]\nfunction typeguardBarRectangleProps(_ref, props) {\n  var {\n      x: xProp,\n      y: yProp\n    } = _ref,\n    option = _objectWithoutProperties(_ref, _excluded);\n  var xValue = \"\".concat(xProp);\n  var x = parseInt(xValue, 10);\n  var yValue = \"\".concat(yProp);\n  var y = parseInt(yValue, 10);\n  var heightValue = \"\".concat(props.height || option.height);\n  var height = parseInt(heightValue, 10);\n  var widthValue = \"\".concat(props.width || option.width);\n  var width = parseInt(widthValue, 10);\n  return _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({}, props), option), x ? {\n    x\n  } : {}), y ? {\n    y\n  } : {}), {}, {\n    height,\n    width,\n    name: props.name,\n    radius: props.radius\n  });\n}\nexport function BarRectangle(props) {\n  return /*#__PURE__*/React.createElement(Shape, _extends({\n    shapeType: \"rectangle\",\n    propTransformer: typeguardBarRectangleProps,\n    activeClassName: \"recharts-active-bar\"\n  }, props));\n}\n/**\n * Safely gets minPointSize from the minPointSize prop if it is a function\n * @param minPointSize minPointSize as passed to the Bar component\n * @param defaultValue default minPointSize\n * @returns minPointSize\n */\nexport var minPointSizeCallback = function minPointSizeCallback(minPointSize) {\n  var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  return (value, index) => {\n    if (isNumber(minPointSize)) return minPointSize;\n    var isValueNumberOrNil = isNumber(value) || isNullish(value);\n    if (isValueNumberOrNil) {\n      return minPointSize(value, index);\n    }\n    !isValueNumberOrNil ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"minPointSize callback function received a value with type of \".concat(typeof value, \". Currently only numbers or null/undefined are supported.\")) : invariant(false) : void 0;\n    return defaultValue;\n  };\n};", "import { useAppDispatch } from '../state/hooks';\nimport { mouseLeaveItem, setActiveClickItemIndex, setActiveMouseOverItemIndex } from '../state/tooltipSlice';\nexport var useMouseEnterItemDispatch = (onMouseEnterFromProps, dataKey) => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseEnterFromProps === null || onMouseEnterFromProps === void 0 || onMouseEnterFromProps(data, index, event);\n    dispatch(setActiveMouseOverItemIndex({\n      activeIndex: String(index),\n      activeDataKey: dataKey,\n      activeCoordinate: data.tooltipPosition\n    }));\n  };\n};\nexport var useMouseLeaveItemDispatch = onMouseLeaveFromProps => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseLeaveFromProps === null || onMouseLeaveFromProps === void 0 || onMouseLeaveFromProps(data, index, event);\n    dispatch(mouseLeaveItem());\n  };\n};\nexport var useMouseClickItemDispatch = (onMouseClickFromProps, dataKey) => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseClickFromProps === null || onMouseClickFromProps === void 0 || onMouseClickFromProps(data, index, event);\n    dispatch(setActiveClickItemIndex({\n      activeIndex: String(index),\n      activeDataKey: dataKey,\n      activeCoordinate: data.tooltipPosition\n    }));\n  };\n};", "import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addBar, removeBar } from './graphicalItemsSlice';\nexport var ReportBar = () => {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addBar());\n    return () => {\n      dispatch(removeBar());\n    };\n  });\n  return null;\n};", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport { selectAxisWithScale, selectCartesianAxisSize, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getPercentValue, isNullish } from '../../util/DataUtils';\nimport { getBandSizeOfAxis } from '../../util/ChartUtils';\nimport { computeBarRectangles } from '../../cartesian/Bar';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartOffset } from './selectChartOffset';\nimport { selectBarCategoryGap, selectBarGap, selectRootBarSize, selectRootMaxBarSize } from './rootPropsSelectors';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nvar pickXAxisId = (_state, xAxisId) => xAxisId;\nvar pickYAxisId = (_state, _xAxisId, yAxisId) => yAxisId;\nvar pickIsPanorama = (_state, _xAxisId, _yAxisId, isPanorama) => isPanorama;\nvar pickBarSettings = (_state, _xAxisId, _yAxisId, _isPanorama, barSettings) => barSettings;\nvar pickMaxBarSize = (_state, _xAxisId, _yAxisId, _isPanorama, barSettings) => barSettings.maxBarSize;\nvar pickCells = (_state, _xAxisId, _yAxisId, _isPanorama, _barSettings, cells) => cells;\nvar getBarSize = (globalSize, totalSize, selfSize) => {\n  var barSize = selfSize !== null && selfSize !== void 0 ? selfSize : globalSize;\n  if (isNullish(barSize)) {\n    return undefined;\n  }\n  return getPercentValue(barSize, totalSize, 0);\n};\nexport var selectAllVisibleBars = createSelector([selectChartLayout, selectUnfilteredCartesianItems, pickXAxisId, pickYAxisId, pickIsPanorama], (layout, allItems, xAxisId, yAxisId, isPanorama) => allItems.filter(i => {\n  if (layout === 'horizontal') {\n    return i.xAxisId === xAxisId;\n  }\n  return i.yAxisId === yAxisId;\n}).filter(i => i.isPanorama === isPanorama).filter(i => i.hide === false).filter(i => i.type === 'bar'));\nvar selectBarStackGroups = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n};\nexport var selectBarCartesianAxisSize = (state, xAxisId, yAxisId) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectCartesianAxisSize(state, 'xAxis', xAxisId);\n  }\n  return selectCartesianAxisSize(state, 'yAxis', yAxisId);\n};\n\n/**\n * Some graphical items allow data stacking. The stacks are optional,\n * so all props here are optional too.\n */\n\n/**\n * Some graphical items allow data stacking.\n * This interface is used to represent the items that are stacked\n * because the user has provided the stackId and dataKey properties.\n */\n\nfunction isStacked(graphicalItem) {\n  return graphicalItem.stackId != null && graphicalItem.dataKey != null;\n}\nexport var combineBarSizeList = (allBars, globalSize, totalSize) => {\n  var initialValue = {};\n  var stackedBars = allBars.filter(isStacked);\n  var unstackedBars = allBars.filter(b => b.stackId == null);\n  var groupByStack = stackedBars.reduce((acc, bar) => {\n    if (!acc[bar.stackId]) {\n      acc[bar.stackId] = [];\n    }\n    acc[bar.stackId].push(bar);\n    return acc;\n  }, initialValue);\n  var stackedSizeList = Object.entries(groupByStack).map(_ref => {\n    var [stackId, bars] = _ref;\n    var dataKeys = bars.map(b => b.dataKey);\n    var barSize = getBarSize(globalSize, totalSize, bars[0].barSize);\n    return {\n      stackId,\n      dataKeys,\n      barSize\n    };\n  });\n  var unstackedSizeList = unstackedBars.map(b => {\n    var dataKeys = [b.dataKey].filter(dk => dk != null);\n    var barSize = getBarSize(globalSize, totalSize, b.barSize);\n    return {\n      stackId: undefined,\n      dataKeys,\n      barSize\n    };\n  });\n  return [...stackedSizeList, ...unstackedSizeList];\n};\nexport var selectBarSizeList = createSelector([selectAllVisibleBars, selectRootBarSize, selectBarCartesianAxisSize], combineBarSizeList);\nexport var selectBarBandSize = (state, xAxisId, yAxisId, isPanorama, barSettings) => {\n  var _ref2, _getBandSizeOfAxis;\n  var layout = selectChartLayout(state);\n  var globalMaxBarSize = selectRootMaxBarSize(state);\n  var {\n    maxBarSize: childMaxBarSize\n  } = barSettings;\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return (_ref2 = (_getBandSizeOfAxis = getBandSizeOfAxis(axis, ticks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref2 !== void 0 ? _ref2 : 0;\n};\nvar selectAxisBandSize = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return getBandSizeOfAxis(axis, ticks);\n};\nfunction getBarPositions(barGap, barCategoryGap, bandSize, sizeList, maxBarSize) {\n  var len = sizeList.length;\n  if (len < 1) {\n    return undefined;\n  }\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n  var initialValue = [];\n\n  // whether is barSize set by user\n  // Okay but why does it check only for the first element? What if the first element is set but others are not?\n  if (isWellBehavedNumber(sizeList[0].barSize)) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    var sum = sizeList.reduce((res, entry) => res + (entry.barSize || 0), 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce((res, entry) => {\n      var _entry$barSize;\n      var newPosition = {\n        stackId: entry.stackId,\n        dataKeys: entry.dataKeys,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          size: useFull ? fullBarSize : (_entry$barSize = entry.barSize) !== null && _entry$barSize !== void 0 ? _entry$barSize : 0\n        }\n      };\n      var newRes = [...res, newPosition];\n      prev = newRes[newRes.length - 1].position;\n      return newRes;\n    }, initialValue);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = isWellBehavedNumber(maxBarSize) ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce((res, entry, i) => [...res, {\n      stackId: entry.stackId,\n      dataKeys: entry.dataKeys,\n      position: {\n        offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n        size\n      }\n    }], initialValue);\n  }\n  return result;\n}\nexport var combineAllBarPositions = (sizeList, globalMaxBarSize, barGap, barCategoryGap, barBandSize, bandSize, childMaxBarSize) => {\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var allBarPositions = getBarPositions(barGap, barCategoryGap, barBandSize !== bandSize ? barBandSize : bandSize, sizeList, maxBarSize);\n  if (barBandSize !== bandSize && allBarPositions != null) {\n    allBarPositions = allBarPositions.map(pos => _objectSpread(_objectSpread({}, pos), {}, {\n      position: _objectSpread(_objectSpread({}, pos.position), {}, {\n        offset: pos.position.offset - barBandSize / 2\n      })\n    }));\n  }\n  return allBarPositions;\n};\nexport var selectAllBarPositions = createSelector([selectBarSizeList, selectRootMaxBarSize, selectBarGap, selectBarCategoryGap, selectBarBandSize, selectAxisBandSize, pickMaxBarSize], combineAllBarPositions);\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nexport var selectBarPosition = createSelector([selectAllBarPositions, pickBarSettings], (allBarPositions, barSettings) => {\n  if (allBarPositions == null) {\n    return undefined;\n  }\n  var position = allBarPositions.find(p => p.stackId === barSettings.stackId && p.dataKeys.includes(barSettings.dataKey));\n  if (position == null) {\n    return undefined;\n  }\n  return position.position;\n});\nexport var combineStackedData = (stackGroups, barSettings) => {\n  if (!stackGroups || (barSettings === null || barSettings === void 0 ? void 0 : barSettings.dataKey) == null) {\n    return undefined;\n  }\n  var {\n    stackId\n  } = barSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var stackGroup = stackGroups[stackId];\n  if (!stackGroup) {\n    return undefined;\n  }\n  var {\n    stackedData\n  } = stackGroup;\n  if (!stackedData) {\n    return undefined;\n  }\n  var stack = stackedData.find(sd => sd.key === barSettings.dataKey);\n  return stack;\n};\nvar selectSynchronisedBarSettings = createSelector([selectUnfilteredCartesianItems, pickBarSettings], (graphicalItems, barSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'bar' && barSettingsFromProps.dataKey === cgis.dataKey && barSettingsFromProps.stackId === cgis.stackId &&\n  // barSettingsFromProps.data === cgis.data && // bar doesn't support data and one is undefined and another is null and this condition breaks\n  barSettingsFromProps.stackId === cgis.stackId)) {\n    return barSettingsFromProps;\n  }\n  return undefined;\n});\nvar selectStackedDataOfItem = createSelector([selectBarStackGroups, pickBarSettings], combineStackedData);\nexport var selectBarRectangles = createSelector([selectChartOffset, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectBarPosition, selectChartLayout, selectChartDataWithIndexesIfNotInPanorama, selectAxisBandSize, selectStackedDataOfItem, selectSynchronisedBarSettings, pickCells], (offset, xAxis, yAxis, xAxisTicks, yAxisTicks, pos, layout, _ref3, bandSize, stackedData, barSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (barSettings == null || pos == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = barSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeBarRectangles({\n    layout,\n    barSettings,\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    displayedData,\n    offset,\n    cells\n  });\n});", "var _excluded = [\"onMouseEnter\", \"onMouseLeave\", \"onClick\"],\n  _excluded2 = [\"value\", \"background\", \"tooltipPosition\"],\n  _excluded3 = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Render a group of bar\n */\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { SetErrorBarPreferredDirection } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { LabelList } from '../component/LabelList';\nimport { interpolateNumber, isNan, isNullish, mathSign, uniqueId } from '../util/DataUtils';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getBaseValueOfBar, getCateCoordinateOfBar, getNormalizedStackId, getTooltipNameProp, getValueByDataKey, truncateByDomain } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { BarRectangle, minPointSizeCallback } from '../util/BarUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { ReportBar } from '../state/ReportBar';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { useChartLayout } from '../context/chartLayoutContext';\nimport { selectBarRectangles } from '../state/selectors/barSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectActiveTooltipDataKey, selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar computeLegendPayloadFromBarData = props => {\n  var {\n    dataKey,\n    name,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: fill,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: undefined,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: props.fill,\n      unit\n    }\n  };\n}\nfunction BarBackground(props) {\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n    data,\n    dataKey,\n    background: backgroundFromProps,\n    allOtherBarProps\n  } = props;\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onMouseLeave: onMouseLeaveFromProps,\n      onClick: onItemClickFromProps\n    } = allOtherBarProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherBarProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, dataKey);\n  if (!backgroundFromProps || data == null) {\n    return null;\n  }\n  var backgroundProps = filterProps(backgroundFromProps, false);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, data.map((entry, i) => {\n    var {\n        value,\n        background: backgroundFromDataEntry,\n        tooltipPosition\n      } = entry,\n      rest = _objectWithoutProperties(entry, _excluded2);\n    if (!backgroundFromDataEntry) {\n      return null;\n    }\n\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onMouseEnter = onMouseEnterFromContext(entry, i);\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onMouseLeave = onMouseLeaveFromContext(entry, i);\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onClick = onClickFromContext(entry, i);\n    var barRectangleProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({\n      option: backgroundFromProps,\n      isActive: String(i) === activeIndex\n    }, rest), {}, {\n      // @ts-expect-error BarRectangle props do not accept `fill` property.\n      fill: '#eee'\n    }, backgroundFromDataEntry), backgroundProps), adaptEventsOfChild(restOfAllOtherProps, entry, i)), {}, {\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      dataKey,\n      index: i,\n      className: 'recharts-bar-background-rectangle'\n    });\n    return /*#__PURE__*/React.createElement(BarRectangle, _extends({\n      key: \"background-bar-\".concat(i)\n    }, barRectangleProps));\n  }));\n}\nfunction BarRectangles(_ref) {\n  var {\n    data,\n    props,\n    showLabels\n  } = _ref;\n  var baseProps = filterProps(props, false);\n  var {\n    shape,\n    dataKey,\n    activeBar\n  } = props;\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var activeDataKey = useAppSelector(selectActiveTooltipDataKey);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = props,\n    restOfAllOtherProps = _objectWithoutProperties(props, _excluded3);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, dataKey);\n  if (!data) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, data.map((entry, i) => {\n    /*\n     * Bars support stacking, meaning that there can be multiple bars at the same x value.\n     * With Tooltip shared=false we only want to highlight the currently active Bar, not all.\n     *\n     * Also, if the tooltip is shared, we want to highlight all bars at the same x value\n     * regardless of the dataKey.\n     *\n     * With shared Tooltip, the activeDataKey is undefined.\n     */\n    var isActive = activeBar && String(i) === activeIndex && (activeDataKey == null || dataKey === activeDataKey);\n    var option = isActive ? activeBar : shape;\n    var barRectangleProps = _objectSpread(_objectSpread(_objectSpread({}, baseProps), entry), {}, {\n      isActive,\n      option,\n      index: i,\n      dataKey\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-bar-rectangle\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      ,\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      ,\n      onClick: onClickFromContext(entry, i)\n      // https://github.com/recharts/recharts/issues/5415\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n      key: \"rectangle-\".concat(entry === null || entry === void 0 ? void 0 : entry.x, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.y, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.value, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(BarRectangle, barRectangleProps));\n  }), showLabels && LabelList.renderCallByParent(props, data));\n}\nfunction RectanglesWithAnimation(_ref2) {\n  var {\n    props,\n    previousRectanglesRef\n  } = _ref2;\n  var {\n    data,\n    layout,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevData = previousRectanglesRef.current;\n  var animationId = useAnimationId(props, 'recharts-bar-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? data : data.map((entry, index) => {\n      var prev = prevData && prevData[index];\n      if (prev) {\n        var interpolatorX = interpolateNumber(prev.x, entry.x);\n        var interpolatorY = interpolateNumber(prev.y, entry.y);\n        var interpolatorWidth = interpolateNumber(prev.width, entry.width);\n        var interpolatorHeight = interpolateNumber(prev.height, entry.height);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: interpolatorX(t),\n          y: interpolatorY(t),\n          width: interpolatorWidth(t),\n          height: interpolatorHeight(t)\n        });\n      }\n      if (layout === 'horizontal') {\n        var _interpolatorHeight = interpolateNumber(0, entry.height);\n        var h = _interpolatorHeight(t);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          y: entry.y + entry.height - h,\n          height: h\n        });\n      }\n      var interpolator = interpolateNumber(0, entry.width);\n      var w = interpolator(t);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        width: w\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousRectanglesRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(BarRectangles, {\n      props: props,\n      data: stepData,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderRectangles(props) {\n  var {\n    data,\n    isAnimationActive\n  } = props;\n  var previousRectanglesRef = useRef(null);\n  if (isAnimationActive && data && data.length && (previousRectanglesRef.current == null || previousRectanglesRef.current !== data)) {\n    return /*#__PURE__*/React.createElement(RectanglesWithAnimation, {\n      previousRectanglesRef: previousRectanglesRef,\n      props: props\n    });\n  }\n  return /*#__PURE__*/React.createElement(BarRectangles, {\n    props: props,\n    data: data,\n    showLabels: true\n  });\n}\nvar defaultMinPointSize = 0;\nvar errorBarDataPointFormatter = (dataPoint, dataKey) => {\n  /**\n   * if the value coming from `selectBarRectangles` is an array then this is a stacked bar chart.\n   * arr[1] represents end value of the bar since the data is in the form of [startValue, endValue].\n   * */\n  var value = Array.isArray(dataPoint.value) ? dataPoint.value[1] : dataPoint.value;\n  return {\n    x: dataPoint.x,\n    y: dataPoint.y,\n    value,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint, dataKey)\n  };\n};\nclass BarWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-bar-'));\n  }\n  render() {\n    var {\n      hide,\n      data,\n      dataKey,\n      className,\n      xAxisId,\n      yAxisId,\n      needClip,\n      background,\n      id,\n      layout\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-bar', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    })), /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-bar-rectangles\",\n      clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n    }, /*#__PURE__*/React.createElement(BarBackground, {\n      data: data,\n      dataKey: dataKey,\n      background: background,\n      allOtherBarProps: this.props\n    }), /*#__PURE__*/React.createElement(RenderRectangles, this.props)), /*#__PURE__*/React.createElement(SetErrorBarPreferredDirection, {\n      direction: layout === 'horizontal' ? 'y' : 'x'\n    }, this.props.children));\n  }\n}\nvar defaultBarProps = {\n  activeBar: false,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'rect',\n  minPointSize: defaultMinPointSize,\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction BarImpl(props) {\n  var {\n    xAxisId,\n    yAxisId,\n    hide,\n    legendType,\n    minPointSize,\n    activeBar,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    isAnimationActive\n  } = resolveDefaultProps(props, defaultBarProps);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var layout = useChartLayout();\n  var isPanorama = useIsPanorama();\n  var barSettings = useMemo(() => ({\n    barSize: props.barSize,\n    data: undefined,\n    dataKey: props.dataKey,\n    maxBarSize: props.maxBarSize,\n    minPointSize,\n    stackId: getNormalizedStackId(props.stackId)\n  }), [props.barSize, props.dataKey, props.maxBarSize, minPointSize, props.stackId]);\n  var cells = findAllByType(props.children, Cell);\n  var rects = useAppSelector(state => selectBarRectangles(state, xAxisId, yAxisId, isPanorama, barSettings, cells));\n  if (layout !== 'vertical' && layout !== 'horizontal') {\n    return null;\n  }\n  var errorBarOffset;\n  var firstDataPoint = rects === null || rects === void 0 ? void 0 : rects[0];\n  if (firstDataPoint == null || firstDataPoint.height == null || firstDataPoint.width == null) {\n    errorBarOffset = 0;\n  } else {\n    errorBarOffset = layout === 'vertical' ? firstDataPoint.height / 2 : firstDataPoint.width / 2;\n  }\n  return /*#__PURE__*/React.createElement(SetErrorBarContext, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    data: rects,\n    dataPointFormatter: errorBarDataPointFormatter,\n    errorBarOffset: errorBarOffset\n  }, /*#__PURE__*/React.createElement(BarWithState, _extends({}, props, {\n    layout: layout,\n    needClip: needClip,\n    data: rects,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    hide: hide,\n    legendType: legendType,\n    minPointSize: minPointSize,\n    activeBar: activeBar,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive\n  })));\n}\nexport function computeBarRectangles(_ref4) {\n  var {\n    layout,\n    barSettings: {\n      dataKey,\n      minPointSize: minPointSizeProp\n    },\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    displayedData,\n    offset,\n    cells\n  } = _ref4;\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error this assumes that the domain is always numeric, but doesn't check for it\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis\n  });\n  return displayedData.map((entry, index) => {\n    var value, x, y, width, height, background;\n    if (stackedData) {\n      // we don't need to use dataStartIndex here, because stackedData is already sliced from the selector\n      value = truncateByDomain(stackedData[index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    var minPointSize = minPointSizeCallback(minPointSizeProp, defaultMinPointSize)(value[1], index);\n    if (layout === 'horizontal') {\n      var _ref5;\n      var [baseValueScale, currentValueScale] = [yAxis.scale(value[0]), yAxis.scale(value[1])];\n      x = getCateCoordinateOfBar({\n        axis: xAxis,\n        ticks: xAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      y = (_ref5 = currentValueScale !== null && currentValueScale !== void 0 ? currentValueScale : baseValueScale) !== null && _ref5 !== void 0 ? _ref5 : undefined;\n      width = pos.size;\n      var computedHeight = baseValueScale - currentValueScale;\n      height = isNan(computedHeight) ? 0 : computedHeight;\n      background = {\n        x,\n        y: offset.top,\n        width,\n        height: offset.height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(height) < Math.abs(minPointSize)) {\n        var delta = mathSign(height || minPointSize) * (Math.abs(minPointSize) - Math.abs(height));\n        y -= delta;\n        height += delta;\n      }\n    } else {\n      var [_baseValueScale, _currentValueScale] = [xAxis.scale(value[0]), xAxis.scale(value[1])];\n      x = _baseValueScale;\n      y = getCateCoordinateOfBar({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      width = _currentValueScale - _baseValueScale;\n      height = pos.size;\n      background = {\n        x: offset.left,\n        y,\n        width: offset.width,\n        height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(width) < Math.abs(minPointSize)) {\n        var _delta = mathSign(width || minPointSize) * (Math.abs(minPointSize) - Math.abs(width));\n        width += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      x,\n      y,\n      width,\n      height,\n      value: stackedData ? value : value[1],\n      payload: entry,\n      background,\n      tooltipPosition: {\n        x: x + width / 2,\n        y: y + height / 2\n      }\n    }, cells && cells[index] && cells[index].props);\n  });\n}\nexport class Bar extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"bar\"\n      // Bar does not allow setting data directly on the graphical item (why?)\n      ,\n      data: null,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      dataKey: this.props.dataKey,\n      stackId: this.props.stackId,\n      hide: this.props.hide,\n      barSize: this.props.barSize\n    }, /*#__PURE__*/React.createElement(ReportBar, null), /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromBarData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(BarImpl, this.props));\n  }\n}\n_defineProperty(Bar, \"displayName\", 'Bar');\n_defineProperty(Bar, \"defaultProps\", defaultBarProps);", "module.exports = require('../dist/compat/predicate/isPlainObject.js').isPlainObject;\n"], "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "getTangentCircle", "_ref", "cx", "cy", "radius", "angle", "sign", "isExternal", "cornerRadius", "cornerIsExternal", "centerRadius", "theta", "Math", "asin", "RADIAN", "centerAngle", "lineTangencyAngle", "center", "polarToCartesian", "circleTangency", "lineTangency", "cos", "getSectorPath", "_ref2", "innerRadius", "outerRadius", "startAngle", "endAngle", "getDeltaAngle", "mathSign", "min", "abs", "tempEndAngle", "outerStartPoint", "outerEndPoint", "path", "concat", "x", "y", "innerStartPoint", "innerEndPoint", "defaultProps", "forceCornerRadius", "Sector", "sectorProps", "props", "resolveDefaultProps", "className", "layerClass", "clsx", "deltaRadius", "cr", "getPercentValue", "_ref3", "soct", "solt", "sot", "eoct", "eolt", "eot", "outerArcAngle", "sict", "silt", "sit", "eict", "eilt", "eit", "innerArcAngle", "getSectorWithCorner", "React", "filterProps", "d", "DivStyledAsH4", "divWithClassName", "displayName", "AlertHeading", "ref", "bsPrefix", "as", "Component", "useBootstrapPrefix", "_jsx", "classNames", "AlertLink", "<PERSON><PERSON>", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "children", "variant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "Transition", "alert", "_jsxs", "role", "undefined", "CloseButton", "onClick", "unmountOnExit", "in", "Link", "Heading", "allowedTooltipTypes", "<PERSON><PERSON><PERSON>", "forwardRef", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chartName", "defaultTooltipEventType", "validateTooltipEventTypes", "tooltipPayloadSearcher", "arrayTooltipSearcher", "categoricalChartProps", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isPlainObject", "object", "getPrototypeOf", "prototype", "toString", "tag", "getOwnPropertyDescriptor", "writable", "proto", "Table", "striped", "bordered", "borderless", "hover", "size", "responsive", "decoratedBsPrefix", "classes", "table", "responsiveClass", "getRectanglePath", "width", "height", "maxRadius", "ySign", "xSign", "clockWise", "Array", "newRadius", "i", "_newRadius", "isAnimationActive", "isUpdateAnimationActive", "animationBegin", "animationDuration", "animationEasing", "Rectangle", "rectangleProps", "pathRef", "useRef", "totalLength", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "useEffect", "current", "getTotalLength", "pathTotalLength", "_unused", "Animate", "canBegin", "from", "to", "duration", "isActive", "currWidth", "currHeight", "currX", "currY", "attributeName", "begin", "easing", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "toPrimitive", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "defaultFormatter", "isArray", "isNumOrStr", "join", "DefaultTooltipContent", "separator", "contentStyle", "itemStyle", "labelStyle", "payload", "formatter", "itemSorter", "wrapperClassName", "labelClassName", "label", "labelFormatter", "accessibilityLayer", "finalStyle", "margin", "padding", "backgroundColor", "border", "whiteSpace", "finalLabelStyle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "finalLabel", "wrapperCN", "labelCN", "accessibilityAttributes", "style", "renderContent", "items", "sortBy", "map", "entry", "type", "<PERSON><PERSON><PERSON><PERSON>er", "name", "finalValue", "finalName", "formatted", "finalItemStyle", "display", "paddingTop", "paddingBottom", "color", "key", "unit", "CSS_CLASS_PREFIX", "TOOLTIP_HIDDEN", "visibility", "getTooltipCSSClassName", "coordinate", "translateX", "translateY", "isNumber", "getTooltipTranslateXY", "allowEscapeViewBox", "offsetTopLeft", "position", "reverseDirection", "tooltipDimension", "viewBox", "viewBoxDimension", "negative", "positive", "viewBoxKey", "max", "TooltipBoundingBox", "PureComponent", "constructor", "super", "this", "dismissed", "dismissedAtCoordinate", "event", "_this$props$coordinat", "_this$props$coordinat2", "_this$props$coordinat3", "_this$props$coordinat4", "setState", "componentDidMount", "document", "addEventListener", "handleKeyDown", "componentWillUnmount", "removeEventListener", "componentDidUpdate", "_this$props$coordinat5", "_this$props$coordinat6", "state", "render", "active", "hasPayload", "offset", "useTranslate3d", "wrapperStyle", "lastBoundingBox", "innerRef", "hasPortalFromProps", "cssClasses", "cssProperties", "_ref4", "tooltipBox", "transform", "getTransformStyle", "getTooltipTranslate", "positionStyles", "pointerEvents", "top", "left", "outerStyle", "xmlns", "tabIndex", "_excluded", "<PERSON><PERSON><PERSON>", "Cross", "indexOf", "_objectWithoutPropertiesLoose", "propertyIsEnumerable", "_objectWithoutProperties", "getRadialCursorPoints", "activeCoordinate", "points", "getCursorPoints", "layout", "x1", "y1", "x2", "y2", "innerPoint", "outerPoint", "CursorInternal", "restProps", "cursor<PERSON>omp", "index", "tooltipAxisBandSize", "cursor", "tooltipEventType", "activePayload", "activeTooltipIndex", "halfSize", "stroke", "fill", "getCursorRectangle", "Curve", "extraClassName", "cursorProps", "payloadIndex", "isValidElement", "cloneElement", "createElement", "<PERSON><PERSON><PERSON>", "useTooltipAxisBandSize", "useOffset", "useChartLayout", "useChartName", "defaultUniqBy", "dataKey", "emptyPayload", "defaultTooltipProps", "axisId", "filterNull", "Global", "isSsr", "trigger", "<PERSON><PERSON><PERSON>", "outsideProps", "activeFromProps", "content", "payloadUniqBy", "shared", "defaultIndex", "portal", "portalFromProps", "dispatch", "useAppDispatch", "defaultIndexAsString", "setTooltipSettingsState", "useViewBox", "useAccessibilityLayer", "useTooltipEventType", "activeIndex", "useAppSelector", "selectIsTooltipActive", "payloadFromRedux", "selectTooltipPayload", "labelFromRedux", "selectActiveLabel", "selectActiveCoordinate", "tooltipPortalFromContext", "useTooltipPortal", "finalIsActive", "updateBoundingBox", "useElementOffset", "useTooltipChartSynchronisation", "tooltipPortal", "finalPayload", "getUniqPayload", "hide", "includeHidden", "tooltipElement", "Boolean", "createPortal", "Cell", "_props", "getTrapezoidPath", "upperWidth", "lowerWidth", "widthGap", "Trapezoid", "trapezoidProps", "currU<PERSON><PERSON><PERSON><PERSON>", "curr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultPropTransformer", "option", "ShapeSelector", "shapeType", "elementProps", "isSymbolsProps", "Symbols", "<PERSON><PERSON><PERSON>", "shape", "propTransformer", "activeClassName", "getPropsFromShapeOption", "nextProps", "Layer", "typeguardBarRectangleProps", "xProp", "yProp", "xValue", "parseInt", "yValue", "heightValue", "widthValue", "BarRectangle", "minPointSizeCallback", "minPointSize", "defaultValue", "isValueNumberOrNil", "condition", "Error", "invariant", "useMouseEnterItemDispatch", "onMouseEnterFromProps", "data", "setActiveMouseOverItemIndex", "activeDataKey", "tooltipPosition", "useMouseLeaveItemDispatch", "onMouseLeaveFromProps", "mouseLeaveItem", "useMouseClickItemDispatch", "onMouseClickFromProps", "setActiveClickItemIndex", "ReportBar", "addBar", "removeBar", "pickBarSettings", "_state", "_xAxisId", "_yAxisId", "_isPanorama", "barSettings", "getBarSize", "globalSize", "totalSize", "selfSize", "barSize", "selectAllVisibleBars", "createSelector", "selectChartLayout", "selectUnfilteredCartesianItems", "pickXAxisId", "xAxisId", "pickYAxisId", "yAxisId", "pickIsPanorama", "isPanorama", "allItems", "isStacked", "graphicalItem", "stackId", "selectBarSizeList", "selectRootBarSize", "selectBarCartesianAxisSize", "selectCartesianAxisSize", "combineBarSizeList", "allBars", "stackedBars", "unstackedBars", "b", "groupByStack", "reduce", "acc", "bar", "entries", "bars", "dataKeys", "dk", "selectAxisBandSize", "axis", "ticks", "selectAxisWithScale", "selectTicksOfGraphicalItem", "getBandSizeOfAxis", "selectAllBarPositions", "selectRootMaxBarSize", "selectBarGap", "selectBarCategoryGap", "selectBarBandSize", "_getBandSizeOfAxis", "globalMaxBarSize", "maxBarSize", "childMaxBarSize", "pickMaxBarSize", "combineAllBarPositions", "sizeList", "barGap", "barCategoryGap", "barBandSize", "bandSize", "allBarPositions", "len", "result", "realBarGap", "initialValue", "isWellBehavedNumber", "useFull", "fullBarSize", "sum", "res", "prev", "_entry$barSize", "newRes", "_offset", "originalSize", "getBarPositions", "pos", "selectBarPosition", "find", "p", "includes", "selectSynchronisedBarSettings", "graphicalItems", "barSettingsFromProps", "some", "cgis", "selectStackedDataOfItem", "selectBarStackGroups", "selectStackGroups", "combineStackedData", "stackGroups", "stackGroup", "stackedData", "sd", "selectBarRectangles", "selectChartOffset", "selectXAxisWithScale", "selectYAxisWithScale", "selectXAxisTicks", "selectYAxisTicks", "selectChartDataWithIndexesIfNotInPanorama", "pick<PERSON>ells", "_barSettings", "cells", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "chartData", "dataStartIndex", "dataEndIndex", "displayedData", "slice", "computeBarRectangles", "_excluded2", "_excluded3", "computeLegendPayloadFromBarData", "legendType", "inactive", "getTooltipNameProp", "getTooltipEntrySettings", "strokeWidth", "dataDefinedOnItem", "positions", "settings", "<PERSON><PERSON><PERSON>", "tooltipType", "BarBackground", "selectActiveTooltipIndex", "background", "backgroundFromProps", "allOtherBarProps", "onMouseEnter", "onMouseLeave", "onItemClickFromProps", "restOfAllOtherProps", "onMouseEnterFromContext", "onMouseLeaveFromContext", "onClickFromContext", "backgroundProps", "backgroundFromDataEntry", "rest", "barRectangleProps", "adaptEventsOfChild", "BarRectangles", "showLabels", "baseProps", "activeBar", "selectActiveTooltipDataKey", "LabelList", "renderCallByParent", "RectanglesWithAnimation", "previousRectanglesRef", "onAnimationEnd", "onAnimationStart", "prevData", "animationId", "useAnimationId", "isAnimating", "setIsAnimating", "handleAnimationEnd", "useCallback", "handleAnimationStart", "stepData", "interpolatorX", "interpolateNumber", "interpolatorY", "interpolatorWidth", "interpolatorHeight", "h", "_interpolatorHeight", "w", "interpolator", "RenderRectangles", "defaultMinPointSize", "errorBarDataPointFormatter", "dataPoint", "errorVal", "getValueByDataKey", "BarWithState", "uniqueId", "needClip", "id", "clipPathId", "GraphicalItemClipPath", "clipPath", "SetErrorBarPreferredDirection", "direction", "defaultBarProps", "BarImpl", "errorBarOffset", "useNeedsClip", "useIsPanorama", "useMemo", "getNormalizedStackId", "findAllByType", "rects", "firstDataPoint", "SetErrorBarContext", "dataPointFormatter", "minPointSizeProp", "numericAxis", "stackedDomain", "scale", "domain", "baseValue", "getBaseValueOfBar", "truncateByDomain", "_ref5", "baseValueScale", "currentValueScale", "getCateCoordinateOfBar", "computedHeight", "isNan", "delta", "_baseValueScale", "_currentValueScale", "Bar", "CartesianGraphicalItemContext", "zAxisId", "SetLegendPayload", "legendPayload", "SetTooltipEntrySettings", "fn", "args", "module"], "sourceRoot": ""}