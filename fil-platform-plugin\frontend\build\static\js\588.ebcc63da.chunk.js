"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[588],{588:(e,a,t)=>{t.r(a),t.d(a,{default:()=>A});var r=t(5043),s=t(4117),n=t(8628),i=t(7417),l=t(3519),d=t(1072),o=t(8602),c=t(4282),h=t(108),u=t(2998),x=t(7734),g=t(2185),m=t(760),j=t(713),_=t(2872),v=t(1283),w=t(4312),f=t(579);const y=e=>{let{title:a,value:t,subValue:r,variant:s,loading:l}=e;return(0,f.jsx)(n.A,{className:`bg-${s} text-white mb-3`,children:(0,f.jsxs)(n.A.Body,{children:[(0,f.jsx)(n.A.Title,{children:a}),l?(0,f.jsxs)("div",{className:"d-flex align-items-center",children:[(0,f.jsx)(i.A,{animation:"border",size:"sm",className:"me-2"}),(0,f.jsx)("span",{children:"Loading..."})]}):(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("h3",{children:t}),r&&(0,f.jsx)("p",{children:r})]})]})})},A=()=>{const{t:e}=(0,s.Bd)(),a=(0,v.Zp)(),[t,A]=(0,r.useState)(!0),[p,b]=(0,r.useState)({totalEarnings:0,yesterdayEarnings:0,availableBalance:0,powerPledge:0}),[D,F]=(0,r.useState)([]),[N,E]=(0,r.useState)(null);(0,r.useEffect)(()=>{(async()=>{const e=(0,w.b)();if(!e)return E("Failed to initialize Supabase client"),void A(!1);try{A(!0);const{data:{user:a}}=await e.auth.getUser();if(!a)return E("User not logged in"),void A(!1);const{data:t,error:r}=await e.from("user_assets").select("currency_code, balance_available, balance_total").eq("user_id",a.id);r&&console.error("Error fetching assets:",r);const{data:s,error:n}=await e.from("order_distributions").select("reward_amount, created_at").eq("customer_id",a.id).order("created_at",{ascending:!1});n&&console.error("Error fetching earnings:",n);const{data:i,error:l}=await e.from("orders").select("pledge_cost, shares, start_at, end_at").eq("customer_id",a.id).eq("review_status","approved");l&&console.error("Error fetching orders:",l);const d=((e,a,t)=>{const r=((null===e||void 0===e?void 0:e.find(e=>"FIL"===e.currency_code))||{}).balance_available||0,s=(null===a||void 0===a?void 0:a.reduce((e,a)=>e+(a.reward_amount||0),0))||0,n=new Date;n.setDate(n.getDate()-1);const i=new Date(n.getFullYear(),n.getMonth(),n.getDate()),l=new Date(i);l.setDate(l.getDate()+1);const d=(null===a||void 0===a?void 0:a.filter(e=>{const a=new Date(e.created_at);return a>=i&&a<l}).reduce((e,a)=>e+(a.reward_amount||0),0))||0,o=new Date,c=(null===t||void 0===t?void 0:t.filter(e=>{const a=new Date(e.start_at),t=new Date(e.end_at);return a<=o&&t>=o}).reduce((e,a)=>e+(a.pledge_cost||0),0))||0,h=[];for(let u=6;u>=0;u--){const e=new Date;e.setDate(e.getDate()-u);const t=new Date(e.getFullYear(),e.getMonth(),e.getDate()),r=new Date(t);r.setDate(r.getDate()+1);const s=(null===a||void 0===a?void 0:a.filter(e=>{const a=new Date(e.created_at);return a>=t&&a<r}).reduce((e,a)=>e+(a.reward_amount||0),0))||0;h.push({name:`${e.getMonth()+1}/${e.getDate()}`,fil:s,usd:4.05*s})}return{stats:{totalEarnings:s,yesterdayEarnings:d,availableBalance:r,powerPledge:c},chartData:h}})(t,s,i);b(d.stats),F(d.chartData)}catch(N){console.error("Error fetching dashboard data:",N),E("Failed to load dashboard data")}finally{A(!1)}})()},[]);const I=e=>{console.log("Navigating to:",e),console.log("Current URL:",window.location.href),a(e)},L=e=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e||0);return N?(0,f.jsx)(l.A,{fluid:!0,children:(0,f.jsx)(d.A,{className:"mb-3",children:(0,f.jsxs)(o.A,{children:[(0,f.jsx)("h2",{children:e("dashboard")}),(0,f.jsx)("div",{className:"alert alert-danger",children:N})]})})}):(0,f.jsxs)(l.A,{fluid:!0,children:[(0,f.jsx)(d.A,{className:"mb-3",children:(0,f.jsx)(o.A,{children:(0,f.jsx)("h2",{children:e("dashboard")})})}),(0,f.jsxs)(d.A,{children:[(0,f.jsx)(o.A,{md:3,children:(0,f.jsx)(y,{title:e("total_earnings"),value:`${L(p.totalEarnings)} FIL`,variant:"primary",loading:t})}),(0,f.jsx)(o.A,{md:3,children:(0,f.jsx)(y,{title:e("yesterday_earnings"),value:`${L(p.yesterdayEarnings)} FIL`,variant:"success",loading:t})}),(0,f.jsx)(o.A,{md:3,children:(0,f.jsx)(y,{title:e("available_balance"),value:`${L(p.availableBalance)} FIL`,variant:"info",loading:t})}),(0,f.jsx)(o.A,{md:3,children:(0,f.jsx)(y,{title:e("power_pledge"),value:`${L(p.powerPledge)} FIL`,variant:"warning",loading:t})})]}),(0,f.jsx)(d.A,{children:(0,f.jsx)(o.A,{children:(0,f.jsx)(n.A,{children:(0,f.jsxs)(n.A.Body,{children:[(0,f.jsx)(n.A.Title,{children:e("earnings_trend")}),t?(0,f.jsxs)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"400px"},children:[(0,f.jsx)(i.A,{animation:"border"}),(0,f.jsx)("span",{className:"ms-2",children:e("loading")})]}):(0,f.jsx)(h.u,{width:"100%",height:400,children:(0,f.jsxs)(u.b,{data:D,children:[(0,f.jsx)(x.d,{strokeDasharray:"3 3"}),(0,f.jsx)(g.W,{dataKey:"name"}),(0,f.jsx)(m.h,{yAxisId:"left",label:{value:"FIL",angle:-90,position:"insideLeft"}}),(0,f.jsx)(j.s,{}),(0,f.jsx)(_.N,{yAxisId:"left",type:"monotone",dataKey:"fil",stroke:"#8884d8",name:e("FIL_earnings")}),(0,f.jsx)(_.N,{yAxisId:"right",type:"monotone",dataKey:"usd",stroke:"#82ca9d",name:e("USD_estimate")})]})})]})})})}),(0,f.jsxs)(d.A,{className:"mt-4",children:[(0,f.jsx)(o.A,{md:6,className:"text-center",children:(0,f.jsx)(n.A,{children:(0,f.jsxs)(n.A.Body,{children:[(0,f.jsx)("h4",{children:e("wallet_management")}),(0,f.jsx)("p",{children:e("manage_your_digital_assets")}),(0,f.jsx)(c.A,{variant:"primary",onClick:()=>I("/my"),children:e("enter_wallet")})]})})}),(0,f.jsx)(o.A,{md:6,className:"text-center",children:(0,f.jsx)(n.A,{children:(0,f.jsxs)(n.A.Body,{children:[(0,f.jsx)("h4",{children:e("buy_power")}),(0,f.jsx)("p",{children:e("view_and_purchase_new_power_products")}),(0,f.jsx)(c.A,{variant:"success",onClick:()=>I("/products"),children:e("browse_products")})]})})})]})]})}}}]);
//# sourceMappingURL=588.ebcc63da.chunk.js.map