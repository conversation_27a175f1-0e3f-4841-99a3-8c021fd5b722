"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[588],{588:(e,a,t)=>{t.r(a),t.d(a,{default:()=>p});var r=t(5043),s=t(4117),n=t(8628),i=t(7417),l=t(3519),d=t(1072),o=t(8602),c=t(4282),u=t(108),h=t(2998),g=t(7734),x=t(2185),m=t(760),j=t(9923),_=t(713),w=t(2872),v=t(1283),f=t(4312),b=t(579);const y=e=>{let{title:a,value:t,subValue:r,variant:s,loading:l}=e;return(0,b.jsx)(n.A,{className:`bg-${s} text-white mb-3`,children:(0,b.jsxs)(n.A.Body,{children:[(0,b.jsx)(n.A.Title,{children:a}),l?(0,b.jsxs)("div",{className:"d-flex align-items-center",children:[(0,b.jsx)(i.A,{animation:"border",size:"sm",className:"me-2"}),(0,b.jsx)("span",{children:"Loading..."})]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("h3",{children:t}),r&&(0,b.jsx)("p",{children:r})]})]})})},p=()=>{const{t:e}=(0,s.Bd)(),a=(0,v.Zp)(),[t,p]=(0,r.useState)(!0),[A,D]=(0,r.useState)({totalEarnings:0,yesterdayEarnings:0,availableBalance:0,powerPledge:0}),[F,N]=(0,r.useState)([]),[I,E]=(0,r.useState)(null);(0,r.useEffect)(()=>{(async()=>{const e=(0,f.b)();if(!e)return E("Failed to initialize Supabase client"),void p(!1);try{p(!0);const{data:{user:a}}=await e.auth.getUser();if(!a)return E("User not logged in"),void p(!1);const{data:t,error:r}=await e.from("user_assets").select("currency_code, balance_available, balance_total").eq("user_id",a.id);r&&console.error("Error fetching assets:",r);const{data:s,error:n}=await e.from("order_distributions").select("reward_amount, created_at").eq("customer_id",a.id).order("created_at",{ascending:!1});n&&console.error("Error fetching earnings:",n);const{data:i,error:l}=await e.from("orders").select("pledge_cost, shares, start_at, end_at").eq("customer_id",a.id).eq("review_status","approved");l&&console.error("Error fetching orders:",l);const d=((e,a,t)=>{const r=((null===e||void 0===e?void 0:e.find(e=>"FIL"===e.currency_code))||{}).balance_available||0,s=(null===a||void 0===a?void 0:a.reduce((e,a)=>e+(a.reward_amount||0),0))||0,n=new Date;n.setDate(n.getDate()-1);const i=new Date(n.getFullYear(),n.getMonth(),n.getDate()),l=new Date(i);l.setDate(l.getDate()+1);const d=(null===a||void 0===a?void 0:a.filter(e=>{const a=new Date(e.created_at);return a>=i&&a<l}).reduce((e,a)=>e+(a.reward_amount||0),0))||0,o=new Date,c=(null===t||void 0===t?void 0:t.filter(e=>{const a=new Date(e.start_at),t=new Date(e.end_at);return a<=o&&t>=o}).reduce((e,a)=>e+(a.pledge_cost||0),0))||0,u=[];for(let h=6;h>=0;h--){const e=new Date;e.setDate(e.getDate()-h);const t=new Date(e.getFullYear(),e.getMonth(),e.getDate()),r=new Date(t);r.setDate(r.getDate()+1);const s=(null===a||void 0===a?void 0:a.filter(e=>{const a=new Date(e.created_at);return a>=t&&a<r}).reduce((e,a)=>e+(a.reward_amount||0),0))||0;u.push({name:`${e.getMonth()+1}/${e.getDate()}`,fil:s,usd:4.05*s})}return{stats:{totalEarnings:s,yesterdayEarnings:d,availableBalance:r,powerPledge:c},chartData:u}})(t,s,i);D(d.stats),N(d.chartData)}catch(I){console.error("Error fetching dashboard data:",I),E("Failed to load dashboard data")}finally{p(!1)}})()},[]);const L=e=>{console.log("Navigating to:",e),console.log("Current URL:",window.location.href),a(e)},$=e=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e||0),S=e=>{const a=4.05*(e||0);return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a)};return I?(0,b.jsx)(l.A,{fluid:!0,children:(0,b.jsx)(d.A,{className:"mb-3",children:(0,b.jsxs)(o.A,{children:[(0,b.jsx)("h2",{children:e("dashboard")}),(0,b.jsx)("div",{className:"alert alert-danger",children:I})]})})}):(0,b.jsxs)(l.A,{fluid:!0,children:[(0,b.jsx)(d.A,{className:"mb-3",children:(0,b.jsx)(o.A,{children:(0,b.jsx)("h2",{children:e("dashboard")})})}),(0,b.jsxs)(d.A,{children:[(0,b.jsx)(o.A,{md:3,children:(0,b.jsx)(y,{title:e("total_earnings"),value:`${$(A.totalEarnings)} FIL`,subValue:`\u2248 ${S(A.totalEarnings)}`,variant:"primary",loading:t})}),(0,b.jsx)(o.A,{md:3,children:(0,b.jsx)(y,{title:e("yesterday_earnings"),value:`${$(A.yesterdayEarnings)} FIL`,subValue:`\u2248 ${S(A.yesterdayEarnings)}`,variant:"success",loading:t})}),(0,b.jsx)(o.A,{md:3,children:(0,b.jsx)(y,{title:e("available_balance"),value:`${$(A.availableBalance)} FIL`,subValue:`\u2248 ${S(A.availableBalance)}`,variant:"info",loading:t})}),(0,b.jsx)(o.A,{md:3,children:(0,b.jsx)(y,{title:e("power_pledge"),value:`${$(A.powerPledge)} FIL`,subValue:`\u2248 ${S(A.powerPledge)}`,variant:"warning",loading:t})})]}),(0,b.jsx)(d.A,{children:(0,b.jsx)(o.A,{children:(0,b.jsx)(n.A,{children:(0,b.jsxs)(n.A.Body,{children:[(0,b.jsx)(n.A.Title,{children:e("earnings_trend")}),t?(0,b.jsxs)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"400px"},children:[(0,b.jsx)(i.A,{animation:"border"}),(0,b.jsx)("span",{className:"ms-2",children:e("loading")})]}):(0,b.jsx)(u.u,{width:"100%",height:400,children:(0,b.jsxs)(h.b,{data:F,children:[(0,b.jsx)(g.d,{strokeDasharray:"3 3"}),(0,b.jsx)(x.W,{dataKey:"name"}),(0,b.jsx)(m.h,{yAxisId:"left",label:{value:"FIL",angle:-90,position:"insideLeft"}}),(0,b.jsx)(m.h,{yAxisId:"right",orientation:"right",label:{value:"USD",angle:-90,position:"insideRight"}}),(0,b.jsx)(j.m,{formatter:(a,t)=>["fil"===t?`${$(a)} FIL`:S(a/4.05),e("fil"===t?"FIL_earnings":"USD_estimate")]}),(0,b.jsx)(_.s,{}),(0,b.jsx)(w.N,{yAxisId:"left",type:"monotone",dataKey:"fil",stroke:"#8884d8",name:e("FIL_earnings")}),(0,b.jsx)(w.N,{yAxisId:"right",type:"monotone",dataKey:"usd",stroke:"#82ca9d",name:e("USD_estimate")})]})})]})})})}),(0,b.jsxs)(d.A,{className:"mt-4",children:[(0,b.jsx)(o.A,{md:6,className:"text-center",children:(0,b.jsx)(n.A,{children:(0,b.jsxs)(n.A.Body,{children:[(0,b.jsx)("h4",{children:e("wallet_management")}),(0,b.jsx)("p",{children:e("manage_your_digital_assets")}),(0,b.jsx)(c.A,{variant:"primary",onClick:()=>L("/wallet"),children:e("enter_wallet")})]})})}),(0,b.jsx)(o.A,{md:6,className:"text-center",children:(0,b.jsx)(n.A,{children:(0,b.jsxs)(n.A.Body,{children:[(0,b.jsx)("h4",{children:e("buy_power")}),(0,b.jsx)("p",{children:e("view_and_purchase_new_power_products")}),(0,b.jsx)(c.A,{variant:"success",onClick:()=>L("/products"),children:e("browse_products")})]})})})]})]})}}}]);
//# sourceMappingURL=588.f95e5fc9.chunk.js.map