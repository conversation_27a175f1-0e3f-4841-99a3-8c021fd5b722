{"version": 3, "file": "static/js/588.f95e5fc9.chunk.js", "mappings": "8TAQA,MA4DMA,EAAWC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,SAAEC,EAAQ,QAAEC,EAAO,QAAEC,GAASL,EAAA,OAC1DM,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAACC,UAAW,MAAMJ,oBAA0BK,UAC7CC,EAAAA,EAAAA,MAACH,EAAAA,EAAKI,KAAI,CAAAF,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAER,IACZI,GACGK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACtCH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,SAASC,KAAK,KAAKP,UAAU,UAChDF,EAAAA,EAAAA,KAAA,QAAAG,SAAM,mBAGVC,EAAAA,EAAAA,MAAAM,EAAAA,SAAA,CAAAP,SAAA,EACIH,EAAAA,EAAAA,KAAA,MAAAG,SAAKP,IACJC,IAAYG,EAAAA,EAAAA,KAAA,KAAAG,SAAIN,aAkPrC,EA3O0Bc,KACtB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACRC,GAAWC,EAAAA,EAAAA,OAGVhB,EAASiB,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAeC,IAAoBF,EAAAA,EAAAA,UAAS,CAC/CG,cAAe,EACfC,kBAAmB,EACnBC,iBAAkB,EAClBC,YAAa,KAEVC,EAAWC,IAAgBR,EAAAA,EAAAA,UAAS,KACpCS,EAAOC,IAAYV,EAAAA,EAAAA,UAAS,OAGnCW,EAAAA,EAAAA,WAAU,KACqBC,WACvB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAGD,OAFAH,EAAS,6CACTX,GAAW,GAIf,IACIA,GAAW,GACX,MAAQgB,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAGD,OAFAN,EAAS,2BACTX,GAAW,GAKf,MAAQgB,KAAMI,EAAQV,MAAOW,SAAsBP,EAC9CQ,KAAK,eACLC,OAAO,mDACPC,GAAG,UAAWP,EAAKQ,IAEpBJ,GACAK,QAAQhB,MAAM,yBAA0BW,GAI5C,MAAQL,KAAMW,EAAUjB,MAAOkB,SAAwBd,EAClDQ,KAAK,uBACLC,OAAO,6BACPC,GAAG,cAAeP,EAAKQ,IACvBI,MAAM,aAAc,CAAEC,WAAW,IAElCF,GACAF,QAAQhB,MAAM,2BAA4BkB,GAI9C,MAAQZ,KAAMe,EAAQrB,MAAOsB,SAAsBlB,EAC9CQ,KAAK,UACLC,OAAO,yCACPC,GAAG,cAAeP,EAAKQ,IACvBD,GAAG,gBAAiB,YAErBQ,GACAN,QAAQhB,MAAM,yBAA0BsB,GAI5C,MAAMC,EAnJOC,EAACd,EAAQO,EAAUI,KAE5C,MACMzB,IADiB,OAANc,QAAM,IAANA,OAAM,EAANA,EAAQe,KAAKC,GAAiC,QAAxBA,EAAMC,iBAA4B,CAAC,GACxCC,mBAAqB,EAGjDlC,GAAwB,OAARuB,QAAQ,IAARA,OAAQ,EAARA,EAAUY,OAAO,CAACC,EAAKC,IAAYD,GAAOC,EAAQC,eAAiB,GAAI,KAAM,EAG7FC,EAAY,IAAIC,KACtBD,EAAUE,QAAQF,EAAUG,UAAY,GACxC,MAAMC,EAAiB,IAAIH,KAAKD,EAAUK,cAAeL,EAAUM,WAAYN,EAAUG,WACnFI,EAAe,IAAIN,KAAKG,GAC9BG,EAAaL,QAAQK,EAAaJ,UAAY,GAE9C,MAAMzC,GAA4B,OAARsB,QAAQ,IAARA,OAAQ,EAARA,EAAUwB,OAAOV,IACvC,MAAMW,EAAc,IAAIR,KAAKH,EAAQY,YACrC,OAAOD,GAAeL,GAAkBK,EAAcF,IACvDX,OAAO,CAACC,EAAKC,IAAYD,GAAOC,EAAQC,eAAiB,GAAI,KAAM,EAGhEY,EAAM,IAAIV,KACVrC,GAAoB,OAANwB,QAAM,IAANA,OAAM,EAANA,EAAQoB,OAAOtB,IAC/B,MAAM0B,EAAY,IAAIX,KAAKf,EAAM2B,UAC3BC,EAAU,IAAIb,KAAKf,EAAM6B,QAC/B,OAAOH,GAAaD,GAAOG,GAAWH,IACvCf,OAAO,CAACC,EAAKX,IAAUW,GAAOX,EAAM8B,aAAe,GAAI,KAAM,EAG1DnD,EAAY,GAClB,IAAK,IAAIoD,EAAI,EAAGA,GAAK,EAAGA,IAAK,CACzB,MAAMC,EAAO,IAAIjB,KACjBiB,EAAKhB,QAAQgB,EAAKf,UAAYc,GAC9B,MAAME,EAAY,IAAIlB,KAAKiB,EAAKb,cAAea,EAAKZ,WAAYY,EAAKf,WAC/DiB,EAAU,IAAInB,KAAKkB,GACzBC,EAAQlB,QAAQkB,EAAQjB,UAAY,GAEpC,MAAMkB,GAAsB,OAARrC,QAAQ,IAARA,OAAQ,EAARA,EAAUwB,OAAOV,IACjC,MAAMW,EAAc,IAAIR,KAAKH,EAAQY,YACrC,OAAOD,GAAeU,GAAaV,EAAcW,IAClDxB,OAAO,CAACC,EAAKC,IAAYD,GAAOC,EAAQC,eAAiB,GAAI,KAAM,EAEtElC,EAAUyD,KAAK,CACXC,KAAM,GAAGL,EAAKZ,WAAa,KAAKY,EAAKf,YACrCqB,IAAKH,EACLI,IAAmB,KAAdJ,GAEb,CAEA,MAAO,CACHK,MAAO,CACHjE,gBACAC,oBACAC,mBACAC,eAEJC,cA2F8B0B,CAAqBd,EAAQO,EAAUI,GAC7D5B,EAAiB8B,EAAcoC,OAC/B5D,EAAawB,EAAczB,UAE/B,CAAE,MAAOE,GACLgB,QAAQhB,MAAM,iCAAkCA,GAChDC,EAAS,gCACb,CAAC,QACGX,GAAW,EACf,GAGJsE,IACD,IAEH,MAAMC,EAAoBC,IACtB9C,QAAQ+C,IAAI,iBAAkBD,GAC9B9C,QAAQ+C,IAAI,eAAgBC,OAAOC,SAASC,MAC5C9E,EAAS0E,IAIPK,EAAgBC,GACX,IAAIC,KAAKC,aAAa,QAAS,CAClCC,sBAAuB,EACvBC,sBAAuB,IACxBC,OAAOL,GAAO,GAIfM,EAAaC,IACf,MAAMC,EAA+B,MAAlBD,GAAa,GAChC,OAAO,IAAIN,KAAKC,aAAa,QAAS,CAClCO,MAAO,WACPC,SAAU,QACXL,OAAOG,IAGd,OAAI5E,GAEI1B,EAAAA,EAAAA,KAACyG,EAAAA,EAAS,CAACC,OAAK,EAAAvG,UACZH,EAAAA,EAAAA,KAAC2G,EAAAA,EAAG,CAACzG,UAAU,OAAMC,UACjBC,EAAAA,EAAAA,MAACwG,EAAAA,EAAG,CAAAzG,SAAA,EACAH,EAAAA,EAAAA,KAAA,MAAAG,SAAKS,EAAE,gBACPZ,EAAAA,EAAAA,KAAA,OAAKE,UAAU,qBAAoBC,SAAEuB,YAQrDtB,EAAAA,EAAAA,MAACqG,EAAAA,EAAS,CAACC,OAAK,EAAAvG,SAAA,EACZH,EAAAA,EAAAA,KAAC2G,EAAAA,EAAG,CAACzG,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAAC4G,EAAAA,EAAG,CAAAzG,UACAH,EAAAA,EAAAA,KAAA,MAAAG,SAAKS,EAAE,oBAIfR,EAAAA,EAAAA,MAACuG,EAAAA,EAAG,CAAAxG,SAAA,EACAH,EAAAA,EAAAA,KAAC4G,EAAAA,EAAG,CAACC,GAAI,EAAE1G,UACPH,EAAAA,EAAAA,KAACP,EAAQ,CACLE,MAAOiB,EAAE,kBACThB,MAAO,GAAGiG,EAAa3E,EAAcE,qBACrCvB,SAAU,UAAKuG,EAAUlF,EAAcE,iBACvCtB,QAAQ,UACRC,QAASA,OAGjBC,EAAAA,EAAAA,KAAC4G,EAAAA,EAAG,CAACC,GAAI,EAAE1G,UACPH,EAAAA,EAAAA,KAACP,EAAQ,CACLE,MAAOiB,EAAE,sBACThB,MAAO,GAAGiG,EAAa3E,EAAcG,yBACrCxB,SAAU,UAAKuG,EAAUlF,EAAcG,qBACvCvB,QAAQ,UACRC,QAASA,OAGjBC,EAAAA,EAAAA,KAAC4G,EAAAA,EAAG,CAACC,GAAI,EAAE1G,UACPH,EAAAA,EAAAA,KAACP,EAAQ,CACLE,MAAOiB,EAAE,qBACThB,MAAO,GAAGiG,EAAa3E,EAAcI,wBACrCzB,SAAU,UAAKuG,EAAUlF,EAAcI,oBACvCxB,QAAQ,OACRC,QAASA,OAGjBC,EAAAA,EAAAA,KAAC4G,EAAAA,EAAG,CAACC,GAAI,EAAE1G,UACPH,EAAAA,EAAAA,KAACP,EAAQ,CACLE,MAAOiB,EAAE,gBACThB,MAAO,GAAGiG,EAAa3E,EAAcK,mBACrC1B,SAAU,UAAKuG,EAAUlF,EAAcK,eACvCzB,QAAQ,UACRC,QAASA,UAKrBC,EAAAA,EAAAA,KAAC2G,EAAAA,EAAG,CAAAxG,UACAH,EAAAA,EAAAA,KAAC4G,EAAAA,EAAG,CAAAzG,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDC,EAAAA,EAAAA,MAACH,EAAAA,EAAKI,KAAI,CAAAF,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAES,EAAE,oBACdb,GACGK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mDAAmDqG,MAAO,CAAEO,OAAQ,SAAU3G,SAAA,EACzFH,EAAAA,EAAAA,KAACO,EAAAA,EAAO,CAACC,UAAU,YACnBR,EAAAA,EAAAA,KAAA,QAAME,UAAU,OAAMC,SAAES,EAAE,iBAG9BZ,EAAAA,EAAAA,KAAC+G,EAAAA,EAAmB,CAACC,MAAM,OAAOF,OAAQ,IAAI3G,UAC1CC,EAAAA,EAAAA,MAAC6G,EAAAA,EAAS,CAACjF,KAAMR,EAAUrB,SAAA,EACvBH,EAAAA,EAAAA,KAACkH,EAAAA,EAAa,CAACC,gBAAgB,SAC/BnH,EAAAA,EAAAA,KAACoH,EAAAA,EAAK,CAACC,QAAQ,UACfrH,EAAAA,EAAAA,KAACsH,EAAAA,EAAK,CAACC,QAAQ,OAAOC,MAAO,CAAE5H,MAAO,MAAO6H,OAAQ,GAAIC,SAAU,iBACnE1H,EAAAA,EAAAA,KAACsH,EAAAA,EAAK,CAACC,QAAQ,QAAQI,YAAY,QAAQH,MAAO,CAAE5H,MAAO,MAAO6H,OAAQ,GAAIC,SAAU,kBACxF1H,EAAAA,EAAAA,KAAC4H,EAAAA,EAAO,CACJC,UAAWA,CAACjI,EAAOsF,IAAS,CACf,QAATA,EAAiB,GAAGW,EAAajG,SAAewG,EAAUxG,EAAQ,MACjDgB,EAAR,QAATsE,EAAmB,eAAoB,oBAG/ClF,EAAAA,EAAAA,KAAC8H,EAAAA,EAAM,KACP9H,EAAAA,EAAAA,KAAC+H,EAAAA,EAAI,CAACR,QAAQ,OAAOS,KAAK,WAAWX,QAAQ,MAAMY,OAAO,UAAU/C,KAAMtE,EAAE,mBAC5EZ,EAAAA,EAAAA,KAAC+H,EAAAA,EAAI,CAACR,QAAQ,QAAQS,KAAK,WAAWX,QAAQ,MAAMY,OAAO,UAAU/C,KAAMtE,EAAE,iCASxGR,EAAAA,EAAAA,MAACuG,EAAAA,EAAG,CAACzG,UAAU,OAAMC,SAAA,EAClBH,EAAAA,EAAAA,KAAC4G,EAAAA,EAAG,CAACC,GAAI,EAAG3G,UAAU,cAAaC,UAC/BH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDC,EAAAA,EAAAA,MAACH,EAAAA,EAAKI,KAAI,CAAAF,SAAA,EACNH,EAAAA,EAAAA,KAAA,MAAAG,SAAKS,EAAE,wBACPZ,EAAAA,EAAAA,KAAA,KAAAG,SAAIS,EAAE,iCACNZ,EAAAA,EAAAA,KAACkI,EAAAA,EAAM,CACHpI,QAAQ,UACRqI,QAASA,IAAM5C,EAAiB,WAAWpF,SAE1CS,EAAE,0BAKnBZ,EAAAA,EAAAA,KAAC4G,EAAAA,EAAG,CAACC,GAAI,EAAG3G,UAAU,cAAaC,UAC/BH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDC,EAAAA,EAAAA,MAACH,EAAAA,EAAKI,KAAI,CAAAF,SAAA,EACNH,EAAAA,EAAAA,KAAA,MAAAG,SAAKS,EAAE,gBACPZ,EAAAA,EAAAA,KAAA,KAAAG,SAAIS,EAAE,2CACNZ,EAAAA,EAAAA,KAACkI,EAAAA,EAAM,CACHpI,QAAQ,UACRqI,QAASA,IAAM5C,EAAiB,aAAapF,SAE5CS,EAAE,kC", "sources": ["pages/customer/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, But<PERSON>, Spinner } from 'react-bootstrap';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { useNavigate } from 'react-router-dom';\nimport { getSupabase } from '../../supabaseClient';\n\n// Helper function to process dashboard data\nconst processDashboardData = (assets, earnings, orders) => {\n    // Calculate available balance (FIL currency)\n    const filAsset = assets?.find(asset => asset.currency_code === 'FIL') || {};\n    const availableBalance = filAsset.balance_available || 0;\n\n    // Calculate total earnings\n    const totalEarnings = earnings?.reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;\n\n    // Calculate yesterday's earnings\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    const yesterdayStart = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());\n    const yesterdayEnd = new Date(yesterdayStart);\n    yesterdayEnd.setDate(yesterdayEnd.getDate() + 1);\n\n    const yesterdayEarnings = earnings?.filter(earning => {\n        const earningDate = new Date(earning.created_at);\n        return earningDate >= yesterdayStart && earningDate < yesterdayEnd;\n    }).reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;\n\n    // Calculate power pledge (sum of active orders' pledge costs)\n    const now = new Date();\n    const powerPledge = orders?.filter(order => {\n        const startDate = new Date(order.start_at);\n        const endDate = new Date(order.end_at);\n        return startDate <= now && endDate >= now;\n    }).reduce((sum, order) => sum + (order.pledge_cost || 0), 0) || 0;\n\n    // Generate chart data for the last 7 days\n    const chartData = [];\n    for (let i = 6; i >= 0; i--) {\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n        const dateStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n        const dateEnd = new Date(dateStart);\n        dateEnd.setDate(dateEnd.getDate() + 1);\n\n        const dayEarnings = earnings?.filter(earning => {\n            const earningDate = new Date(earning.created_at);\n            return earningDate >= dateStart && earningDate < dateEnd;\n        }).reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;\n\n        chartData.push({\n            name: `${date.getMonth() + 1}/${date.getDate()}`,\n            fil: dayEarnings,\n            usd: dayEarnings * 4.05 // Approximate FIL to USD conversion\n        });\n    }\n\n    return {\n        stats: {\n            totalEarnings,\n            yesterdayEarnings,\n            availableBalance,\n            powerPledge\n        },\n        chartData\n    };\n};\n\nconst StatCard = ({ title, value, subValue, variant, loading }) => (\n    <Card className={`bg-${variant} text-white mb-3`}>\n        <Card.Body>\n            <Card.Title>{title}</Card.Title>\n            {loading ? (\n                <div className=\"d-flex align-items-center\">\n                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                    <span>Loading...</span>\n                </div>\n            ) : (\n                <>\n                    <h3>{value}</h3>\n                    {subValue && <p>{subValue}</p>}\n                </>\n            )}\n        </Card.Body>\n    </Card>\n);\n\nconst CustomerDashboard = () => {\n    const { t } = useTranslation();\n    const navigate = useNavigate();\n\n    // State management\n    const [loading, setLoading] = useState(true);\n    const [dashboardData, setDashboardData] = useState({\n        totalEarnings: 0,\n        yesterdayEarnings: 0,\n        availableBalance: 0,\n        powerPledge: 0\n    });\n    const [chartData, setChartData] = useState([]);\n    const [error, setError] = useState(null);\n\n    // Fetch dashboard data from Supabase\n    useEffect(() => {\n        const fetchDashboardData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) {\n                setError('Failed to initialize Supabase client');\n                setLoading(false);\n                return;\n            }\n\n            try {\n                setLoading(true);\n                const { data: { user } } = await supabase.auth.getUser();\n\n                if (!user) {\n                    setError('User not logged in');\n                    setLoading(false);\n                    return;\n                }\n\n                // Fetch user assets (available balance)\n                const { data: assets, error: assetsError } = await supabase\n                    .from('user_assets')\n                    .select('currency_code, balance_available, balance_total')\n                    .eq('user_id', user.id);\n\n                if (assetsError) {\n                    console.error('Error fetching assets:', assetsError);\n                }\n\n                // Fetch earnings data\n                const { data: earnings, error: earningsError } = await supabase\n                    .from('order_distributions')\n                    .select('reward_amount, created_at')\n                    .eq('customer_id', user.id)\n                    .order('created_at', { ascending: false });\n\n                if (earningsError) {\n                    console.error('Error fetching earnings:', earningsError);\n                }\n\n                // Fetch orders for power pledge calculation\n                const { data: orders, error: ordersError } = await supabase\n                    .from('orders')\n                    .select('pledge_cost, shares, start_at, end_at')\n                    .eq('customer_id', user.id)\n                    .eq('review_status', 'approved');\n\n                if (ordersError) {\n                    console.error('Error fetching orders:', ordersError);\n                }\n\n                // Process the data\n                const processedData = processDashboardData(assets, earnings, orders);\n                setDashboardData(processedData.stats);\n                setChartData(processedData.chartData);\n\n            } catch (error) {\n                console.error('Error fetching dashboard data:', error);\n                setError('Failed to load dashboard data');\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchDashboardData();\n    }, []);\n\n    const handleNavigation = (path) => {\n        console.log('Navigating to:', path);\n        console.log('Current URL:', window.location.href);\n        navigate(path);\n    };\n\n    // Format number for display\n    const formatNumber = (num) => {\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n        }).format(num || 0);\n    };\n\n    // Format USD estimate\n    const formatUSD = (filAmount) => {\n        const usdAmount = (filAmount || 0) * 4.05; // Approximate conversion rate\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(usdAmount);\n    };\n\n    if (error) {\n        return (\n            <Container fluid>\n                <Row className=\"mb-3\">\n                    <Col>\n                        <h2>{t('dashboard')}</h2>\n                        <div className=\"alert alert-danger\">{error}</div>\n                    </Col>\n                </Row>\n            </Container>\n        );\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('dashboard')}</h2>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col md={3}>\n                    <StatCard\n                        title={t('total_earnings')}\n                        value={`${formatNumber(dashboardData.totalEarnings)} FIL`}\n                        subValue={`≈ ${formatUSD(dashboardData.totalEarnings)}`}\n                        variant=\"primary\"\n                        loading={loading}\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('yesterday_earnings')}\n                        value={`${formatNumber(dashboardData.yesterdayEarnings)} FIL`}\n                        subValue={`≈ ${formatUSD(dashboardData.yesterdayEarnings)}`}\n                        variant=\"success\"\n                        loading={loading}\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('available_balance')}\n                        value={`${formatNumber(dashboardData.availableBalance)} FIL`}\n                        subValue={`≈ ${formatUSD(dashboardData.availableBalance)}`}\n                        variant=\"info\"\n                        loading={loading}\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('power_pledge')}\n                        value={`${formatNumber(dashboardData.powerPledge)} FIL`}\n                        subValue={`≈ ${formatUSD(dashboardData.powerPledge)}`}\n                        variant=\"warning\"\n                        loading={loading}\n                    />\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('earnings_trend')}</Card.Title>\n                            {loading ? (\n                                <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\n                                    <Spinner animation=\"border\" />\n                                    <span className=\"ms-2\">{t('loading')}</span>\n                                </div>\n                            ) : (\n                                <ResponsiveContainer width=\"100%\" height={400}>\n                                    <LineChart data={chartData}>\n                                        <CartesianGrid strokeDasharray=\"3 3\" />\n                                        <XAxis dataKey=\"name\" />\n                                        <YAxis yAxisId=\"left\" label={{ value: 'FIL', angle: -90, position: 'insideLeft' }} />\n                                        <YAxis yAxisId=\"right\" orientation=\"right\" label={{ value: 'USD', angle: -90, position: 'insideRight' }} />\n                                        <Tooltip\n                                            formatter={(value, name) => [\n                                                name === 'fil' ? `${formatNumber(value)} FIL` : formatUSD(value / 4.05),\n                                                name === 'fil' ? t('FIL_earnings') : t('USD_estimate')\n                                            ]}\n                                        />\n                                        <Legend />\n                                        <Line yAxisId=\"left\" type=\"monotone\" dataKey=\"fil\" stroke=\"#8884d8\" name={t('FIL_earnings')} />\n                                        <Line yAxisId=\"right\" type=\"monotone\" dataKey=\"usd\" stroke=\"#82ca9d\" name={t('USD_estimate')} />\n                                    </LineChart>\n                                </ResponsiveContainer>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n             <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('wallet_management')}</h4>\n                            <p>{t('manage_your_digital_assets')}</p>\n                            <Button\n                                variant=\"primary\"\n                                onClick={() => handleNavigation('/wallet')}\n                            >\n                                {t('enter_wallet')}\n                            </Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('buy_power')}</h4>\n                            <p>{t('view_and_purchase_new_power_products')}</p>\n                            <Button\n                                variant=\"success\"\n                                onClick={() => handleNavigation('/products')}\n                            >\n                                {t('browse_products')}\n                            </Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n        </Container>\n    );\n};\n\nexport default CustomerDashboard;"], "names": ["StatCard", "_ref", "title", "value", "subValue", "variant", "loading", "_jsx", "Card", "className", "children", "_jsxs", "Body", "Title", "Spinner", "animation", "size", "_Fragment", "CustomerDashboard", "t", "useTranslation", "navigate", "useNavigate", "setLoading", "useState", "dashboardData", "setDashboardData", "totalEarnings", "yesterdayEarnings", "availableBalance", "powerPledge", "chartData", "setChartData", "error", "setError", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "assets", "assetsError", "from", "select", "eq", "id", "console", "earnings", "earningsError", "order", "ascending", "orders", "ordersError", "processedData", "processDashboardData", "find", "asset", "currency_code", "balance_available", "reduce", "sum", "earning", "reward_amount", "yesterday", "Date", "setDate", "getDate", "yesterdayStart", "getFullYear", "getMonth", "yesterdayEnd", "filter", "earningDate", "created_at", "now", "startDate", "start_at", "endDate", "end_at", "pledge_cost", "i", "date", "dateStart", "dateEnd", "dayEarnings", "push", "name", "fil", "usd", "stats", "fetchDashboardData", "handleNavigation", "path", "log", "window", "location", "href", "formatNumber", "num", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatUSD", "filAmount", "usdAmount", "style", "currency", "Container", "fluid", "Row", "Col", "md", "height", "ResponsiveContainer", "width", "Line<PERSON>hart", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XAxis", "dataKey", "YA<PERSON>s", "yAxisId", "label", "angle", "position", "orientation", "<PERSON><PERSON><PERSON>", "formatter", "Legend", "Line", "type", "stroke", "<PERSON><PERSON>", "onClick"], "sourceRoot": ""}