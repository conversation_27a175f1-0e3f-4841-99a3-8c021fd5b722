"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[923],{923:(e,r,s)=>{s.r(r),s.d(r,{default:()=>j});var a=s(5043),t=s(3519),l=s(7417),n=s(1719),i=s(1072),c=s(8602),d=s(8628),o=s(3722),h=s(4282),m=s(3204),f=s(4312),x=s(4117),u=s(579);const j=()=>{const{t:e}=(0,x.Bd)(),[r,s]=(0,a.useState)(!0),[j,_]=(0,a.useState)([]),[g,p]=(0,a.useState)([]),[A,v]=(0,a.useState)(new Set),[N,b]=(0,a.useState)(null),[y,w]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{const r=(0,f.b)();if(!r)return;s(!0),b(null);let a=null;try{const{data:{user:t}}=await r.auth.getUser();if(a=t,!t)return b(e("user_not_logged_in")),void s(!1);const{data:l,error:n}=await r.from("agent_profiles").select("maker_id").eq("user_id",t.id).single();if(n)return console.error("Error fetching agent profile:",n),b(e("agent_profile_not_found")),void s(!1);const{data:i,error:c}=await r.from("customer_profiles").select("user_id").eq("agent_id",t.id).limit(100);if(c)return console.error("Error fetching customer profiles:",c),b(e("failed_to_load_referral_data")),void s(!1);const d=i.map(e=>e.user_id).filter(Boolean),{data:o,error:h}=await r.from("users").select("id, email, referred_by, created_at, role, invite_code").in("id",d);if(h)return console.error("Error fetching user info:",h),b(e("failed_to_load_referral_data")),void s(!1);null;const m=o||[],f=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;if(s>=a||0===e.length)return[];try{const{data:t,error:l}=await r.from("users").select("id, email, referred_by, created_at, role, invite_code").in("id",e).limit(50);if(l)return console.error(`Error fetching referrers at level ${s}:`,l),[];const n=t||[],i=[...new Set(n.map(e=>e.referred_by).filter(Boolean))],c=await f(i,s+1,a);return[...n,...c]}catch(N){return console.error(`Error in fetchReferrers at level ${s}:`,N),[]}},x=[...new Set(m.map(e=>e.referred_by).filter(Boolean))],u=await f(x),j=[...m,...u].filter((e,r,s)=>r===s.findIndex(r=>r.id===e.id)),g=$(j);_(g),p(g)}catch(l){console.error("Error:",l);try{var t;console.log("Attempting fallback query...");if(!(null===(t=a)||void 0===t?void 0:t.id)){const{data:{user:s}}=await r.auth.getUser();if(!s)return void b(e("user_not_logged_in"));a=s}const{data:s,error:l}=await r.from("customer_profiles").select("\n                            users (\n                                id,\n                                email,\n                                created_at,\n                                role,\n                                invite_code\n                            )\n                        ").eq("agent_id",a.id).limit(50);if(!l&&s&&s.length>0){const r=s.map(e=>e.users).filter(Boolean).map(e=>({...e,referred_by:null,children:[]}));console.log("Fallback successful, loaded",r.length,"users"),_(r),p(r),b(e("limited_referral_data"))}else console.log("Fallback failed or no data:",l),_([]),p([]),b(e("no_referral_data"))}catch(n){console.error("Fallback error:",n),_([]),p([]),b(e("unexpected_error"))}}finally{s(!1)}})()},[e]),(0,a.useEffect)(()=>{if(!y.trim())return void p(j);const e=r=>r.filter(r=>{const s=r.email.toLowerCase().includes(y.toLowerCase())||r.id.toLowerCase().includes(y.toLowerCase()),a=r.children?e(r.children):[];return s||a.length>0}).map(r=>({...r,children:r.children?e(r.children):[]}));p(e(j))},[y,j]);const $=e=>{if(!e||0===e.length)return[];const r=new Map,s=[];return e.forEach(e=>{r.set(e.id,{...e,children:[]})}),e.forEach(e=>{if(e.referred_by){const a=r.get(e.referred_by);a?a.children.push(r.get(e.id)):s.push(r.get(e.id))}else s.push(r.get(e.id))}),s},k=e=>{switch(e){case"maker":return(0,u.jsx)(m.YXz,{className:"text-primary me-1"});case"agent":return(0,u.jsx)(m.x$1,{className:"text-success me-1"});case"customer":return(0,u.jsx)(m.x$1,{className:"text-info me-1"});default:return(0,u.jsx)(m.x$1,{className:"text-secondary me-1"})}},C=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const s=e.children&&e.children.length>0,a=A.has(e.id),t=20*r;return(0,u.jsxs)("div",{className:"mb-1",children:[(0,u.jsxs)("div",{className:"d-flex align-items-center p-2 border-bottom",style:{paddingLeft:`${t}px`,cursor:s?"pointer":"default"},onClick:()=>s&&(e=>{const r=new Set(A);r.has(e)?r.delete(e):r.add(e),v(r)})(e.id),children:[s?a?(0,u.jsx)(m.Vr3,{className:"me-2 text-muted"}):(0,u.jsx)(m.X6T,{className:"me-2 text-muted"}):(0,u.jsx)("span",{className:"me-4"}),k(e.role),(0,u.jsx)("span",{className:"me-2",children:e.email}),(0,u.jsx)("span",{className:"text-muted small",children:e.invite_code}),s&&(0,u.jsx)("span",{className:"ms-auto badge bg-secondary",children:e.children.length})]}),s&&a&&(0,u.jsx)("div",{children:e.children.map(e=>C(e,r+1))})]},e.id)};return r?(0,u.jsx)(t.A,{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:(0,u.jsxs)("div",{className:"text-center",children:[(0,u.jsx)(l.A,{animation:"border",role:"status",className:"mb-3"}),(0,u.jsx)("div",{children:e("loading_referral_data")})]})}):N?(0,u.jsx)(t.A,{children:(0,u.jsx)(n.A,{variant:"danger",children:N})}):(0,u.jsxs)(t.A,{children:[(0,u.jsx)(i.A,{className:"mb-4",children:(0,u.jsxs)(c.A,{children:[(0,u.jsx)("h2",{children:e("referral_relationships")}),(0,u.jsx)("p",{className:"text-muted",children:e("referral_tree_description")})]})}),(0,u.jsx)(i.A,{className:"mb-3",children:(0,u.jsx)(c.A,{children:(0,u.jsx)(d.A,{children:(0,u.jsx)(d.A.Body,{children:(0,u.jsxs)(i.A,{className:"align-items-end",children:[(0,u.jsx)(c.A,{md:4,children:(0,u.jsxs)(o.A.Group,{children:[(0,u.jsx)(o.A.Label,{children:e("search_users")}),(0,u.jsx)(o.A.Control,{type:"text",placeholder:e("search_by_email_or_id"),value:y,onChange:e=>w(e.target.value)})]})}),(0,u.jsx)(c.A,{md:4,children:(0,u.jsxs)("div",{className:"d-flex gap-2",children:[(0,u.jsxs)(h.A,{variant:"outline-primary",size:"sm",onClick:()=>{const e=new Set,r=s=>{s.forEach(s=>{s.children&&s.children.length>0&&(e.add(s.id),r(s.children))})};r(g),v(e)},children:[(0,u.jsx)(m.xKl,{className:"me-1"}),e("expand_all")]}),(0,u.jsxs)(h.A,{variant:"outline-secondary",size:"sm",onClick:()=>{v(new Set)},children:[(0,u.jsx)(m.f93,{className:"me-1"}),e("collapse_all")]})]})})]})})})})}),(0,u.jsxs)(i.A,{className:"mb-3",children:[(0,u.jsx)(c.A,{md:4,children:(0,u.jsx)(d.A,{className:"bg-primary text-white",children:(0,u.jsxs)(d.A.Body,{children:[(0,u.jsx)("h5",{children:e("total_users")}),(0,u.jsx)("h3",{children:(e=>{let r=0;const s=e=>{e.forEach(e=>{r++,e.children&&e.children.length>0&&s(e.children)})};return s(e),r})(g)})]})})}),(0,u.jsx)(c.A,{md:4,children:(0,u.jsx)(d.A,{className:"bg-success text-white",children:(0,u.jsxs)(d.A.Body,{children:[(0,u.jsx)("h5",{children:e("root_users")}),(0,u.jsx)("h3",{children:g.length})]})})}),(0,u.jsx)(c.A,{md:4,children:(0,u.jsx)(d.A,{className:"bg-info text-white",children:(0,u.jsxs)(d.A.Body,{children:[(0,u.jsx)("h5",{children:e("expanded_nodes")}),(0,u.jsx)("h3",{children:A.size})]})})})]}),(0,u.jsx)(i.A,{children:(0,u.jsx)(c.A,{children:(0,u.jsxs)(d.A,{children:[(0,u.jsxs)(d.A.Header,{children:[(0,u.jsx)("h5",{className:"mb-0",children:e("referral_tree")}),(0,u.jsx)("small",{className:"text-muted",children:e("click_to_expand_collapse")})]}),(0,u.jsx)(d.A.Body,{style:{maxHeight:"600px",overflowY:"auto"},children:0===g.length?(0,u.jsx)("div",{className:"text-center text-muted py-4",children:e(y?"no_search_results":"no_referral_data")}):(0,u.jsx)("div",{children:g.map(e=>C(e))})})]})})})]})}},1072:(e,r,s)=>{s.d(r,{A:()=>d});var a=s(8139),t=s.n(a),l=s(5043),n=s(7852),i=s(579);const c=l.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:l="div",...c}=e;const d=(0,n.oU)(s,"row"),o=(0,n.gy)(),h=(0,n.Jm)(),m=`${d}-cols`,f=[];return o.forEach(e=>{const r=c[e];let s;delete c[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const a=e!==h?`-${e}`:"";null!=s&&f.push(`${m}${a}-${s}`)}),(0,i.jsx)(l,{ref:r,...c,className:t()(a,d,...f)})});c.displayName="Row";const d=c},1719:(e,r,s)=>{s.d(r,{A:()=>A});var a=s(8139),t=s.n(a),l=s(5043),n=s(1969),i=s(6618),c=s(7852),d=s(4488),o=s(579);const h=(0,d.A)("h4");h.displayName="DivStyledAsH4";const m=l.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:l=h,...n}=e;return a=(0,c.oU)(a,"alert-heading"),(0,o.jsx)(l,{ref:r,className:t()(s,a),...n})});m.displayName="AlertHeading";const f=m;var x=s(7071);const u=l.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:l=x.A,...n}=e;return a=(0,c.oU)(a,"alert-link"),(0,o.jsx)(l,{ref:r,className:t()(s,a),...n})});u.displayName="AlertLink";const j=u;var _=s(8072),g=s(5632);const p=l.forwardRef((e,r)=>{const{bsPrefix:s,show:a=!0,closeLabel:l="Close alert",closeVariant:d,className:h,children:m,variant:f="primary",onClose:x,dismissible:u,transition:j=_.A,...p}=(0,n.Zw)(e,{show:"onClose"}),A=(0,c.oU)(s,"alert"),v=(0,i.A)(e=>{x&&x(!1,e)}),N=!0===j?_.A:j,b=(0,o.jsxs)("div",{role:"alert",...N?void 0:p,ref:r,className:t()(h,A,f&&`${A}-${f}`,u&&`${A}-dismissible`),children:[u&&(0,o.jsx)(g.A,{onClick:v,"aria-label":l,variant:d}),m]});return N?(0,o.jsx)(N,{unmountOnExit:!0,...p,ref:void 0,in:a,children:b}):a?b:null});p.displayName="Alert";const A=Object.assign(p,{Link:j,Heading:f})},7417:(e,r,s)=>{s.d(r,{A:()=>d});var a=s(8139),t=s.n(a),l=s(5043),n=s(7852),i=s(579);const c=l.forwardRef((e,r)=>{let{bsPrefix:s,variant:a,animation:l="border",size:c,as:d="div",className:o,...h}=e;s=(0,n.oU)(s,"spinner");const m=`${s}-${l}`;return(0,i.jsx)(d,{ref:r,...h,className:t()(o,m,c&&`${m}-${c}`,a&&`text-${a}`)})});c.displayName="Spinner";const d=c}}]);
//# sourceMappingURL=923.97b6a4c2.chunk.js.map