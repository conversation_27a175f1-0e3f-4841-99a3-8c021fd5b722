{"version": 3, "file": "static/js/923.97b6a4c2.chunk.js", "mappings": "qQAMA,MAsfA,EAtfkBA,KACd,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,KAC1CG,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAS,KAC1CK,EAAeC,IAAoBN,EAAAA,EAAAA,UAAS,IAAIO,MAChDC,EAAOC,IAAYT,EAAAA,EAAAA,UAAS,OAC5BU,EAAYC,IAAiBX,EAAAA,EAAAA,UAAS,KAE7CY,EAAAA,EAAAA,WAAU,KACoBC,WACtB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEff,GAAW,GACXU,EAAS,MAET,IAAIO,EAAc,KAKlB,IACI,MAAQC,MAAM,KAAEC,UAAiBJ,EAASK,KAAKC,UAG/C,GAFAJ,EAAcE,GAETA,EAGD,OAFAT,EAASb,EAAE,4BACXG,GAAW,GAKf,MAAQkB,KAAMI,EAAcb,MAAOc,SAAqBR,EACnDS,KAAK,kBACLC,OAAO,YACPC,GAAG,UAAWP,EAAKQ,IACnBC,SAEL,GAAIL,EAIA,OAHAM,QAAQpB,MAAM,gCAAiCc,GAC/Cb,EAASb,EAAE,iCACXG,GAAW,GAKf,MAAQkB,KAAMY,EAAkBrB,MAAOsB,SAAuBhB,EACzDS,KAAK,qBACLC,OAAO,WACPC,GAAG,WAAYP,EAAKQ,IACpBK,MAAM,KAEX,GAAID,EAIA,OAHAF,QAAQpB,MAAM,oCAAqCsB,GACnDrB,EAASb,EAAE,sCACXG,GAAW,GAKf,MAAMiC,EAAUH,EAAiBI,IAAIC,GAAKA,EAAEC,SAASC,OAAOC,UAEpDpB,KAAMqB,EAAW9B,MAAO+B,SAAqBzB,EAChDS,KAAK,SACLC,OAAO,yDACPgB,GAAG,KAAMR,GAEd,GAAIO,EAIA,OAHAX,QAAQpB,MAAM,4BAA6B+B,GAC3C9B,EAASb,EAAE,sCACXG,GAAW,GApDE,KAgEjB,MAAM0C,EAAgBH,GAAa,GAG7BI,EAAiB7B,eAAOmB,GAAsC,IAA7BW,EAAKC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAAGG,EAAQH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACzD,GAAID,GAASI,GAA+B,IAAnBf,EAAQa,OAC7B,MAAO,GAGX,IACI,MAAQ5B,KAAM+B,EAAcxC,MAAOyC,SAAwBnC,EACtDS,KAAK,SACLC,OAAO,yDACPgB,GAAG,KAAMR,GACTD,MAAM,IAEX,GAAIkB,EAEA,OADArB,QAAQpB,MAAM,qCAAqCmC,KAAUM,GACtD,GAGX,MAAMC,EAAYF,GAAgB,GAG5BG,EAAe,IAAI,IAAI5C,IAAI2C,EAC5BjB,IAAImB,GAAKA,EAAEC,aACXjB,OAAOC,WAGNiB,QAA2BZ,EAAeS,EAAcR,EAAQ,EAAGI,GAEzE,MAAO,IAAIG,KAAcI,EAC7B,CAAE,MAAO9C,GAEL,OADAoB,QAAQpB,MAAM,oCAAoCmC,KAAUnC,GACrD,EACX,CACJ,EAGM+C,EAAqB,IAAI,IAAIhD,IAAIkC,EAClCR,IAAImB,GAAKA,EAAEC,aACXjB,OAAOC,WAENa,QAAkBR,EAAea,GAMjCC,EAHW,IAAIf,KAAkBS,GAGVd,OAAO,CAAClB,EAAMuC,EAAOC,IAC9CD,IAAUC,EAAKC,UAAUP,GAAKA,EAAE1B,KAAOR,EAAKQ,KAI1CkC,EAAOC,EAAkBL,GAC/BtD,EAAgB0D,GAChBxD,EAAgBwD,EAEpB,CAAE,MAAOE,GACLlC,QAAQpB,MAAM,SAAUsD,GAGxB,IAAK,IAADC,EACAnC,QAAQoC,IAAI,gCAIZ,KAD+B,QAAdD,EAAG/C,SAAW,IAAA+C,OAAA,EAAXA,EAAarC,IACf,CACd,MAAQT,MAAQC,KAAM+C,UAAyBnD,EAASK,KAAKC,UAC7D,IAAK6C,EAED,YADAxD,EAASb,EAAE,uBAGfoB,EAAciD,CAClB,CAEA,MAAQhD,KAAMiD,EAAiB1D,MAAO2D,SAAsBrD,EACvDS,KAAK,qBACLC,OAAO,gTASPC,GAAG,WAAYT,EAAYU,IAC3BK,MAAM,IAEX,IAAKoC,GAAeD,GAAmBA,EAAgBrB,OAAS,EAAG,CAC/D,MAAMuB,EAAcF,EACfjC,IAAIoC,GAAKA,EAAEC,OACXlC,OAAOC,SACPJ,IAAImB,IAAC,IACCA,EACHC,YAAa,KACbkB,SAAU,MAGlB3C,QAAQoC,IAAI,8BAA+BI,EAAYvB,OAAQ,SAC/D3C,EAAgBkE,GAChBhE,EAAgBgE,GAChB3D,EAASb,EAAE,yBACf,MACIgC,QAAQoC,IAAI,8BAA+BG,GAC3CjE,EAAgB,IAChBE,EAAgB,IAChBK,EAASb,EAAE,oBAEnB,CAAE,MAAO4E,GACL5C,QAAQpB,MAAM,kBAAmBgE,GACjCtE,EAAgB,IAChBE,EAAgB,IAChBK,EAASb,EAAE,oBACf,CACJ,CAAC,QACGG,GAAW,EACf,GAGJ0E,IACD,CAAC7E,KAGJgB,EAAAA,EAAAA,WAAU,KACN,IAAKF,EAAWgE,OAEZ,YADAtE,EAAgBH,GAIpB,MAAM0E,EAAcC,GACTA,EAAMxC,OAAOyC,IAChB,MAAMC,EAAgBD,EAAKE,MAAMC,cAAcC,SAASvE,EAAWsE,gBAC/CH,EAAKnD,GAAGsD,cAAcC,SAASvE,EAAWsE,eAExDE,EAAmBL,EAAKN,SAAWI,EAAWE,EAAKN,UAAY,GAErE,OAAOO,GAAiBI,EAAiBrC,OAAS,IACnDZ,IAAI4C,IAAI,IACJA,EACHN,SAAUM,EAAKN,SAAWI,EAAWE,EAAKN,UAAY,MAI9DnE,EAAgBuE,EAAW1E,KAC5B,CAACS,EAAYT,IAEhB,MAAM4D,EAAqBS,IACvB,IAAKA,GAA0B,IAAjBA,EAAMzB,OAChB,MAAO,GAGX,MAAMsC,EAAU,IAAIC,IACdC,EAAY,GA0BlB,OAvBAf,EAAMgB,QAAQpE,IACViE,EAAQI,IAAIrE,EAAKQ,GAAI,IACdR,EACHqD,SAAU,OAKlBD,EAAMgB,QAAQpE,IACV,GAAIA,EAAKmC,YAAa,CAClB,MAAMmC,EAASL,EAAQM,IAAIvE,EAAKmC,aAC5BmC,EACAA,EAAOjB,SAASmB,KAAKP,EAAQM,IAAIvE,EAAKQ,KAGtC2D,EAAUK,KAAKP,EAAQM,IAAIvE,EAAKQ,IAExC,MAEI2D,EAAUK,KAAKP,EAAQM,IAAIvE,EAAKQ,OAIjC2D,GAmCLM,EAAeC,IACjB,OAAQA,GACJ,IAAK,QACD,OAAOC,EAAAA,EAAAA,KAACC,EAAAA,IAAO,CAACC,UAAU,sBAC9B,IAAK,QACD,OAAOF,EAAAA,EAAAA,KAACG,EAAAA,IAAM,CAACD,UAAU,sBAC7B,IAAK,WACD,OAAOF,EAAAA,EAAAA,KAACG,EAAAA,IAAM,CAACD,UAAU,mBAC7B,QACI,OAAOF,EAAAA,EAAAA,KAACG,EAAAA,IAAM,CAACD,UAAU,0BAI/BE,EAAiB,SAACpB,GAAqB,IAAflC,EAAKC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAClC,MAAMsD,EAAcrB,EAAKN,UAAYM,EAAKN,SAAS1B,OAAS,EACtDsD,EAAa9F,EAAc+F,IAAIvB,EAAKnD,IACpC2E,EAAsB,GAAR1D,EAEpB,OACI2D,EAAAA,EAAAA,MAAA,OAAmBP,UAAU,OAAMxB,SAAA,EAC/B+B,EAAAA,EAAAA,MAAA,OACIP,UAAU,8CACVQ,MAAO,CAAEF,YAAa,GAAGA,MAAiBG,OAAQN,EAAc,UAAY,WAC5EO,QAASA,IAAMP,GAvDXQ,KAChB,MAAMC,EAAc,IAAIpG,IAAIF,GACxBsG,EAAYP,IAAIM,GAChBC,EAAYC,OAAOF,GAEnBC,EAAYE,IAAIH,GAEpBpG,EAAiBqG,IAgDyBG,CAAWjC,EAAKnD,IAAI6C,SAAA,CAEjD2B,EACGC,GACIN,EAAAA,EAAAA,KAACkB,EAAAA,IAAa,CAAChB,UAAU,qBAEzBF,EAAAA,EAAAA,KAACmB,EAAAA,IAAc,CAACjB,UAAU,qBAG9BF,EAAAA,EAAAA,KAAA,QAAME,UAAU,SAGnBJ,EAAYd,EAAKe,OAElBC,EAAAA,EAAAA,KAAA,QAAME,UAAU,OAAMxB,SACjBM,EAAKE,SAGVc,EAAAA,EAAAA,KAAA,QAAME,UAAU,mBAAkBxB,SAC7BM,EAAKoC,cAGTf,IACGL,EAAAA,EAAAA,KAAA,QAAME,UAAU,6BAA4BxB,SACvCM,EAAKN,SAAS1B,YAK1BqD,GAAeC,IACZN,EAAAA,EAAAA,KAAA,OAAAtB,SACKM,EAAKN,SAAStC,IAAIiF,GAASjB,EAAeiB,EAAOvE,EAAQ,QAnC5DkC,EAAKnD,GAwCvB,EAoBA,OAAI5B,GAEI+F,EAAAA,EAAAA,KAACsB,EAAAA,EAAS,CAACpB,UAAU,mDAAmDQ,MAAO,CAAEa,UAAW,SAAU7C,UAClG+B,EAAAA,EAAAA,MAAA,OAAKP,UAAU,cAAaxB,SAAA,EACxBsB,EAAAA,EAAAA,KAACwB,EAAAA,EAAO,CAACC,UAAU,SAAS1B,KAAK,SAASG,UAAU,UACpDF,EAAAA,EAAAA,KAAA,OAAAtB,SAAM3E,EAAE,gCAMpBY,GAEIqF,EAAAA,EAAAA,KAACsB,EAAAA,EAAS,CAAA5C,UACNsB,EAAAA,EAAAA,KAAC0B,EAAAA,EAAK,CAACC,QAAQ,SAAQjD,SAClB/D,OAOb8F,EAAAA,EAAAA,MAACa,EAAAA,EAAS,CAAA5C,SAAA,EACNsB,EAAAA,EAAAA,KAAC4B,EAAAA,EAAG,CAAC1B,UAAU,OAAMxB,UACjB+B,EAAAA,EAAAA,MAACoB,EAAAA,EAAG,CAAAnD,SAAA,EACAsB,EAAAA,EAAAA,KAAA,MAAAtB,SAAK3E,EAAE,6BACPiG,EAAAA,EAAAA,KAAA,KAAGE,UAAU,aAAYxB,SAAE3E,EAAE,qCAIrCiG,EAAAA,EAAAA,KAAC4B,EAAAA,EAAG,CAAC1B,UAAU,OAAMxB,UACjBsB,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAAAnD,UACAsB,EAAAA,EAAAA,KAAC8B,EAAAA,EAAI,CAAApD,UACDsB,EAAAA,EAAAA,KAAC8B,EAAAA,EAAKC,KAAI,CAAArD,UACN+B,EAAAA,EAAAA,MAACmB,EAAAA,EAAG,CAAC1B,UAAU,kBAAiBxB,SAAA,EAC5BsB,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAACG,GAAI,EAAEtD,UACP+B,EAAAA,EAAAA,MAACwB,EAAAA,EAAKC,MAAK,CAAAxD,SAAA,EACPsB,EAAAA,EAAAA,KAACiC,EAAAA,EAAKE,MAAK,CAAAzD,SAAE3E,EAAE,mBACfiG,EAAAA,EAAAA,KAACiC,EAAAA,EAAKG,QAAO,CACTC,KAAK,OACLC,YAAavI,EAAE,yBACfwI,MAAO1H,EACP2H,SAAWC,GAAM3H,EAAc2H,EAAEC,OAAOH,eAIpDvC,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAACG,GAAI,EAAEtD,UACP+B,EAAAA,EAAAA,MAAA,OAAKP,UAAU,eAAcxB,SAAA,EACzB+B,EAAAA,EAAAA,MAACkC,EAAAA,EAAM,CACHhB,QAAQ,kBACRiB,KAAK,KACLhC,QAxJtBiC,KACd,MAAMC,EAAa,IAAIpI,IACjBqI,EAAkBhE,IACpBA,EAAMU,QAAQT,IACNA,EAAKN,UAAYM,EAAKN,SAAS1B,OAAS,IACxC8F,EAAW9B,IAAIhC,EAAKnD,IACpBkH,EAAe/D,EAAKN,cAIhCqE,EAAezI,GACfG,EAAiBqI,IA6IsCpE,SAAA,EAEnBsB,EAAAA,EAAAA,KAACgD,EAAAA,IAAiB,CAAC9C,UAAU,SAC5BnG,EAAE,kBAEP0G,EAAAA,EAAAA,MAACkC,EAAAA,EAAM,CACHhB,QAAQ,oBACRiB,KAAK,KACLhC,QAlJpBqC,KAChBxI,EAAiB,IAAIC,MAiJoCgE,SAAA,EAErBsB,EAAAA,EAAAA,KAACkD,EAAAA,IAAmB,CAAChD,UAAU,SAC9BnG,EAAE,oCAUnC0G,EAAAA,EAAAA,MAACmB,EAAAA,EAAG,CAAC1B,UAAU,OAAMxB,SAAA,EACjBsB,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAACG,GAAI,EAAEtD,UACPsB,EAAAA,EAAAA,KAAC8B,EAAAA,EAAI,CAAC5B,UAAU,wBAAuBxB,UACnC+B,EAAAA,EAAAA,MAACqB,EAAAA,EAAKC,KAAI,CAAArD,SAAA,EACNsB,EAAAA,EAAAA,KAAA,MAAAtB,SAAK3E,EAAE,kBACPiG,EAAAA,EAAAA,KAAA,MAAAtB,SA/FDK,KACnB,IAAIoE,EAAQ,EACZ,MAAMC,EAAcC,IAChBA,EAAS5D,QAAQT,IACbmE,IACInE,EAAKN,UAAYM,EAAKN,SAAS1B,OAAS,GACxCoG,EAAWpE,EAAKN,aAK5B,OADA0E,EAAWrE,GACJoE,GAoFkBG,CAAchJ,aAI/B0F,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAACG,GAAI,EAAEtD,UACPsB,EAAAA,EAAAA,KAAC8B,EAAAA,EAAI,CAAC5B,UAAU,wBAAuBxB,UACnC+B,EAAAA,EAAAA,MAACqB,EAAAA,EAAKC,KAAI,CAAArD,SAAA,EACNsB,EAAAA,EAAAA,KAAA,MAAAtB,SAAK3E,EAAE,iBACPiG,EAAAA,EAAAA,KAAA,MAAAtB,SAxFbpE,EAAa0C,iBA4FZgD,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAACG,GAAI,EAAEtD,UACPsB,EAAAA,EAAAA,KAAC8B,EAAAA,EAAI,CAAC5B,UAAU,qBAAoBxB,UAChC+B,EAAAA,EAAAA,MAACqB,EAAAA,EAAKC,KAAI,CAAArD,SAAA,EACNsB,EAAAA,EAAAA,KAAA,MAAAtB,SAAK3E,EAAE,qBACPiG,EAAAA,EAAAA,KAAA,MAAAtB,SAAKlE,EAAcoI,kBAMnC5C,EAAAA,EAAAA,KAAC4B,EAAAA,EAAG,CAAAlD,UACAsB,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAAAnD,UACA+B,EAAAA,EAAAA,MAACqB,EAAAA,EAAI,CAAApD,SAAA,EACD+B,EAAAA,EAAAA,MAACqB,EAAAA,EAAKyB,OAAM,CAAA7E,SAAA,EACRsB,EAAAA,EAAAA,KAAA,MAAIE,UAAU,OAAMxB,SAAE3E,EAAE,oBACxBiG,EAAAA,EAAAA,KAAA,SAAOE,UAAU,aAAYxB,SACxB3E,EAAE,kCAGXiG,EAAAA,EAAAA,KAAC8B,EAAAA,EAAKC,KAAI,CAACrB,MAAO,CAAE8C,UAAW,QAASC,UAAW,QAAS/E,SAC/B,IAAxBpE,EAAa0C,QACVgD,EAAAA,EAAAA,KAAA,OAAKE,UAAU,8BAA6BxB,SAC1B3E,EAAbc,EAAe,oBAAyB,uBAG7CmF,EAAAA,EAAAA,KAAA,OAAAtB,SACKpE,EAAa8B,IAAI4C,GAAQoB,EAAepB,kB,sFC3e7E,MAAM4C,EAAmB8B,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACR3D,EAEA4D,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMM,GAAoBC,EAAAA,EAAAA,IAAmBL,EAAU,OACjDM,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAY1E,QAAQgF,IAClB,MAAMC,EAAYV,EAAMS,GAExB,IAAIE,SADGX,EAAMS,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaJ,EAAgB,IAAII,IAAa,GAChD,MAARE,GAAcH,EAAQ3E,KAAK,GAAG0E,IAAaK,KAASD,QAEtC3E,EAAAA,EAAAA,KAAK+D,EAAW,CAClCH,IAAKA,KACFI,EACH9D,UAAW2E,IAAW3E,EAAW+D,KAAsBO,OAG3D5C,EAAIkD,YAAc,MAClB,S,oHChCA,MAAMC,GAAgBC,EAAAA,EAAAA,GAAiB,MACvCD,EAAcD,YAAc,gBAC5B,MAAMG,EAA4BvB,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClD1D,EAAS,SACT2D,EACAC,GAAIC,EAAYgB,KACbf,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,kBACpB7D,EAAAA,EAAAA,KAAK+D,EAAW,CAClCH,IAAKA,EACL1D,UAAW2E,IAAW3E,EAAW2D,MAC9BG,MAGPiB,EAAaH,YAAc,eAC3B,U,cChBA,MAAMI,EAAyBxB,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/C1D,EAAS,SACT2D,EACAC,GAAIC,EAAYoB,EAAAA,KACbnB,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,eACpB7D,EAAAA,EAAAA,KAAK+D,EAAW,CAClCH,IAAKA,EACL1D,UAAW2E,IAAW3E,EAAW2D,MAC9BG,MAGPkB,EAAUJ,YAAc,YACxB,U,wBCRA,MAAMpD,EAAqBgC,EAAAA,WAAiB,CAAC0B,EAAmBxB,KAC9D,MAAM,SACJC,EAAQ,KACRwB,GAAO,EAAI,WACXC,EAAa,cAAa,aAC1BC,EAAY,UACZrF,EAAS,SACTxB,EAAQ,QACRiD,EAAU,UAAS,QACnB6D,EAAO,YACPC,EAAW,WACXC,EAAaC,EAAAA,KACV3B,IACD4B,EAAAA,EAAAA,IAAgBR,EAAmB,CACrCC,KAAM,YAEFQ,GAAS3B,EAAAA,EAAAA,IAAmBL,EAAU,SACtCiC,GAAcC,EAAAA,EAAAA,GAAiBtD,IAC/B+C,GACFA,GAAQ,EAAO/C,KAGbuD,GAA4B,IAAfN,EAAsBC,EAAAA,EAAOD,EAC1CO,GAAqBxF,EAAAA,EAAAA,MAAM,MAAO,CACtCV,KAAM,WACDiG,OAAqB/I,EAAR+G,EAClBJ,IAAKA,EACL1D,UAAW2E,IAAW3E,EAAW2F,EAAQlE,GAAW,GAAGkE,KAAUlE,IAAW8D,GAAe,GAAGI,iBAC9FnH,SAAU,CAAC+G,IAA4BzF,EAAAA,EAAAA,KAAKkG,EAAAA,EAAa,CACvDtF,QAASkF,EACT,aAAcR,EACd3D,QAAS4D,IACP7G,KAEN,OAAKsH,GACehG,EAAAA,EAAAA,KAAKgG,EAAY,CACnCG,eAAe,KACZnC,EACHJ,SAAK3G,EACLN,GAAI0I,EACJ3G,SAAUuH,IANYZ,EAAOY,EAAQ,OASzCvE,EAAMoD,YAAc,QACpB,QAAesB,OAAOC,OAAO3E,EAAO,CAClC4E,KAAMpB,EACNqB,QAAStB,G,sFCrDX,MAAMzD,EAAuBkC,EAAAA,WAAiB,CAAAC,EAS3CC,KAAQ,IAToC,SAC7CC,EAAQ,QACRlC,EAAO,UACPF,EAAY,SAAQ,KACpBmB,EAEAkB,GAAIC,EAAY,MAAK,UACrB7D,KACG8D,GACJL,EACCE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,WACxC,MAAM2C,EAAkB,GAAG3C,KAAYpC,IACvC,OAAoBzB,EAAAA,EAAAA,KAAK+D,EAAW,CAClCH,IAAKA,KACFI,EACH9D,UAAW2E,IAAW3E,EAAWsG,EAAiB5D,GAAQ,GAAG4D,KAAmB5D,IAAQjB,GAAW,QAAQA,SAG/GH,EAAQsD,YAAc,UACtB,S", "sources": ["pages/agent/Recommend.js", "../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/AlertHeading.js", "../node_modules/react-bootstrap/esm/AlertLink.js", "../node_modules/react-bootstrap/esm/Alert.js", "../node_modules/react-bootstrap/esm/Spinner.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';\nimport { FaChevronDown, FaChevronRight, FaUser, FaUsers, FaExpandArrowsAlt, FaCompressArrowsAlt, FaSearch } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Recommend = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [referralTree, setReferralTree] = useState([]);\n    const [filteredTree, setFilteredTree] = useState([]);\n    const [expandedNodes, setExpandedNodes] = useState(new Set());\n    const [error, setError] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n\n    useEffect(() => {\n        const fetchReferralData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            setError(null);\n\n            let currentUser = null;\n            let customers = [];\n            let customersError = null;\n\n\n            try {\n                const { data: { user } } = await supabase.auth.getUser();\n                currentUser = user;\n\n                if (!user) {\n                    setError(t('user_not_logged_in'));\n                    setLoading(false);\n                    return;\n                }\n\n                // First, get the agent's maker_id to determine scope\n                const { data: agentProfile, error: agentError } = await supabase\n                    .from('agent_profiles')\n                    .select('maker_id')\n                    .eq('user_id', user.id)\n                    .single();\n\n                if (agentError) {\n                    console.error('Error fetching agent profile:', agentError);\n                    setError(t('agent_profile_not_found'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Step 1: 获取 customer_profiles 列表\n                const { data: customerProfiles, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id')  // 不嵌套 users 表\n                    .eq('agent_id', user.id)\n                    .limit(100);\n\n                if (profileError) {\n                    console.error('Error fetching customer profiles:', profileError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Step 2: 用 user_id 去单独获取 users 表\n                const userIds = customerProfiles.map(p => p.user_id).filter(Boolean);\n\n                const { data: usersData, error: usersError } = await supabase\n                    .from('users')\n                    .select('id, email, referred_by, created_at, role, invite_code')\n                    .in('id', userIds);\n\n                if (usersError) {\n                    console.error('Error fetching user info:', usersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                if (customersError) {\n                    console.error('Error fetching customers:', customersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Extract user data from customers\n                const customerUsers = usersData || [];\n\n                // Get referrer information recursively (up to 3 levels to avoid infinite loops)\n                const fetchReferrers = async (userIds, level = 0, maxLevel = 3) => {\n                    if (level >= maxLevel || userIds.length === 0) {\n                        return [];\n                    }\n\n                    try {\n                        const { data: referrerData, error: referrerError } = await supabase\n                            .from('users')\n                            .select('id, email, referred_by, created_at, role, invite_code')\n                            .in('id', userIds)\n                            .limit(50); // Limit each level to prevent large queries\n\n                        if (referrerError) {\n                            console.error(`Error fetching referrers at level ${level}:`, referrerError);\n                            return [];\n                        }\n\n                        const referrers = referrerData || [];\n\n                        // Get next level referrer IDs\n                        const nextLevelIds = [...new Set(referrers\n                            .map(u => u.referred_by)\n                            .filter(Boolean))];\n\n                        // Recursively fetch next level\n                        const nextLevelReferrers = await fetchReferrers(nextLevelIds, level + 1, maxLevel);\n\n                        return [...referrers, ...nextLevelReferrers];\n                    } catch (error) {\n                        console.error(`Error in fetchReferrers at level ${level}:`, error);\n                        return [];\n                    }\n                };\n\n                // Get unique referred_by IDs from customers\n                const initialReferrerIds = [...new Set(customerUsers\n                    .map(u => u.referred_by)\n                    .filter(Boolean))];\n\n                const referrers = await fetchReferrers(initialReferrerIds);\n\n                // Combine customer users and their referrers\n                const allUsers = [...customerUsers, ...referrers];\n\n                // Remove duplicates based on user ID\n                const uniqueUsers = allUsers.filter((user, index, self) =>\n                    index === self.findIndex(u => u.id === user.id)\n                );\n\n                // Build the referral tree\n                const tree = buildReferralTree(uniqueUsers);\n                setReferralTree(tree);\n                setFilteredTree(tree);\n\n            } catch (err) {\n                console.error('Error:', err);\n\n                // Fallback: try to get just the direct customers without referrer data\n                try {\n                    console.log('Attempting fallback query...');\n\n                    // Use currentUser if available, otherwise try to get user again\n                    const userIdToUse = currentUser?.id;\n                    if (!userIdToUse) {\n                        const { data: { user: fallbackUser } } = await supabase.auth.getUser();\n                        if (!fallbackUser) {\n                            setError(t('user_not_logged_in'));\n                            return;\n                        }\n                        currentUser = fallbackUser;\n                    }\n\n                    const { data: simpleCustomers, error: simpleError } = await supabase\n                        .from('customer_profiles')\n                        .select(`\n                            users (\n                                id,\n                                email,\n                                created_at,\n                                role,\n                                invite_code\n                            )\n                        `)\n                        .eq('agent_id', currentUser.id)\n                        .limit(50); // Further limit for fallback\n\n                    if (!simpleError && simpleCustomers && simpleCustomers.length > 0) {\n                        const simpleUsers = simpleCustomers\n                            .map(c => c.users)\n                            .filter(Boolean)\n                            .map(u => ({\n                                ...u,\n                                referred_by: null,\n                                children: []\n                            })); // Remove referral info for simplicity\n\n                        console.log('Fallback successful, loaded', simpleUsers.length, 'users');\n                        setReferralTree(simpleUsers);\n                        setFilteredTree(simpleUsers);\n                        setError(t('limited_referral_data'));\n                    } else {\n                        console.log('Fallback failed or no data:', simpleError);\n                        setReferralTree([]);\n                        setFilteredTree([]);\n                        setError(t('no_referral_data'));\n                    }\n                } catch (fallbackErr) {\n                    console.error('Fallback error:', fallbackErr);\n                    setReferralTree([]);\n                    setFilteredTree([]);\n                    setError(t('unexpected_error'));\n                }\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchReferralData();\n    }, [t]);\n\n    // Filter tree based on search term\n    useEffect(() => {\n        if (!searchTerm.trim()) {\n            setFilteredTree(referralTree);\n            return;\n        }\n\n        const filterTree = (nodes) => {\n            return nodes.filter(node => {\n                const matchesSearch = node.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                                    node.id.toLowerCase().includes(searchTerm.toLowerCase());\n\n                const filteredChildren = node.children ? filterTree(node.children) : [];\n\n                return matchesSearch || filteredChildren.length > 0;\n            }).map(node => ({\n                ...node,\n                children: node.children ? filterTree(node.children) : []\n            }));\n        };\n\n        setFilteredTree(filterTree(referralTree));\n    }, [searchTerm, referralTree]);\n\n    const buildReferralTree = (users) => {\n        if (!users || users.length === 0) {\n            return [];\n        }\n\n        const userMap = new Map();\n        const rootUsers = [];\n\n        // Create a map of all users\n        users.forEach(user => {\n            userMap.set(user.id, {\n                ...user,\n                children: []\n            });\n        });\n\n        // Build the tree structure\n        users.forEach(user => {\n            if (user.referred_by) {\n                const parent = userMap.get(user.referred_by);\n                if (parent) {\n                    parent.children.push(userMap.get(user.id));\n                } else {\n                    // Parent not found, treat as root\n                    rootUsers.push(userMap.get(user.id));\n                }\n            } else {\n                // No referrer, this is a root user\n                rootUsers.push(userMap.get(user.id));\n            }\n        });\n\n        return rootUsers;\n    };\n\n    const toggleNode = (userId) => {\n        const newExpanded = new Set(expandedNodes);\n        if (newExpanded.has(userId)) {\n            newExpanded.delete(userId);\n        } else {\n            newExpanded.add(userId);\n        }\n        setExpandedNodes(newExpanded);\n    };\n\n    const expandAll = () => {\n        const allNodeIds = new Set();\n        const collectNodeIds = (nodes) => {\n            nodes.forEach(node => {\n                if (node.children && node.children.length > 0) {\n                    allNodeIds.add(node.id);\n                    collectNodeIds(node.children);\n                }\n            });\n        };\n        collectNodeIds(filteredTree);\n        setExpandedNodes(allNodeIds);\n    };\n\n    const collapseAll = () => {\n        setExpandedNodes(new Set());\n    };\n\n    const formatUserId = (id) => {\n        return id.substring(0, 8);\n    };\n\n    const getRoleIcon = (role) => {\n        switch (role) {\n            case 'maker':\n                return <FaUsers className=\"text-primary me-1\" />;\n            case 'agent':\n                return <FaUser className=\"text-success me-1\" />;\n            case 'customer':\n                return <FaUser className=\"text-info me-1\" />;\n            default:\n                return <FaUser className=\"text-secondary me-1\" />;\n        }\n    };\n\n    const renderTreeNode = (node, level = 0) => {\n        const hasChildren = node.children && node.children.length > 0;\n        const isExpanded = expandedNodes.has(node.id);\n        const paddingLeft = level * 20;\n\n        return (\n            <div key={node.id} className=\"mb-1\">\n                <div \n                    className=\"d-flex align-items-center p-2 border-bottom\"\n                    style={{ paddingLeft: `${paddingLeft}px`, cursor: hasChildren ? 'pointer' : 'default' }}\n                    onClick={() => hasChildren && toggleNode(node.id)}\n                >\n                    {hasChildren ? (\n                        isExpanded ? (\n                            <FaChevronDown className=\"me-2 text-muted\" />\n                        ) : (\n                            <FaChevronRight className=\"me-2 text-muted\" />\n                        )\n                    ) : (\n                        <span className=\"me-4\"></span>\n                    )}\n                    \n                    {getRoleIcon(node.role)}\n                    \n                    <span className=\"me-2\">\n                        {node.email}\n                    </span>\n                    \n                    <span className=\"text-muted small\">\n                        {node.invite_code}\n                    </span>\n                    \n                    {hasChildren && (\n                        <span className=\"ms-auto badge bg-secondary\">\n                            {node.children.length}\n                        </span>\n                    )}\n                </div>\n                \n                {hasChildren && isExpanded && (\n                    <div>\n                        {node.children.map(child => renderTreeNode(child, level + 1))}\n                    </div>\n                )}\n            </div>\n        );\n    };\n\n    const getTotalUsers = (nodes) => {\n        let total = 0;\n        const countNodes = (nodeList) => {\n            nodeList.forEach(node => {\n                total++;\n                if (node.children && node.children.length > 0) {\n                    countNodes(node.children);\n                }\n            });\n        };\n        countNodes(nodes);\n        return total;\n    };\n\n    const getRootUsersCount = () => {\n        return filteredTree.length;\n    };\n\n    if (loading) {\n        return (\n            <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n                <div className=\"text-center\">\n                    <Spinner animation=\"border\" role=\"status\" className=\"mb-3\" />\n                    <div>{t('loading_referral_data')}</div>\n                </div>\n            </Container>\n        );\n    }\n\n    if (error) {\n        return (\n            <Container>\n                <Alert variant=\"danger\">\n                    {error}\n                </Alert>\n            </Container>\n        );\n    }\n\n    return (\n        <Container>\n            <Row className=\"mb-4\">\n                <Col>\n                    <h2>{t('referral_relationships')}</h2>\n                    <p className=\"text-muted\">{t('referral_tree_description')}</p>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={4}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_users')}</Form.Label>\n                                        <Form.Control\n                                            type=\"text\"\n                                            placeholder={t('search_by_email_or_id')}\n                                            value={searchTerm}\n                                            onChange={(e) => setSearchTerm(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={4}>\n                                    <div className=\"d-flex gap-2\">\n                                        <Button\n                                            variant=\"outline-primary\"\n                                            size=\"sm\"\n                                            onClick={expandAll}\n                                        >\n                                            <FaExpandArrowsAlt className=\"me-1\" />\n                                            {t('expand_all')}\n                                        </Button>\n                                        <Button\n                                            variant=\"outline-secondary\"\n                                            size=\"sm\"\n                                            onClick={collapseAll}\n                                        >\n                                            <FaCompressArrowsAlt className=\"me-1\" />\n                                            {t('collapse_all')}\n                                        </Button>\n                                    </div>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white\">\n                        <Card.Body>\n                            <h5>{t('total_users')}</h5>\n                            <h3>{getTotalUsers(filteredTree)}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white\">\n                        <Card.Body>\n                            <h5>{t('root_users')}</h5>\n                            <h3>{getRootUsersCount()}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white\">\n                        <Card.Body>\n                            <h5>{t('expanded_nodes')}</h5>\n                            <h3>{expandedNodes.size}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <h5 className=\"mb-0\">{t('referral_tree')}</h5>\n                            <small className=\"text-muted\">\n                                {t('click_to_expand_collapse')}\n                            </small>\n                        </Card.Header>\n                        <Card.Body style={{ maxHeight: '600px', overflowY: 'auto' }}>\n                            {filteredTree.length === 0 ? (\n                                <div className=\"text-center text-muted py-4\">\n                                    {searchTerm ? t('no_search_results') : t('no_referral_data')}\n                                </div>\n                            ) : (\n                                <div>\n                                    {filteredTree.map(node => renderTreeNode(node))}\n                                </div>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Recommend;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Spinner = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  variant,\n  animation = 'border',\n  size,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  className,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  const bsSpinnerPrefix = `${bsPrefix}-${animation}`;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsSpinnerPrefix, size && `${bsSpinnerPrefix}-${size}`, variant && `text-${variant}`)\n  });\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;"], "names": ["Recommend", "t", "useTranslation", "loading", "setLoading", "useState", "referralTree", "setReferralTree", "filteredTree", "setFilteredTree", "expandedNodes", "setExpandedNodes", "Set", "error", "setError", "searchTerm", "setSearchTerm", "useEffect", "async", "supabase", "getSupabase", "currentUser", "data", "user", "auth", "getUser", "agentProfile", "agent<PERSON><PERSON>r", "from", "select", "eq", "id", "single", "console", "customerProfiles", "profileError", "limit", "userIds", "map", "p", "user_id", "filter", "Boolean", "usersData", "usersError", "in", "customerUsers", "fetchReferrers", "level", "arguments", "length", "undefined", "maxLevel", "referrerData", "referrerError", "referrers", "nextLevelIds", "u", "referred_by", "nextLevelReferrers", "initialReferrerIds", "uniqueUsers", "index", "self", "findIndex", "tree", "buildReferralTree", "err", "_currentUser", "log", "fallbackUser", "simpleCustomers", "simpleError", "simpleUsers", "c", "users", "children", "fallbackErr", "fetchReferralData", "trim", "filterTree", "nodes", "node", "matchesSearch", "email", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userMap", "Map", "rootUsers", "for<PERSON>ach", "set", "parent", "get", "push", "getRoleIcon", "role", "_jsx", "FaUsers", "className", "FaUser", "renderTreeNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpanded", "has", "paddingLeft", "_jsxs", "style", "cursor", "onClick", "userId", "newExpanded", "delete", "add", "toggleNode", "FaChevronDown", "FaChevronRight", "invite_code", "child", "Container", "minHeight", "Spinner", "animation", "<PERSON><PERSON>", "variant", "Row", "Col", "Card", "Body", "md", "Form", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "<PERSON><PERSON>", "size", "expandAll", "allNodeIds", "collectNodeIds", "FaExpandArrowsAlt", "collapseAll", "FaCompressArrowsAlt", "total", "countNodes", "nodeList", "getTotalUsers", "Header", "maxHeight", "overflowY", "React", "_ref", "ref", "bsPrefix", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "brkPoint", "propValue", "cols", "infix", "classNames", "displayName", "DivStyledAsH4", "divWithClassName", "AlertHeading", "AlertLink", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "Transition", "alert", "CloseButton", "unmountOnExit", "Object", "assign", "Link", "Heading", "bsSpinnerPrefix"], "sourceRoot": ""}