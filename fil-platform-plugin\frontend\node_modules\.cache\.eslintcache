[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js": "20", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js": "21", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js": "22", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js": "23", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js": "24", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js": "25", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js": "26", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js": "27", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js": "28", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js": "29", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js": "30", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Recommend.js": "31", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\NetworkStats.js": "32", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\CapacityRequest.js": "33", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Members.js": "34", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\WithdrawList.js": "35", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\OrderReports.js": "36", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\WalletFlow.js": "37", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Filfox.js": "38"}, {"size": 408, "mtime": 1751951658003, "results": "39", "hashOfConfig": "40"}, {"size": 18283, "mtime": 1752226717547, "results": "41", "hashOfConfig": "40"}, {"size": 54439, "mtime": 1752227253350, "results": "42", "hashOfConfig": "40"}, {"size": 1212, "mtime": 1751873051207, "results": "43", "hashOfConfig": "40"}, {"size": 3250, "mtime": 1751953679420, "results": "44", "hashOfConfig": "40"}, {"size": 4791, "mtime": 1751960448046, "results": "45", "hashOfConfig": "40"}, {"size": 3994, "mtime": 1752113564124, "results": "46", "hashOfConfig": "40"}, {"size": 4610, "mtime": 1751946228349, "results": "47", "hashOfConfig": "40"}, {"size": 5273, "mtime": 1751960463052, "results": "48", "hashOfConfig": "40"}, {"size": 8598, "mtime": 1751939997191, "results": "49", "hashOfConfig": "40"}, {"size": 4230, "mtime": 1751940026705, "results": "50", "hashOfConfig": "40"}, {"size": 6280, "mtime": 1752224341562, "results": "51", "hashOfConfig": "40"}, {"size": 4495, "mtime": 1751940037703, "results": "52", "hashOfConfig": "40"}, {"size": 3655, "mtime": 1751948557098, "results": "53", "hashOfConfig": "40"}, {"size": 11599, "mtime": 1752224430085, "results": "54", "hashOfConfig": "40"}, {"size": 9624, "mtime": 1752211816825, "results": "55", "hashOfConfig": "40"}, {"size": 3916, "mtime": 1752205047584, "results": "56", "hashOfConfig": "40"}, {"size": 13565, "mtime": 1752120431667, "results": "57", "hashOfConfig": "40"}, {"size": 10902, "mtime": 1752120494989, "results": "58", "hashOfConfig": "40"}, {"size": 4650, "mtime": 1752111918233, "results": "59", "hashOfConfig": "40"}, {"size": 5979, "mtime": 1752111934504, "results": "60", "hashOfConfig": "40"}, {"size": 4355, "mtime": 1752111904775, "results": "61", "hashOfConfig": "40"}, {"size": 6983, "mtime": 1752115841378, "results": "62", "hashOfConfig": "40"}, {"size": 7638, "mtime": 1752115874068, "results": "63", "hashOfConfig": "40"}, {"size": 4798, "mtime": 1752121974201, "results": "64", "hashOfConfig": "40"}, {"size": 3043, "mtime": 1752220020332, "results": "65", "hashOfConfig": "40"}, {"size": 6196, "mtime": 1752114298217, "results": "66", "hashOfConfig": "40"}, {"size": 7110, "mtime": 1752120864192, "results": "67", "hashOfConfig": "40"}, {"size": 7223, "mtime": 1752119937411, "results": "68", "hashOfConfig": "40"}, {"size": 7467, "mtime": 1752122455950, "results": "69", "hashOfConfig": "40"}, {"size": 19472, "mtime": 1752213477302, "results": "70", "hashOfConfig": "40"}, {"size": 3043, "mtime": 1752220085745, "results": "71", "hashOfConfig": "40"}, {"size": 7923, "mtime": 1752196766555, "results": "72", "hashOfConfig": "40"}, {"size": 15593, "mtime": 1752213771897, "results": "73", "hashOfConfig": "40"}, {"size": 12028, "mtime": 1752209647792, "results": "74", "hashOfConfig": "40"}, {"size": 7157, "mtime": 1752211590271, "results": "75", "hashOfConfig": "40"}, {"size": 8839, "mtime": 1752209016116, "results": "76", "hashOfConfig": "40"}, {"size": 17286, "mtime": 1752227695849, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ji7irk", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["192", "193", "194", "195", "196", "197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["213"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", ["214"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", ["215"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", ["216"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", ["217"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js", ["218"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js", ["219"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js", ["220"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js", ["221"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js", ["222"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js", ["223", "224"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Recommend.js", ["225", "226", "227", "228"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\NetworkStats.js", ["229"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Members.js", ["230", "231"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\WithdrawList.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\WalletFlow.js", ["232"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Filfox.js", ["233"], [], {"ruleId": "234", "severity": 1, "message": "235", "line": 181, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 181, "endColumn": 18}, {"ruleId": "234", "severity": 1, "message": "238", "line": 183, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 183, "endColumn": 23}, {"ruleId": "234", "severity": 1, "message": "239", "line": 300, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 300, "endColumn": 17}, {"ruleId": "234", "severity": 1, "message": "240", "line": 347, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 347, "endColumn": 26}, {"ruleId": "234", "severity": 1, "message": "241", "line": 348, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 348, "endColumn": 27}, {"ruleId": "234", "severity": 1, "message": "242", "line": 363, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 363, "endColumn": 21}, {"ruleId": "234", "severity": 1, "message": "243", "line": 364, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 364, "endColumn": 20}, {"ruleId": "234", "severity": 1, "message": "235", "line": 576, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 576, "endColumn": 18}, {"ruleId": "234", "severity": 1, "message": "238", "line": 578, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 578, "endColumn": 23}, {"ruleId": "234", "severity": 1, "message": "239", "line": 695, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 695, "endColumn": 17}, {"ruleId": "234", "severity": 1, "message": "240", "line": 742, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 742, "endColumn": 26}, {"ruleId": "234", "severity": 1, "message": "241", "line": 743, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 743, "endColumn": 27}, {"ruleId": "234", "severity": 1, "message": "242", "line": 758, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 758, "endColumn": 21}, {"ruleId": "234", "severity": 1, "message": "243", "line": 759, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 759, "endColumn": 20}, {"ruleId": "234", "severity": 1, "message": "235", "line": 971, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 971, "endColumn": 18}, {"ruleId": "234", "severity": 1, "message": "238", "line": 973, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 973, "endColumn": 23}, {"ruleId": "234", "severity": 1, "message": "239", "line": 1090, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 1090, "endColumn": 17}, {"ruleId": "234", "severity": 1, "message": "240", "line": 1137, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 1137, "endColumn": 26}, {"ruleId": "234", "severity": 1, "message": "241", "line": 1138, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 1138, "endColumn": 27}, {"ruleId": "234", "severity": 1, "message": "242", "line": 1153, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 1153, "endColumn": 21}, {"ruleId": "234", "severity": 1, "message": "243", "line": 1154, "column": 7, "nodeType": "236", "messageId": "237", "endLine": 1154, "endColumn": 20}, {"ruleId": "244", "severity": 1, "message": "245", "line": 49, "column": 8, "nodeType": "246", "endLine": 49, "endColumn": 10, "suggestions": "247"}, {"ruleId": "248", "severity": 1, "message": "249", "line": 3, "column": 55, "nodeType": "250", "messageId": "251", "endLine": 3, "endColumn": 61}, {"ruleId": "248", "severity": 1, "message": "252", "line": 4, "column": 56, "nodeType": "250", "messageId": "251", "endLine": 4, "endColumn": 63}, {"ruleId": "248", "severity": 1, "message": "253", "line": 23, "column": 46, "nodeType": "250", "messageId": "251", "endLine": 23, "endColumn": 57}, {"ruleId": "248", "severity": 1, "message": "254", "line": 3, "column": 44, "nodeType": "250", "messageId": "251", "endLine": 3, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "254", "line": 2, "column": 44, "nodeType": "250", "messageId": "251", "endLine": 2, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "254", "line": 2, "column": 44, "nodeType": "250", "messageId": "251", "endLine": 2, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "254", "line": 2, "column": 44, "nodeType": "250", "messageId": "251", "endLine": 2, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "254", "line": 2, "column": 44, "nodeType": "250", "messageId": "251", "endLine": 2, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "254", "line": 2, "column": 44, "nodeType": "250", "messageId": "251", "endLine": 2, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "255", "line": 7, "column": 13, "nodeType": "250", "messageId": "251", "endLine": 7, "endColumn": 14}, {"ruleId": "248", "severity": 1, "message": "256", "line": 90, "column": 21, "nodeType": "250", "messageId": "251", "endLine": 90, "endColumn": 25}, {"ruleId": "248", "severity": 1, "message": "257", "line": 3, "column": 98, "nodeType": "250", "messageId": "251", "endLine": 3, "endColumn": 106}, {"ruleId": "248", "severity": 1, "message": "258", "line": 25, "column": 17, "nodeType": "250", "messageId": "251", "endLine": 25, "endColumn": 26}, {"ruleId": "248", "severity": 1, "message": "259", "line": 40, "column": 31, "nodeType": "250", "messageId": "251", "endLine": 40, "endColumn": 43}, {"ruleId": "248", "severity": 1, "message": "260", "line": 300, "column": 11, "nodeType": "250", "messageId": "251", "endLine": 300, "endColumn": 23}, {"ruleId": "248", "severity": 1, "message": "254", "line": 2, "column": 44, "nodeType": "250", "messageId": "251", "endLine": 2, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "261", "line": 2, "column": 77, "nodeType": "250", "messageId": "251", "endLine": 2, "endColumn": 85}, {"ruleId": "248", "severity": 1, "message": "262", "line": 44, "column": 23, "nodeType": "250", "messageId": "251", "endLine": 44, "endColumn": 30}, {"ruleId": "248", "severity": 1, "message": "259", "line": 26, "column": 31, "nodeType": "250", "messageId": "251", "endLine": 26, "endColumn": 43}, {"ruleId": "244", "severity": 1, "message": "263", "line": 136, "column": 8, "nodeType": "246", "endLine": 136, "endColumn": 10, "suggestions": "264"}, "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "Duplicate key 'batch_id'.", "Duplicate key 'member_management'.", "Duplicate key 'product_management'.", "Duplicate key 'requested_at'.", "Duplicate key 'reviewed_at'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["265"], "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'Tooltip' is defined but never used.", "'memberError' is assigned a value but never used.", "'Badge' is defined but never used.", "'t' is assigned a value but never used.", "'data' is assigned a value but never used.", "'FaSearch' is defined but never used.", "'customers' is assigned a value but never used.", "'agentProfile' is assigned a value but never used.", "'formatUserId' is assigned a value but never used.", "'Dropdown' is defined but never used.", "'userIds' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchNetworkStats'. Either include it or remove the dependency array.", ["266"], {"desc": "267", "fix": "268"}, {"desc": "269", "fix": "270"}, "Update the dependencies array to be: [t]", {"range": "271", "text": "272"}, "Update the dependencies array to be: [fetchNetworkStats]", {"range": "273", "text": "274"}, [1977, 1979], "[t]", [5295, 5297], "[fetchNetworkStats]"]