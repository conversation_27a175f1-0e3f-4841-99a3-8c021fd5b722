{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{Container,Row,Col,Card,Table,Spinner,<PERSON><PERSON>,<PERSON><PERSON>}from'react-bootstrap';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>Axis,<PERSON><PERSON><PERSON><PERSON>,Cartes<PERSON>Grid,<PERSON><PERSON><PERSON>,Legend,ResponsiveContainer}from'recharts';import{getSupabase}from'../../supabaseClient';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const StatCard=_ref=>{let{title,value,unit,variant,loading,icon}=_ref;return/*#__PURE__*/_jsx(Card,{className:`bg-${variant} text-white mb-3 h-100`,children:/*#__PURE__*/_jsx(Card.Body,{className:\"d-flex flex-column justify-content-between\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-start\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Card.Title,{className:\"h6\",children:title}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading...\"})]}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:value}),unit&&/*#__PURE__*/_jsx(\"small\",{className:\"opacity-75\",children:unit})]})]}),icon&&/*#__PURE__*/_jsx(\"div\",{className:\"fs-2 opacity-50\",children:icon})]})})});};const Filfox=()=>{const{t}=useTranslation();const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[currentStats,setCurrentStats]=useState(null);const[historicalData,setHistoricalData]=useState([]);const[refreshing,setRefreshing]=useState(false);// Fetch real-time network stats from WordPress API\nconst fetchNetworkStats=async()=>{try{setLoading(true);// Get WordPress site URL from window object or construct it\nconst wpApiUrl=window.location.origin+'/wp-json/fil-platform/v1/filfox-realtime';const response=await fetch(wpApiUrl,{method:'GET',credentials:'include',// Include cookies for authentication\nheaders:{'Content-Type':'application/json'}});if(!response.ok){throw new Error(`HTTP error! status: ${response.status}`);}const result=await response.json();if(result.success){setCurrentStats(result.data);setError(null);}else{setError(result.message||'Failed to fetch real-time data');}// For historical data, we'll fetch from Supabase (only fil_per_tib)\nawait fetchHistoricalData();}catch(error){console.error('Error fetching real-time stats:',error);setError('Failed to load real-time network statistics: '+error.message);}finally{setLoading(false);setRefreshing(false);}};// Fetch historical data for charts (only fil_per_tib from database)\nconst fetchHistoricalData=async()=>{const supabase=getSupabase();if(!supabase)return;try{const{data:{user}}=await supabase.auth.getUser();if(!user)return;// Fetch historical data for charts (last 30 days, only fil_per_tib)\nconst{data:historicalData,error:historicalError}=await supabase.from('network_stats').select('stat_date, fil_per_tib').order('stat_date',{ascending:false}).limit(30);if(historicalError){console.error('Error fetching historical stats:',historicalError);}else{// Reverse to show chronological order in charts\nsetHistoricalData(historicalData.reverse());}}catch(error){console.error('Error fetching historical data:',error);}};useEffect(()=>{fetchNetworkStats();},[]);const handleRefresh=()=>{setRefreshing(true);fetchNetworkStats();};const formatNumber=num=>{if(num===null||num===undefined)return'N/A';return new Intl.NumberFormat('en-US',{minimumFractionDigits:0,maximumFractionDigits:4}).format(num);};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};if(error){return/*#__PURE__*/_jsx(Container,{fluid:true,children:/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"h2\",{children:t('filfox_network_stats')}),/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error})]})})});}return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsx(\"h2\",{children:t('filfox_network_stats')}),/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",onClick:handleRefresh,disabled:refreshing,children:refreshing?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),t('refreshing')]}):t('refresh')})]})})}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('block_height'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.block_height),variant:\"primary\",loading:loading,icon:\"\\uD83D\\uDD17\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('network_storage_power'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.network_storage_power),unit:\"EiB\",variant:\"success\",loading:loading,icon:\"\\uD83D\\uDCBE\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('active_miners'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.active_miners),variant:\"info\",loading:loading,icon:\"\\u26CF\\uFE0F\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('block_reward'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.block_reward),unit:\"FIL\",variant:\"warning\",loading:loading,icon:\"\\uD83C\\uDF81\"})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('mining_reward_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.fil_per_tib),unit:\"FIL/TiB\",variant:\"secondary\",loading:loading,icon:\"\\u26A1\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('fil_production_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.fil_production_24h),unit:\"FIL\",variant:\"dark\",loading:loading,icon:\"\\uD83C\\uDFED\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('total_pledge_collateral'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.total_pledge_collateral),unit:\"FIL\",variant:\"danger\",loading:loading,icon:\"\\uD83D\\uDD12\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('messages_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.messages_24h),variant:\"light\",loading:loading,icon:\"\\uD83D\\uDCE8\"})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(StatCard,{title:t('sector_initial_pledge'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.sector_initial_pledge),unit:\"FIL/32GiB\",variant:\"primary\",loading:loading,icon:\"\\uD83D\\uDD10\"})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(StatCard,{title:t('latest_block'),value:(currentStats===null||currentStats===void 0?void 0:currentStats.latest_block)||'N/A',variant:\"info\",loading:loading,icon:\"\\u23F0\"})})]}),historicalData.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('mining_reward_trend')}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:300,children:/*#__PURE__*/_jsxs(LineChart,{data:historicalData,children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"stat_date\",tickFormatter:formatDate}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{labelFormatter:formatDate,formatter:value=>[formatNumber(value),'FIL/TiB']}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"fil_per_tib\",stroke:\"#8884d8\",name:t('mining_reward')})]})})]})})})}),/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('historical_note')}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted\",children:t('historical_note_description')})]})})})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('current_network_summary')}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:t('loading')})]}):currentStats?/*#__PURE__*/_jsx(Table,{striped:true,bordered:true,hover:true,responsive:true,children:/*#__PURE__*/_jsxs(\"tbody\",{children:[/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('block_height')})}),/*#__PURE__*/_jsx(\"td\",{children:formatNumber(currentStats.block_height)}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('network_storage_power')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.network_storage_power),\" EiB\"]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('active_miners')})}),/*#__PURE__*/_jsx(\"td\",{children:formatNumber(currentStats.active_miners)}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('block_reward')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.block_reward),\" FIL\"]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('mining_reward_24h')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.mining_reward),\" FIL/TiB\"]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('fil_production_24h')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.fil_production_24h),\" FIL\"]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('total_pledge_collateral')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.total_pledge_collateral),\" FIL\"]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('messages_24h')})}),/*#__PURE__*/_jsx(\"td\",{children:formatNumber(currentStats.messages_24h)})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('sector_initial_pledge')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.sector_initial_pledge),\" FIL/32GiB\"]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('latest_block')})}),/*#__PURE__*/_jsx(\"td\",{children:currentStats.latest_block||'N/A'})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('last_updated')})}),/*#__PURE__*/_jsx(\"td\",{colSpan:\"3\",children:currentStats.scraped_at?new Date(currentStats.scraped_at).toLocaleString():'N/A'})]})]})}):/*#__PURE__*/_jsx(\"p\",{children:t('no_data_available')})]})})})})]});};export default Filfox;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Row", "Col", "Card", "Table", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON>", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "StatCard", "_ref", "title", "value", "unit", "variant", "loading", "icon", "className", "children", "Body", "Title", "animation", "size", "Filfox", "t", "setLoading", "error", "setError", "currentStats", "setCurrentStats", "historicalData", "setHistoricalData", "refreshing", "setRefreshing", "fetchNetworkStats", "wpApiUrl", "window", "location", "origin", "response", "fetch", "method", "credentials", "headers", "ok", "Error", "status", "result", "json", "success", "data", "message", "fetchHistoricalData", "console", "supabase", "user", "auth", "getUser", "historicalError", "from", "select", "order", "ascending", "limit", "reverse", "handleRefresh", "formatNumber", "num", "undefined", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "fluid", "onClick", "disabled", "md", "block_height", "network_storage_power", "active_miners", "block_reward", "fil_per_tib", "fil_production_24h", "total_pledge_collateral", "messages_24h", "sector_initial_pledge", "latest_block", "length", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick<PERSON><PERSON><PERSON><PERSON>", "labelFormatter", "formatter", "type", "stroke", "name", "striped", "bordered", "hover", "responsive", "mining_reward", "colSpan", "scraped_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/Filfox.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, Table, Spinner, Al<PERSON>, Button } from 'react-bootstrap';\nimport { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { getSupabase } from '../../supabaseClient';\n\nconst StatCard = ({ title, value, unit, variant, loading, icon }) => (\n    <Card className={`bg-${variant} text-white mb-3 h-100`}>\n        <Card.Body className=\"d-flex flex-column justify-content-between\">\n            <div className=\"d-flex justify-content-between align-items-start\">\n                <div>\n                    <Card.Title className=\"h6\">{title}</Card.Title>\n                    {loading ? (\n                        <div className=\"d-flex align-items-center\">\n                            <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                            <span>Loading...</span>\n                        </div>\n                    ) : (\n                        <div>\n                            <h4 className=\"mb-0\">{value}</h4>\n                            {unit && <small className=\"opacity-75\">{unit}</small>}\n                        </div>\n                    )}\n                </div>\n                {icon && <div className=\"fs-2 opacity-50\">{icon}</div>}\n            </div>\n        </Card.Body>\n    </Card>\n);\n\nconst Filfox = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [currentStats, setCurrentStats] = useState(null);\n    const [historicalData, setHistoricalData] = useState([]);\n    const [refreshing, setRefreshing] = useState(false);\n\n    // Fetch real-time network stats from WordPress API\n    const fetchNetworkStats = async () => {\n        try {\n            setLoading(true);\n\n            // Get WordPress site URL from window object or construct it\n            const wpApiUrl = window.location.origin + '/wp-json/fil-platform/v1/filfox-realtime';\n\n            const response = await fetch(wpApiUrl, {\n                method: 'GET',\n                credentials: 'include', // Include cookies for authentication\n                headers: {\n                    'Content-Type': 'application/json',\n                }\n            });\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const result = await response.json();\n\n            if (result.success) {\n                setCurrentStats(result.data);\n                setError(null);\n            } else {\n                setError(result.message || 'Failed to fetch real-time data');\n            }\n\n            // For historical data, we'll fetch from Supabase (only fil_per_tib)\n            await fetchHistoricalData();\n\n        } catch (error) {\n            console.error('Error fetching real-time stats:', error);\n            setError('Failed to load real-time network statistics: ' + error.message);\n        } finally {\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n\n    // Fetch historical data for charts (only fil_per_tib from database)\n    const fetchHistoricalData = async () => {\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        try {\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) return;\n\n            // Fetch historical data for charts (last 30 days, only fil_per_tib)\n            const { data: historicalData, error: historicalError } = await supabase\n                .from('network_stats')\n                .select('stat_date, fil_per_tib')\n                .order('stat_date', { ascending: false })\n                .limit(30);\n\n            if (historicalError) {\n                console.error('Error fetching historical stats:', historicalError);\n            } else {\n                // Reverse to show chronological order in charts\n                setHistoricalData(historicalData.reverse());\n            }\n        } catch (error) {\n            console.error('Error fetching historical data:', error);\n        }\n    };\n\n    useEffect(() => {\n        fetchNetworkStats();\n    }, []);\n\n    const handleRefresh = () => {\n        setRefreshing(true);\n        fetchNetworkStats();\n    };\n\n    const formatNumber = (num) => {\n        if (num === null || num === undefined) return 'N/A';\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 4\n        }).format(num);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString();\n    };\n\n    if (error) {\n        return (\n            <Container fluid>\n                <Row className=\"mb-3\">\n                    <Col>\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Alert variant=\"danger\">{error}</Alert>\n                    </Col>\n                </Row>\n            </Container>\n        );\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Button \n                            variant=\"outline-primary\" \n                            onClick={handleRefresh}\n                            disabled={refreshing}\n                        >\n                            {refreshing ? (\n                                <>\n                                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                                    {t('refreshing')}\n                                </>\n                            ) : (\n                                t('refresh')\n                            )}\n                        </Button>\n                    </div>\n                </Col>\n            </Row>\n\n            {/* Current Statistics Cards */}\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_height')}\n                        value={formatNumber(currentStats?.block_height)}\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔗\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('network_storage_power')}\n                        value={formatNumber(currentStats?.network_storage_power)}\n                        unit=\"EiB\"\n                        variant=\"success\"\n                        loading={loading}\n                        icon=\"💾\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('active_miners')}\n                        value={formatNumber(currentStats?.active_miners)}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⛏️\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_reward')}\n                        value={formatNumber(currentStats?.block_reward)}\n                        unit=\"FIL\"\n                        variant=\"warning\"\n                        loading={loading}\n                        icon=\"🎁\"\n                    />\n                </Col>\n            </Row>\n\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('mining_reward_24h')}\n                        value={formatNumber(currentStats?.fil_per_tib)}\n                        unit=\"FIL/TiB\"\n                        variant=\"secondary\"\n                        loading={loading}\n                        icon=\"⚡\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('fil_production_24h')}\n                        value={formatNumber(currentStats?.fil_production_24h)}\n                        unit=\"FIL\"\n                        variant=\"dark\"\n                        loading={loading}\n                        icon=\"🏭\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('total_pledge_collateral')}\n                        value={formatNumber(currentStats?.total_pledge_collateral)}\n                        unit=\"FIL\"\n                        variant=\"danger\"\n                        loading={loading}\n                        icon=\"🔒\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('messages_24h')}\n                        value={formatNumber(currentStats?.messages_24h)}\n                        variant=\"light\"\n                        loading={loading}\n                        icon=\"📨\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Additional Stats */}\n            <Row className=\"mb-4\">\n                <Col md={6}>\n                    <StatCard\n                        title={t('sector_initial_pledge')}\n                        value={formatNumber(currentStats?.sector_initial_pledge)}\n                        unit=\"FIL/32GiB\"\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔐\"\n                    />\n                </Col>\n                <Col md={6}>\n                    <StatCard\n                        title={t('latest_block')}\n                        value={currentStats?.latest_block || 'N/A'}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⏰\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Historical Charts */}\n            {historicalData.length > 0 && (\n                <>\n                    <Row className=\"mb-4\">\n                        <Col>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('mining_reward_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <LineChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), 'FIL/TiB']}\n                                            />\n                                            <Legend />\n                                            <Line \n                                                type=\"monotone\" \n                                                dataKey=\"fil_per_tib\" \n                                                stroke=\"#8884d8\" \n                                                name={t('mining_reward')}\n                                            />\n                                        </LineChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n\n                    <Row className=\"mb-4\">\n                        <Col>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('historical_note')}</Card.Title>\n                                    <p className=\"text-muted\">\n                                        {t('historical_note_description')}\n                                    </p>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n                </>\n            )}\n\n            {/* Current Data Summary */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('current_network_summary')}</Card.Title>\n                            {loading ? (\n                                <div className=\"text-center\">\n                                    <Spinner animation=\"border\" />\n                                    <p className=\"mt-2\">{t('loading')}</p>\n                                </div>\n                            ) : currentStats ? (\n                                <Table striped bordered hover responsive>\n                                    <tbody>\n                                        <tr>\n                                            <td><strong>{t('block_height')}</strong></td>\n                                            <td>{formatNumber(currentStats.block_height)}</td>\n                                            <td><strong>{t('network_storage_power')}</strong></td>\n                                            <td>{formatNumber(currentStats.network_storage_power)} EiB</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('active_miners')}</strong></td>\n                                            <td>{formatNumber(currentStats.active_miners)}</td>\n                                            <td><strong>{t('block_reward')}</strong></td>\n                                            <td>{formatNumber(currentStats.block_reward)} FIL</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('mining_reward_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.mining_reward)} FIL/TiB</td>\n                                            <td><strong>{t('fil_production_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.fil_production_24h)} FIL</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('total_pledge_collateral')}</strong></td>\n                                            <td>{formatNumber(currentStats.total_pledge_collateral)} FIL</td>\n                                            <td><strong>{t('messages_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.messages_24h)}</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('sector_initial_pledge')}</strong></td>\n                                            <td>{formatNumber(currentStats.sector_initial_pledge)} FIL/32GiB</td>\n                                            <td><strong>{t('latest_block')}</strong></td>\n                                            <td>{currentStats.latest_block || 'N/A'}</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('last_updated')}</strong></td>\n                                            <td colSpan=\"3\">{currentStats.scraped_at ? new Date(currentStats.scraped_at).toLocaleString() : 'N/A'}</td>\n                                        </tr>\n                                    </tbody>\n                                </Table>\n                            ) : (\n                                <p>{t('no_data_available')}</p>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Filfox;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,KAAK,CAAEC,MAAM,KAAQ,iBAAiB,CAC1F,OAASC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,aAAa,CAAEC,OAAO,CAAEC,MAAM,CAAEC,mBAAmB,KAAQ,UAAU,CAC7G,OAASC,WAAW,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnD,KAAM,CAAAC,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,IAAI,CAAEC,OAAO,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAAN,IAAA,oBAC5DN,IAAA,CAACf,IAAI,EAAC4B,SAAS,CAAE,MAAMH,OAAO,wBAAyB,CAAAI,QAAA,cACnDd,IAAA,CAACf,IAAI,CAAC8B,IAAI,EAACF,SAAS,CAAC,4CAA4C,CAAAC,QAAA,cAC7DZ,KAAA,QAAKW,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC7DZ,KAAA,QAAAY,QAAA,eACId,IAAA,CAACf,IAAI,CAAC+B,KAAK,EAACH,SAAS,CAAC,IAAI,CAAAC,QAAA,CAAEP,KAAK,CAAa,CAAC,CAC9CI,OAAO,cACJT,KAAA,QAAKW,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACtCd,IAAA,CAACb,OAAO,EAAC8B,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACL,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDb,IAAA,SAAAc,QAAA,CAAM,YAAU,CAAM,CAAC,EACtB,CAAC,cAENZ,KAAA,QAAAY,QAAA,eACId,IAAA,OAAIa,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEN,KAAK,CAAK,CAAC,CAChCC,IAAI,eAAIT,IAAA,UAAOa,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEL,IAAI,CAAQ,CAAC,EACpD,CACR,EACA,CAAC,CACLG,IAAI,eAAIZ,IAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEF,IAAI,CAAM,CAAC,EACrD,CAAC,CACC,CAAC,CACV,CAAC,EACV,CAED,KAAM,CAAAO,MAAM,CAAGA,CAAA,GAAM,CACjB,KAAM,CAAEC,CAAE,CAAC,CAAGvC,cAAc,CAAC,CAAC,CAC9B,KAAM,CAAC8B,OAAO,CAAEU,UAAU,CAAC,CAAG1C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2C,KAAK,CAAEC,QAAQ,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAC6C,YAAY,CAAEC,eAAe,CAAC,CAAG9C,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC+C,cAAc,CAAEC,iBAAiB,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACiD,UAAU,CAAEC,aAAa,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAAAmD,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACAT,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAAU,QAAQ,CAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAG,0CAA0C,CAEpF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACL,QAAQ,CAAE,CACnCM,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,SAAS,CAAE;AACxBC,OAAO,CAAE,CACL,cAAc,CAAE,kBACpB,CACJ,CAAC,CAAC,CAEF,GAAI,CAACJ,QAAQ,CAACK,EAAE,CAAE,CACd,KAAM,IAAI,CAAAC,KAAK,CAAC,uBAAuBN,QAAQ,CAACO,MAAM,EAAE,CAAC,CAC7D,CAEA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAR,QAAQ,CAACS,IAAI,CAAC,CAAC,CAEpC,GAAID,MAAM,CAACE,OAAO,CAAE,CAChBpB,eAAe,CAACkB,MAAM,CAACG,IAAI,CAAC,CAC5BvB,QAAQ,CAAC,IAAI,CAAC,CAClB,CAAC,IAAM,CACHA,QAAQ,CAACoB,MAAM,CAACI,OAAO,EAAI,gCAAgC,CAAC,CAChE,CAEA;AACA,KAAM,CAAAC,mBAAmB,CAAC,CAAC,CAE/B,CAAE,MAAO1B,KAAK,CAAE,CACZ2B,OAAO,CAAC3B,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDC,QAAQ,CAAC,+CAA+C,CAAGD,KAAK,CAACyB,OAAO,CAAC,CAC7E,CAAC,OAAS,CACN1B,UAAU,CAAC,KAAK,CAAC,CACjBQ,aAAa,CAAC,KAAK,CAAC,CACxB,CACJ,CAAC,CAED;AACA,KAAM,CAAAmB,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACpC,KAAM,CAAAE,QAAQ,CAAGpD,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACoD,QAAQ,CAAE,OAEf,GAAI,CACA,KAAM,CAAEJ,IAAI,CAAE,CAAEK,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAD,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CACxD,GAAI,CAACF,IAAI,CAAE,OAEX;AACA,KAAM,CAAEL,IAAI,CAAEpB,cAAc,CAAEJ,KAAK,CAAEgC,eAAgB,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CAClEK,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,wBAAwB,CAAC,CAChCC,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CACxCC,KAAK,CAAC,EAAE,CAAC,CAEd,GAAIL,eAAe,CAAE,CACjBL,OAAO,CAAC3B,KAAK,CAAC,kCAAkC,CAAEgC,eAAe,CAAC,CACtE,CAAC,IAAM,CACH;AACA3B,iBAAiB,CAACD,cAAc,CAACkC,OAAO,CAAC,CAAC,CAAC,CAC/C,CACJ,CAAE,MAAOtC,KAAK,CAAE,CACZ2B,OAAO,CAAC3B,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CAC3D,CACJ,CAAC,CAED1C,SAAS,CAAC,IAAM,CACZkD,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA+B,aAAa,CAAGA,CAAA,GAAM,CACxBhC,aAAa,CAAC,IAAI,CAAC,CACnBC,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAgC,YAAY,CAAIC,GAAG,EAAK,CAC1B,GAAIA,GAAG,GAAK,IAAI,EAAIA,GAAG,GAAKC,SAAS,CAAE,MAAO,KAAK,CACnD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CAClCC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CAC3B,CAAC,CAAC,CAACC,MAAM,CAACN,GAAG,CAAC,CAClB,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CAC/B,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CACpD,CAAC,CAED,GAAInD,KAAK,CAAE,CACP,mBACItB,IAAA,CAAClB,SAAS,EAAC4F,KAAK,MAAA5D,QAAA,cACZd,IAAA,CAACjB,GAAG,EAAC8B,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBZ,KAAA,CAAClB,GAAG,EAAA8B,QAAA,eACAd,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,cACpCpB,IAAA,CAACZ,KAAK,EAACsB,OAAO,CAAC,QAAQ,CAAAI,QAAA,CAAEQ,KAAK,CAAQ,CAAC,EACtC,CAAC,CACL,CAAC,CACC,CAAC,CAEpB,CAEA,mBACIpB,KAAA,CAACpB,SAAS,EAAC4F,KAAK,MAAA5D,QAAA,eACZd,IAAA,CAACjB,GAAG,EAAC8B,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBd,IAAA,CAAChB,GAAG,EAAA8B,QAAA,cACAZ,KAAA,QAAKW,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC9Dd,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,cACpCpB,IAAA,CAACX,MAAM,EACHqB,OAAO,CAAC,iBAAiB,CACzBiE,OAAO,CAAEd,aAAc,CACvBe,QAAQ,CAAEhD,UAAW,CAAAd,QAAA,CAEpBc,UAAU,cACP1B,KAAA,CAAAE,SAAA,EAAAU,QAAA,eACId,IAAA,CAACb,OAAO,EAAC8B,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACL,SAAS,CAAC,MAAM,CAAE,CAAC,CACxDO,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,CAEHA,CAAC,CAAC,SAAS,CACd,CACG,CAAC,EACR,CAAC,CACL,CAAC,CACL,CAAC,cAGNlB,KAAA,CAACnB,GAAG,EAAC8B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBd,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,cAAc,CAAE,CACzBZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsD,YAAY,CAAE,CAChDpE,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,uBAAuB,CAAE,CAClCZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEuD,qBAAqB,CAAE,CACzDtE,IAAI,CAAC,KAAK,CACVC,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,eAAe,CAAE,CAC1BZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEwD,aAAa,CAAE,CACjDtE,OAAO,CAAC,MAAM,CACdC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,cAAc,CAAE,CACzBZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEyD,YAAY,CAAE,CAChDxE,IAAI,CAAC,KAAK,CACVC,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,EACL,CAAC,cAENV,KAAA,CAACnB,GAAG,EAAC8B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBd,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,mBAAmB,CAAE,CAC9BZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE0D,WAAW,CAAE,CAC/CzE,IAAI,CAAC,SAAS,CACdC,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,QAAG,CACX,CAAC,CACD,CAAC,cACNZ,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,oBAAoB,CAAE,CAC/BZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE2D,kBAAkB,CAAE,CACtD1E,IAAI,CAAC,KAAK,CACVC,OAAO,CAAC,MAAM,CACdC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,yBAAyB,CAAE,CACpCZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE4D,uBAAuB,CAAE,CAC3D3E,IAAI,CAAC,KAAK,CACVC,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,cAAc,CAAE,CACzBZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE6D,YAAY,CAAE,CAChD3E,OAAO,CAAC,OAAO,CACfC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,EACL,CAAC,cAGNV,KAAA,CAACnB,GAAG,EAAC8B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBd,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,uBAAuB,CAAE,CAClCZ,KAAK,CAAEsD,YAAY,CAACtC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE8D,qBAAqB,CAAE,CACzD7E,IAAI,CAAC,WAAW,CAChBC,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAChB,GAAG,EAAC6F,EAAE,CAAE,CAAE,CAAA/D,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,cAAc,CAAE,CACzBZ,KAAK,CAAE,CAAAgB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE+D,YAAY,GAAI,KAAM,CAC3C7E,OAAO,CAAC,MAAM,CACdC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,QAAG,CACX,CAAC,CACD,CAAC,EACL,CAAC,CAGLc,cAAc,CAAC8D,MAAM,CAAG,CAAC,eACtBtF,KAAA,CAAAE,SAAA,EAAAU,QAAA,eACId,IAAA,CAACjB,GAAG,EAAC8B,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBd,IAAA,CAAChB,GAAG,EAAA8B,QAAA,cACAd,IAAA,CAACf,IAAI,EAAA6B,QAAA,cACDZ,KAAA,CAACjB,IAAI,CAAC8B,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACf,IAAI,CAAC+B,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,qBAAqB,CAAC,CAAa,CAAC,cACnDpB,IAAA,CAACH,mBAAmB,EAAC4F,KAAK,CAAC,MAAM,CAACC,MAAM,CAAE,GAAI,CAAA5E,QAAA,cAC1CZ,KAAA,CAACZ,SAAS,EAACwD,IAAI,CAAEpB,cAAe,CAAAZ,QAAA,eAC5Bd,IAAA,CAACN,aAAa,EAACiG,eAAe,CAAC,KAAK,CAAE,CAAC,cACvC3F,IAAA,CAACR,KAAK,EACFoG,OAAO,CAAC,WAAW,CACnBC,aAAa,CAAEvB,UAAW,CAC7B,CAAC,cACFtE,IAAA,CAACP,KAAK,GAAE,CAAC,cACTO,IAAA,CAACL,OAAO,EACJmG,cAAc,CAAExB,UAAW,CAC3ByB,SAAS,CAAGvF,KAAK,EAAK,CAACsD,YAAY,CAACtD,KAAK,CAAC,CAAE,SAAS,CAAE,CAC1D,CAAC,cACFR,IAAA,CAACJ,MAAM,GAAE,CAAC,cACVI,IAAA,CAACT,IAAI,EACDyG,IAAI,CAAC,UAAU,CACfJ,OAAO,CAAC,aAAa,CACrBK,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAE9E,CAAC,CAAC,eAAe,CAAE,CAC5B,CAAC,EACK,CAAC,CACK,CAAC,EACf,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAENpB,IAAA,CAACjB,GAAG,EAAC8B,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBd,IAAA,CAAChB,GAAG,EAAA8B,QAAA,cACAd,IAAA,CAACf,IAAI,EAAA6B,QAAA,cACDZ,KAAA,CAACjB,IAAI,CAAC8B,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACf,IAAI,CAAC+B,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,iBAAiB,CAAC,CAAa,CAAC,cAC/CpB,IAAA,MAAGa,SAAS,CAAC,YAAY,CAAAC,QAAA,CACpBM,CAAC,CAAC,6BAA6B,CAAC,CAClC,CAAC,EACG,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACR,CACL,cAGDpB,IAAA,CAACjB,GAAG,EAAA+B,QAAA,cACAd,IAAA,CAAChB,GAAG,EAAA8B,QAAA,cACAd,IAAA,CAACf,IAAI,EAAA6B,QAAA,cACDZ,KAAA,CAACjB,IAAI,CAAC8B,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACf,IAAI,CAAC+B,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,yBAAyB,CAAC,CAAa,CAAC,CACtDT,OAAO,cACJT,KAAA,QAAKW,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBd,IAAA,CAACb,OAAO,EAAC8B,SAAS,CAAC,QAAQ,CAAE,CAAC,cAC9BjB,IAAA,MAAGa,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEM,CAAC,CAAC,SAAS,CAAC,CAAI,CAAC,EACrC,CAAC,CACNI,YAAY,cACZxB,IAAA,CAACd,KAAK,EAACiH,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAxF,QAAA,cACpCZ,KAAA,UAAAY,QAAA,eACIZ,KAAA,OAAAY,QAAA,eACId,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CpB,IAAA,OAAAc,QAAA,CAAKgD,YAAY,CAACtC,YAAY,CAACsD,YAAY,CAAC,CAAK,CAAC,cAClD9E,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,uBAAuB,CAAC,CAAS,CAAC,CAAI,CAAC,cACtDlB,KAAA,OAAAY,QAAA,EAAKgD,YAAY,CAACtC,YAAY,CAACuD,qBAAqB,CAAC,CAAC,MAAI,EAAI,CAAC,EAC/D,CAAC,cACL7E,KAAA,OAAAY,QAAA,eACId,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,CAAI,CAAC,cAC9CpB,IAAA,OAAAc,QAAA,CAAKgD,YAAY,CAACtC,YAAY,CAACwD,aAAa,CAAC,CAAK,CAAC,cACnDhF,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7ClB,KAAA,OAAAY,QAAA,EAAKgD,YAAY,CAACtC,YAAY,CAACyD,YAAY,CAAC,CAAC,MAAI,EAAI,CAAC,EACtD,CAAC,cACL/E,KAAA,OAAAY,QAAA,eACId,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,mBAAmB,CAAC,CAAS,CAAC,CAAI,CAAC,cAClDlB,KAAA,OAAAY,QAAA,EAAKgD,YAAY,CAACtC,YAAY,CAAC+E,aAAa,CAAC,CAAC,UAAQ,EAAI,CAAC,cAC3DvG,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,oBAAoB,CAAC,CAAS,CAAC,CAAI,CAAC,cACnDlB,KAAA,OAAAY,QAAA,EAAKgD,YAAY,CAACtC,YAAY,CAAC2D,kBAAkB,CAAC,CAAC,MAAI,EAAI,CAAC,EAC5D,CAAC,cACLjF,KAAA,OAAAY,QAAA,eACId,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,yBAAyB,CAAC,CAAS,CAAC,CAAI,CAAC,cACxDlB,KAAA,OAAAY,QAAA,EAAKgD,YAAY,CAACtC,YAAY,CAAC4D,uBAAuB,CAAC,CAAC,MAAI,EAAI,CAAC,cACjEpF,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CpB,IAAA,OAAAc,QAAA,CAAKgD,YAAY,CAACtC,YAAY,CAAC6D,YAAY,CAAC,CAAK,CAAC,EAClD,CAAC,cACLnF,KAAA,OAAAY,QAAA,eACId,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,uBAAuB,CAAC,CAAS,CAAC,CAAI,CAAC,cACtDlB,KAAA,OAAAY,QAAA,EAAKgD,YAAY,CAACtC,YAAY,CAAC8D,qBAAqB,CAAC,CAAC,YAAU,EAAI,CAAC,cACrEtF,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CpB,IAAA,OAAAc,QAAA,CAAKU,YAAY,CAAC+D,YAAY,EAAI,KAAK,CAAK,CAAC,EAC7C,CAAC,cACLrF,KAAA,OAAAY,QAAA,eACId,IAAA,OAAAc,QAAA,cAAId,IAAA,WAAAc,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CpB,IAAA,OAAIwG,OAAO,CAAC,GAAG,CAAA1F,QAAA,CAAEU,YAAY,CAACiF,UAAU,CAAG,GAAI,CAAAjC,IAAI,CAAChD,YAAY,CAACiF,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,EAC3G,CAAC,EACF,CAAC,CACL,CAAC,cAER1G,IAAA,MAAAc,QAAA,CAAIM,CAAC,CAAC,mBAAmB,CAAC,CAAI,CACjC,EACM,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}