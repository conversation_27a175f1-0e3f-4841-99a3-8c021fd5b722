{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{Container,Row,Col,Card,But<PERSON>,Spinner}from'react-bootstrap';import{LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend,ResponsiveContainer}from'recharts';import{useNavigate}from'react-router-dom';import{getSupabase}from'../../supabaseClient';// Helper function to process dashboard data\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const processDashboardData=(assets,earnings,orders)=>{// Calculate available balance (FIL currency)\nconst filAsset=(assets===null||assets===void 0?void 0:assets.find(asset=>asset.currency_code==='FIL'))||{};const availableBalance=filAsset.balance_available||0;// Calculate total earnings\nconst totalEarnings=(earnings===null||earnings===void 0?void 0:earnings.reduce((sum,earning)=>sum+(earning.reward_amount||0),0))||0;// Calculate yesterday's earnings\nconst yesterday=new Date();yesterday.setDate(yesterday.getDate()-1);const yesterdayStart=new Date(yesterday.getFullYear(),yesterday.getMonth(),yesterday.getDate());const yesterdayEnd=new Date(yesterdayStart);yesterdayEnd.setDate(yesterdayEnd.getDate()+1);const yesterdayEarnings=(earnings===null||earnings===void 0?void 0:earnings.filter(earning=>{const earningDate=new Date(earning.created_at);return earningDate>=yesterdayStart&&earningDate<yesterdayEnd;}).reduce((sum,earning)=>sum+(earning.reward_amount||0),0))||0;// Calculate power pledge (sum of active orders' pledge costs)\nconst now=new Date();const powerPledge=(orders===null||orders===void 0?void 0:orders.filter(order=>{const startDate=new Date(order.start_at);const endDate=new Date(order.end_at);return startDate<=now&&endDate>=now;}).reduce((sum,order)=>sum+(order.pledge_cost||0),0))||0;// Generate chart data for the last 7 days\nconst chartData=[];for(let i=6;i>=0;i--){const date=new Date();date.setDate(date.getDate()-i);const dateStart=new Date(date.getFullYear(),date.getMonth(),date.getDate());const dateEnd=new Date(dateStart);dateEnd.setDate(dateEnd.getDate()+1);const dayEarnings=(earnings===null||earnings===void 0?void 0:earnings.filter(earning=>{const earningDate=new Date(earning.created_at);return earningDate>=dateStart&&earningDate<dateEnd;}).reduce((sum,earning)=>sum+(earning.reward_amount||0),0))||0;chartData.push({name:`${date.getMonth()+1}/${date.getDate()}`,fil:dayEarnings,usd:dayEarnings*4.05// Approximate FIL to USD conversion\n});}return{stats:{totalEarnings,yesterdayEarnings,availableBalance,powerPledge},chartData};};const StatCard=_ref=>{let{title,value,subValue,variant,loading}=_ref;return/*#__PURE__*/_jsx(Card,{className:`bg-${variant} text-white mb-3`,children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:title}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading...\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h3\",{children:value}),subValue&&/*#__PURE__*/_jsx(\"p\",{children:subValue})]})]})});};const CustomerDashboard=()=>{const{t}=useTranslation();const navigate=useNavigate();// State management\nconst[loading,setLoading]=useState(true);const[dashboardData,setDashboardData]=useState({totalEarnings:0,yesterdayEarnings:0,availableBalance:0,powerPledge:0});const[chartData,setChartData]=useState([]);const[error,setError]=useState(null);// Fetch dashboard data from Supabase\nuseEffect(()=>{const fetchDashboardData=async()=>{const supabase=getSupabase();if(!supabase){setError('Failed to initialize Supabase client');setLoading(false);return;}try{setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setError('User not logged in');setLoading(false);return;}// Fetch user assets (available balance)\nconst{data:assets,error:assetsError}=await supabase.from('user_assets').select('currency_code, balance_available, balance_total').eq('user_id',user.id);if(assetsError){console.error('Error fetching assets:',assetsError);}// Fetch earnings data\nconst{data:earnings,error:earningsError}=await supabase.from('order_distributions').select('reward_amount, created_at').eq('customer_id',user.id).order('created_at',{ascending:false});if(earningsError){console.error('Error fetching earnings:',earningsError);}// Fetch orders for power pledge calculation\nconst{data:orders,error:ordersError}=await supabase.from('orders').select('pledge_cost, shares, start_at, end_at').eq('customer_id',user.id).eq('review_status','approved');if(ordersError){console.error('Error fetching orders:',ordersError);}// Process the data\nconst processedData=processDashboardData(assets,earnings,orders);setDashboardData(processedData.stats);setChartData(processedData.chartData);}catch(error){console.error('Error fetching dashboard data:',error);setError('Failed to load dashboard data');}finally{setLoading(false);}};fetchDashboardData();},[]);const handleNavigation=path=>{console.log('Navigating to:',path);console.log('Current URL:',window.location.href);navigate(path);};// Format number for display\nconst formatNumber=num=>{return new Intl.NumberFormat('en-US',{minimumFractionDigits:2,maximumFractionDigits:2}).format(num||0);};if(error){return/*#__PURE__*/_jsx(Container,{fluid:true,children:/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"h2\",{children:t('dashboard')}),/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-danger\",children:error})]})})});}return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(\"h2\",{children:t('dashboard')})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('total_earnings'),value:`${formatNumber(dashboardData.totalEarnings)} FIL`,variant:\"primary\",loading:loading})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('yesterday_earnings'),value:`${formatNumber(dashboardData.yesterdayEarnings)} FIL`,variant:\"success\",loading:loading})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('available_balance'),value:`${formatNumber(dashboardData.availableBalance)} FIL`,variant:\"info\",loading:loading})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('power_pledge'),value:`${formatNumber(dashboardData.powerPledge)} FIL`,variant:\"warning\",loading:loading})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('earnings_trend')}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-center align-items-center\",style:{height:'400px'},children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ms-2\",children:t('loading')})]}):/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:400,children:/*#__PURE__*/_jsxs(LineChart,{data:chartData,children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"name\"}),/*#__PURE__*/_jsx(YAxis,{yAxisId:\"left\",label:{value:'FIL',angle:-90,position:'insideLeft'}}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Line,{yAxisId:\"left\",type:\"monotone\",dataKey:\"fil\",stroke:\"#8884d8\",name:t('FIL_earnings')}),/*#__PURE__*/_jsx(Line,{yAxisId:\"right\",type:\"monotone\",dataKey:\"usd\",stroke:\"#82ca9d\",name:t('USD_estimate')})]})})]})})})}),/*#__PURE__*/_jsxs(Row,{className:\"mt-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('wallet_management')}),/*#__PURE__*/_jsx(\"p\",{children:t('manage_your_digital_assets')}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:()=>handleNavigation('/my'),children:t('enter_wallet')})]})})}),/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('buy_power')}),/*#__PURE__*/_jsx(\"p\",{children:t('view_and_purchase_new_power_products')}),/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:()=>handleNavigation('/products'),children:t('browse_products')})]})})})]})]});};export default CustomerDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Spinner", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "useNavigate", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "processDashboardData", "assets", "earnings", "orders", "filAsset", "find", "asset", "currency_code", "availableBalance", "balance_available", "totalEarnings", "reduce", "sum", "earning", "reward_amount", "yesterday", "Date", "setDate", "getDate", "yesterdayStart", "getFullYear", "getMonth", "yesterdayEnd", "yesterdayEarnings", "filter", "earningDate", "created_at", "now", "powerPledge", "order", "startDate", "start_at", "endDate", "end_at", "pledge_cost", "chartData", "i", "date", "dateStart", "dateEnd", "dayEarnings", "push", "name", "fil", "usd", "stats", "StatCard", "_ref", "title", "value", "subValue", "variant", "loading", "className", "children", "Body", "Title", "animation", "size", "CustomerDashboard", "t", "navigate", "setLoading", "dashboardData", "setDashboardData", "setChartData", "error", "setError", "fetchDashboardData", "supabase", "data", "user", "auth", "getUser", "assetsError", "from", "select", "eq", "id", "console", "earningsError", "ascending", "ordersError", "processedData", "handleNavigation", "path", "log", "window", "location", "href", "formatNumber", "num", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "fluid", "md", "style", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "yAxisId", "label", "angle", "position", "type", "stroke", "onClick"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, But<PERSON>, Spinner } from 'react-bootstrap';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { useNavigate } from 'react-router-dom';\nimport { getSupabase } from '../../supabaseClient';\n\n// Helper function to process dashboard data\nconst processDashboardData = (assets, earnings, orders) => {\n    // Calculate available balance (FIL currency)\n    const filAsset = assets?.find(asset => asset.currency_code === 'FIL') || {};\n    const availableBalance = filAsset.balance_available || 0;\n\n    // Calculate total earnings\n    const totalEarnings = earnings?.reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;\n\n    // Calculate yesterday's earnings\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    const yesterdayStart = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());\n    const yesterdayEnd = new Date(yesterdayStart);\n    yesterdayEnd.setDate(yesterdayEnd.getDate() + 1);\n\n    const yesterdayEarnings = earnings?.filter(earning => {\n        const earningDate = new Date(earning.created_at);\n        return earningDate >= yesterdayStart && earningDate < yesterdayEnd;\n    }).reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;\n\n    // Calculate power pledge (sum of active orders' pledge costs)\n    const now = new Date();\n    const powerPledge = orders?.filter(order => {\n        const startDate = new Date(order.start_at);\n        const endDate = new Date(order.end_at);\n        return startDate <= now && endDate >= now;\n    }).reduce((sum, order) => sum + (order.pledge_cost || 0), 0) || 0;\n\n    // Generate chart data for the last 7 days\n    const chartData = [];\n    for (let i = 6; i >= 0; i--) {\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n        const dateStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n        const dateEnd = new Date(dateStart);\n        dateEnd.setDate(dateEnd.getDate() + 1);\n\n        const dayEarnings = earnings?.filter(earning => {\n            const earningDate = new Date(earning.created_at);\n            return earningDate >= dateStart && earningDate < dateEnd;\n        }).reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;\n\n        chartData.push({\n            name: `${date.getMonth() + 1}/${date.getDate()}`,\n            fil: dayEarnings,\n            usd: dayEarnings * 4.05 // Approximate FIL to USD conversion\n        });\n    }\n\n    return {\n        stats: {\n            totalEarnings,\n            yesterdayEarnings,\n            availableBalance,\n            powerPledge\n        },\n        chartData\n    };\n};\n\nconst StatCard = ({ title, value, subValue, variant, loading }) => (\n    <Card className={`bg-${variant} text-white mb-3`}>\n        <Card.Body>\n            <Card.Title>{title}</Card.Title>\n            {loading ? (\n                <div className=\"d-flex align-items-center\">\n                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                    <span>Loading...</span>\n                </div>\n            ) : (\n                <>\n                    <h3>{value}</h3>\n                    {subValue && <p>{subValue}</p>}\n                </>\n            )}\n        </Card.Body>\n    </Card>\n);\n\nconst CustomerDashboard = () => {\n    const { t } = useTranslation();\n    const navigate = useNavigate();\n\n    // State management\n    const [loading, setLoading] = useState(true);\n    const [dashboardData, setDashboardData] = useState({\n        totalEarnings: 0,\n        yesterdayEarnings: 0,\n        availableBalance: 0,\n        powerPledge: 0\n    });\n    const [chartData, setChartData] = useState([]);\n    const [error, setError] = useState(null);\n\n    // Fetch dashboard data from Supabase\n    useEffect(() => {\n        const fetchDashboardData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) {\n                setError('Failed to initialize Supabase client');\n                setLoading(false);\n                return;\n            }\n\n            try {\n                setLoading(true);\n                const { data: { user } } = await supabase.auth.getUser();\n\n                if (!user) {\n                    setError('User not logged in');\n                    setLoading(false);\n                    return;\n                }\n\n                // Fetch user assets (available balance)\n                const { data: assets, error: assetsError } = await supabase\n                    .from('user_assets')\n                    .select('currency_code, balance_available, balance_total')\n                    .eq('user_id', user.id);\n\n                if (assetsError) {\n                    console.error('Error fetching assets:', assetsError);\n                }\n\n                // Fetch earnings data\n                const { data: earnings, error: earningsError } = await supabase\n                    .from('order_distributions')\n                    .select('reward_amount, created_at')\n                    .eq('customer_id', user.id)\n                    .order('created_at', { ascending: false });\n\n                if (earningsError) {\n                    console.error('Error fetching earnings:', earningsError);\n                }\n\n                // Fetch orders for power pledge calculation\n                const { data: orders, error: ordersError } = await supabase\n                    .from('orders')\n                    .select('pledge_cost, shares, start_at, end_at')\n                    .eq('customer_id', user.id)\n                    .eq('review_status', 'approved');\n\n                if (ordersError) {\n                    console.error('Error fetching orders:', ordersError);\n                }\n\n                // Process the data\n                const processedData = processDashboardData(assets, earnings, orders);\n                setDashboardData(processedData.stats);\n                setChartData(processedData.chartData);\n\n            } catch (error) {\n                console.error('Error fetching dashboard data:', error);\n                setError('Failed to load dashboard data');\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchDashboardData();\n    }, []);\n\n    const handleNavigation = (path) => {\n        console.log('Navigating to:', path);\n        console.log('Current URL:', window.location.href);\n        navigate(path);\n    };\n\n    // Format number for display\n    const formatNumber = (num) => {\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n        }).format(num || 0);\n    };\n\n    if (error) {\n        return (\n            <Container fluid>\n                <Row className=\"mb-3\">\n                    <Col>\n                        <h2>{t('dashboard')}</h2>\n                        <div className=\"alert alert-danger\">{error}</div>\n                    </Col>\n                </Row>\n            </Container>\n        );\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('dashboard')}</h2>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col md={3}>\n                    <StatCard\n                        title={t('total_earnings')}\n                        value={`${formatNumber(dashboardData.totalEarnings)} FIL`}\n                        variant=\"primary\"\n                        loading={loading}\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('yesterday_earnings')}\n                        value={`${formatNumber(dashboardData.yesterdayEarnings)} FIL`}\n                        variant=\"success\"\n                        loading={loading}\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('available_balance')}\n                        value={`${formatNumber(dashboardData.availableBalance)} FIL`}\n                        variant=\"info\"\n                        loading={loading}\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('power_pledge')}\n                        value={`${formatNumber(dashboardData.powerPledge)} FIL`}\n                        variant=\"warning\"\n                        loading={loading}\n                    />\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('earnings_trend')}</Card.Title>\n                            {loading ? (\n                                <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\n                                    <Spinner animation=\"border\" />\n                                    <span className=\"ms-2\">{t('loading')}</span>\n                                </div>\n                            ) : (\n                                <ResponsiveContainer width=\"100%\" height={400}>\n                                    <LineChart data={chartData}>\n                                        <CartesianGrid strokeDasharray=\"3 3\" />\n                                        <XAxis dataKey=\"name\" />\n                                        <YAxis yAxisId=\"left\" label={{ value: 'FIL', angle: -90, position: 'insideLeft' }} />\n                                        <Legend />\n                                        <Line yAxisId=\"left\" type=\"monotone\" dataKey=\"fil\" stroke=\"#8884d8\" name={t('FIL_earnings')} />\n                                        <Line yAxisId=\"right\" type=\"monotone\" dataKey=\"usd\" stroke=\"#82ca9d\" name={t('USD_estimate')} />\n                                    </LineChart>\n                                </ResponsiveContainer>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n             <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('wallet_management')}</h4>\n                            <p>{t('manage_your_digital_assets')}</p>\n                            <Button\n                                variant=\"primary\"\n                                onClick={() => handleNavigation('/my')}\n                            >\n                                {t('enter_wallet')}\n                            </Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('buy_power')}</h4>\n                            <p>{t('view_and_purchase_new_power_products')}</p>\n                            <Button\n                                variant=\"success\"\n                                onClick={() => handleNavigation('/products')}\n                            >\n                                {t('browse_products')}\n                            </Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n        </Container>\n    );\n};\n\nexport default CustomerDashboard;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,OAAO,KAAQ,iBAAiB,CAC5E,OAASC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,aAAa,CAAEC,OAAO,CAAEC,MAAM,CAAEC,mBAAmB,KAAQ,UAAU,CAC7G,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,WAAW,KAAQ,sBAAsB,CAElD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,oBAAoB,CAAGA,CAACC,MAAM,CAAEC,QAAQ,CAAEC,MAAM,GAAK,CACvD;AACA,KAAM,CAAAC,QAAQ,CAAG,CAAAH,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEI,IAAI,CAACC,KAAK,EAAIA,KAAK,CAACC,aAAa,GAAK,KAAK,CAAC,GAAI,CAAC,CAAC,CAC3E,KAAM,CAAAC,gBAAgB,CAAGJ,QAAQ,CAACK,iBAAiB,EAAI,CAAC,CAExD;AACA,KAAM,CAAAC,aAAa,CAAG,CAAAR,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAES,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GAAKD,GAAG,EAAIC,OAAO,CAACC,aAAa,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,GAAI,CAAC,CAEpG;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAC5BD,SAAS,CAACE,OAAO,CAACF,SAAS,CAACG,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAC1C,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAAH,IAAI,CAACD,SAAS,CAACK,WAAW,CAAC,CAAC,CAAEL,SAAS,CAACM,QAAQ,CAAC,CAAC,CAAEN,SAAS,CAACG,OAAO,CAAC,CAAC,CAAC,CACnG,KAAM,CAAAI,YAAY,CAAG,GAAI,CAAAN,IAAI,CAACG,cAAc,CAAC,CAC7CG,YAAY,CAACL,OAAO,CAACK,YAAY,CAACJ,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAEhD,KAAM,CAAAK,iBAAiB,CAAG,CAAArB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,MAAM,CAACX,OAAO,EAAI,CAClD,KAAM,CAAAY,WAAW,CAAG,GAAI,CAAAT,IAAI,CAACH,OAAO,CAACa,UAAU,CAAC,CAChD,MAAO,CAAAD,WAAW,EAAIN,cAAc,EAAIM,WAAW,CAAGH,YAAY,CACtE,CAAC,CAAC,CAACX,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GAAKD,GAAG,EAAIC,OAAO,CAACC,aAAa,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,GAAI,CAAC,CAEvE;AACA,KAAM,CAAAa,GAAG,CAAG,GAAI,CAAAX,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAY,WAAW,CAAG,CAAAzB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEqB,MAAM,CAACK,KAAK,EAAI,CACxC,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAd,IAAI,CAACa,KAAK,CAACE,QAAQ,CAAC,CAC1C,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAhB,IAAI,CAACa,KAAK,CAACI,MAAM,CAAC,CACtC,MAAO,CAAAH,SAAS,EAAIH,GAAG,EAAIK,OAAO,EAAIL,GAAG,CAC7C,CAAC,CAAC,CAAChB,MAAM,CAAC,CAACC,GAAG,CAAEiB,KAAK,GAAKjB,GAAG,EAAIiB,KAAK,CAACK,WAAW,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,GAAI,CAAC,CAEjE;AACA,KAAM,CAAAC,SAAS,CAAG,EAAE,CACpB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI,CAAC,CAAEA,CAAC,EAAE,CAAE,CACzB,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAArB,IAAI,CAAC,CAAC,CACvBqB,IAAI,CAACpB,OAAO,CAACoB,IAAI,CAACnB,OAAO,CAAC,CAAC,CAAGkB,CAAC,CAAC,CAChC,KAAM,CAAAE,SAAS,CAAG,GAAI,CAAAtB,IAAI,CAACqB,IAAI,CAACjB,WAAW,CAAC,CAAC,CAAEiB,IAAI,CAAChB,QAAQ,CAAC,CAAC,CAAEgB,IAAI,CAACnB,OAAO,CAAC,CAAC,CAAC,CAC/E,KAAM,CAAAqB,OAAO,CAAG,GAAI,CAAAvB,IAAI,CAACsB,SAAS,CAAC,CACnCC,OAAO,CAACtB,OAAO,CAACsB,OAAO,CAACrB,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAEtC,KAAM,CAAAsB,WAAW,CAAG,CAAAtC,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,MAAM,CAACX,OAAO,EAAI,CAC5C,KAAM,CAAAY,WAAW,CAAG,GAAI,CAAAT,IAAI,CAACH,OAAO,CAACa,UAAU,CAAC,CAChD,MAAO,CAAAD,WAAW,EAAIa,SAAS,EAAIb,WAAW,CAAGc,OAAO,CAC5D,CAAC,CAAC,CAAC5B,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GAAKD,GAAG,EAAIC,OAAO,CAACC,aAAa,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,GAAI,CAAC,CAEvEqB,SAAS,CAACM,IAAI,CAAC,CACXC,IAAI,CAAE,GAAGL,IAAI,CAAChB,QAAQ,CAAC,CAAC,CAAG,CAAC,IAAIgB,IAAI,CAACnB,OAAO,CAAC,CAAC,EAAE,CAChDyB,GAAG,CAAEH,WAAW,CAChBI,GAAG,CAAEJ,WAAW,CAAG,IAAK;AAC5B,CAAC,CAAC,CACN,CAEA,MAAO,CACHK,KAAK,CAAE,CACHnC,aAAa,CACba,iBAAiB,CACjBf,gBAAgB,CAChBoB,WACJ,CAAC,CACDO,SACJ,CAAC,CACL,CAAC,CAED,KAAM,CAAAW,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,OAAQ,CAAC,CAAAL,IAAA,oBAC1DpD,IAAA,CAACd,IAAI,EAACwE,SAAS,CAAE,MAAMF,OAAO,kBAAmB,CAAAG,QAAA,cAC7CzD,KAAA,CAAChB,IAAI,CAAC0E,IAAI,EAAAD,QAAA,eACN3D,IAAA,CAACd,IAAI,CAAC2E,KAAK,EAAAF,QAAA,CAAEN,KAAK,CAAa,CAAC,CAC/BI,OAAO,cACJvD,KAAA,QAAKwD,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACtC3D,IAAA,CAACZ,OAAO,EAAC0E,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACL,SAAS,CAAC,MAAM,CAAE,CAAC,cACzD1D,IAAA,SAAA2D,QAAA,CAAM,YAAU,CAAM,CAAC,EACtB,CAAC,cAENzD,KAAA,CAAAE,SAAA,EAAAuD,QAAA,eACI3D,IAAA,OAAA2D,QAAA,CAAKL,KAAK,CAAK,CAAC,CACfC,QAAQ,eAAIvD,IAAA,MAAA2D,QAAA,CAAIJ,QAAQ,CAAI,CAAC,EAChC,CACL,EACM,CAAC,CACV,CAAC,EACV,CAED,KAAM,CAAAS,iBAAiB,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,CAAE,CAAC,CAAGnF,cAAc,CAAC,CAAC,CAC9B,KAAM,CAAAoF,QAAQ,CAAGrE,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAC4D,OAAO,CAAEU,UAAU,CAAC,CAAGvF,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwF,aAAa,CAAEC,gBAAgB,CAAC,CAAGzF,QAAQ,CAAC,CAC/CmC,aAAa,CAAE,CAAC,CAChBa,iBAAiB,CAAE,CAAC,CACpBf,gBAAgB,CAAE,CAAC,CACnBoB,WAAW,CAAE,CACjB,CAAC,CAAC,CACF,KAAM,CAACO,SAAS,CAAE8B,YAAY,CAAC,CAAG1F,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2F,KAAK,CAAEC,QAAQ,CAAC,CAAG5F,QAAQ,CAAC,IAAI,CAAC,CAExC;AACAC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAA4F,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACnC,KAAM,CAAAC,QAAQ,CAAG5E,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAC4E,QAAQ,CAAE,CACXF,QAAQ,CAAC,sCAAsC,CAAC,CAChDL,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA,GAAI,CACAA,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEQ,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,QAAQ,CAAC,oBAAoB,CAAC,CAC9BL,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEQ,IAAI,CAAErE,MAAM,CAAEiE,KAAK,CAAEQ,WAAY,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACtDM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,iDAAiD,CAAC,CACzDC,EAAE,CAAC,SAAS,CAAEN,IAAI,CAACO,EAAE,CAAC,CAE3B,GAAIJ,WAAW,CAAE,CACbK,OAAO,CAACb,KAAK,CAAC,wBAAwB,CAAEQ,WAAW,CAAC,CACxD,CAEA;AACA,KAAM,CAAEJ,IAAI,CAAEpE,QAAQ,CAAEgE,KAAK,CAAEc,aAAc,CAAC,CAAG,KAAM,CAAAX,QAAQ,CAC1DM,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC,2BAA2B,CAAC,CACnCC,EAAE,CAAC,aAAa,CAAEN,IAAI,CAACO,EAAE,CAAC,CAC1BjD,KAAK,CAAC,YAAY,CAAE,CAAEoD,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAID,aAAa,CAAE,CACfD,OAAO,CAACb,KAAK,CAAC,0BAA0B,CAAEc,aAAa,CAAC,CAC5D,CAEA;AACA,KAAM,CAAEV,IAAI,CAAEnE,MAAM,CAAE+D,KAAK,CAAEgB,WAAY,CAAC,CAAG,KAAM,CAAAb,QAAQ,CACtDM,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC,uCAAuC,CAAC,CAC/CC,EAAE,CAAC,aAAa,CAAEN,IAAI,CAACO,EAAE,CAAC,CAC1BD,EAAE,CAAC,eAAe,CAAE,UAAU,CAAC,CAEpC,GAAIK,WAAW,CAAE,CACbH,OAAO,CAACb,KAAK,CAAC,wBAAwB,CAAEgB,WAAW,CAAC,CACxD,CAEA;AACA,KAAM,CAAAC,aAAa,CAAGnF,oBAAoB,CAACC,MAAM,CAAEC,QAAQ,CAAEC,MAAM,CAAC,CACpE6D,gBAAgB,CAACmB,aAAa,CAACtC,KAAK,CAAC,CACrCoB,YAAY,CAACkB,aAAa,CAAChD,SAAS,CAAC,CAEzC,CAAE,MAAO+B,KAAK,CAAE,CACZa,OAAO,CAACb,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtDC,QAAQ,CAAC,+BAA+B,CAAC,CAC7C,CAAC,OAAS,CACNL,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAEDM,kBAAkB,CAAC,CAAC,CACxB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAgB,gBAAgB,CAAIC,IAAI,EAAK,CAC/BN,OAAO,CAACO,GAAG,CAAC,gBAAgB,CAAED,IAAI,CAAC,CACnCN,OAAO,CAACO,GAAG,CAAC,cAAc,CAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CACjD5B,QAAQ,CAACwB,IAAI,CAAC,CAClB,CAAC,CAED;AACA,KAAM,CAAAK,YAAY,CAAIC,GAAG,EAAK,CAC1B,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CAClCC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CAC3B,CAAC,CAAC,CAACC,MAAM,CAACL,GAAG,EAAI,CAAC,CAAC,CACvB,CAAC,CAED,GAAIzB,KAAK,CAAE,CACP,mBACIvE,IAAA,CAACjB,SAAS,EAACuH,KAAK,MAAA3C,QAAA,cACZ3D,IAAA,CAAChB,GAAG,EAAC0E,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBzD,KAAA,CAACjB,GAAG,EAAA0E,QAAA,eACA3D,IAAA,OAAA2D,QAAA,CAAKM,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBjE,IAAA,QAAK0D,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEY,KAAK,CAAM,CAAC,EAChD,CAAC,CACL,CAAC,CACC,CAAC,CAEpB,CAEA,mBACIrE,KAAA,CAACnB,SAAS,EAACuH,KAAK,MAAA3C,QAAA,eACZ3D,IAAA,CAAChB,GAAG,EAAC0E,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjB3D,IAAA,CAACf,GAAG,EAAA0E,QAAA,cACA3D,IAAA,OAAA2D,QAAA,CAAKM,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,CACxB,CAAC,CACL,CAAC,cAEN/D,KAAA,CAAClB,GAAG,EAAA2E,QAAA,eACA3D,IAAA,CAACf,GAAG,EAACsH,EAAE,CAAE,CAAE,CAAA5C,QAAA,cACP3D,IAAA,CAACmD,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,gBAAgB,CAAE,CAC3BX,KAAK,CAAE,GAAGyC,YAAY,CAAC3B,aAAa,CAACrD,aAAa,CAAC,MAAO,CAC1DyC,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACpB,CAAC,CACD,CAAC,cACNzD,IAAA,CAACf,GAAG,EAACsH,EAAE,CAAE,CAAE,CAAA5C,QAAA,cACP3D,IAAA,CAACmD,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,oBAAoB,CAAE,CAC/BX,KAAK,CAAE,GAAGyC,YAAY,CAAC3B,aAAa,CAACxC,iBAAiB,CAAC,MAAO,CAC9D4B,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACpB,CAAC,CACD,CAAC,cACNzD,IAAA,CAACf,GAAG,EAACsH,EAAE,CAAE,CAAE,CAAA5C,QAAA,cACP3D,IAAA,CAACmD,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,mBAAmB,CAAE,CAC9BX,KAAK,CAAE,GAAGyC,YAAY,CAAC3B,aAAa,CAACvD,gBAAgB,CAAC,MAAO,CAC7D2C,OAAO,CAAC,MAAM,CACdC,OAAO,CAAEA,OAAQ,CACpB,CAAC,CACD,CAAC,cACNzD,IAAA,CAACf,GAAG,EAACsH,EAAE,CAAE,CAAE,CAAA5C,QAAA,cACP3D,IAAA,CAACmD,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,cAAc,CAAE,CACzBX,KAAK,CAAE,GAAGyC,YAAY,CAAC3B,aAAa,CAACnC,WAAW,CAAC,MAAO,CACxDuB,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACpB,CAAC,CACD,CAAC,EACL,CAAC,cAENzD,IAAA,CAAChB,GAAG,EAAA2E,QAAA,cACA3D,IAAA,CAACf,GAAG,EAAA0E,QAAA,cACA3D,IAAA,CAACd,IAAI,EAAAyE,QAAA,cACDzD,KAAA,CAAChB,IAAI,CAAC0E,IAAI,EAAAD,QAAA,eACN3D,IAAA,CAACd,IAAI,CAAC2E,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,gBAAgB,CAAC,CAAa,CAAC,CAC7CR,OAAO,cACJvD,KAAA,QAAKwD,SAAS,CAAC,kDAAkD,CAAC8C,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAA9C,QAAA,eACzF3D,IAAA,CAACZ,OAAO,EAAC0E,SAAS,CAAC,QAAQ,CAAE,CAAC,cAC9B9D,IAAA,SAAM0D,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEM,CAAC,CAAC,SAAS,CAAC,CAAO,CAAC,EAC3C,CAAC,cAENjE,IAAA,CAACJ,mBAAmB,EAAC8G,KAAK,CAAC,MAAM,CAACD,MAAM,CAAE,GAAI,CAAA9C,QAAA,cAC1CzD,KAAA,CAACb,SAAS,EAACsF,IAAI,CAAEnC,SAAU,CAAAmB,QAAA,eACvB3D,IAAA,CAACP,aAAa,EAACkH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvC3G,IAAA,CAACT,KAAK,EAACqH,OAAO,CAAC,MAAM,CAAE,CAAC,cACxB5G,IAAA,CAACR,KAAK,EAACqH,OAAO,CAAC,MAAM,CAACC,KAAK,CAAE,CAAExD,KAAK,CAAE,KAAK,CAAEyD,KAAK,CAAE,CAAC,EAAE,CAAEC,QAAQ,CAAE,YAAa,CAAE,CAAE,CAAC,cACrFhH,IAAA,CAACL,MAAM,GAAE,CAAC,cACVK,IAAA,CAACV,IAAI,EAACuH,OAAO,CAAC,MAAM,CAACI,IAAI,CAAC,UAAU,CAACL,OAAO,CAAC,KAAK,CAACM,MAAM,CAAC,SAAS,CAACnE,IAAI,CAAEkB,CAAC,CAAC,cAAc,CAAE,CAAE,CAAC,cAC/FjE,IAAA,CAACV,IAAI,EAACuH,OAAO,CAAC,OAAO,CAACI,IAAI,CAAC,UAAU,CAACL,OAAO,CAAC,KAAK,CAACM,MAAM,CAAC,SAAS,CAACnE,IAAI,CAAEkB,CAAC,CAAC,cAAc,CAAE,CAAE,CAAC,EACzF,CAAC,CACK,CACxB,EACM,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAEL/D,KAAA,CAAClB,GAAG,EAAC0E,SAAS,CAAC,MAAM,CAAAC,QAAA,eAClB3D,IAAA,CAACf,GAAG,EAACsH,EAAE,CAAE,CAAE,CAAC7C,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC/B3D,IAAA,CAACd,IAAI,EAAAyE,QAAA,cACDzD,KAAA,CAAChB,IAAI,CAAC0E,IAAI,EAAAD,QAAA,eACN3D,IAAA,OAAA2D,QAAA,CAAKM,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCjE,IAAA,MAAA2D,QAAA,CAAIM,CAAC,CAAC,4BAA4B,CAAC,CAAI,CAAC,cACxCjE,IAAA,CAACb,MAAM,EACHqE,OAAO,CAAC,SAAS,CACjB2D,OAAO,CAAEA,CAAA,GAAM1B,gBAAgB,CAAC,KAAK,CAAE,CAAA9B,QAAA,CAEtCM,CAAC,CAAC,cAAc,CAAC,CACd,CAAC,EACF,CAAC,CACV,CAAC,CACN,CAAC,cACNjE,IAAA,CAACf,GAAG,EAACsH,EAAE,CAAE,CAAE,CAAC7C,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC/B3D,IAAA,CAACd,IAAI,EAAAyE,QAAA,cACDzD,KAAA,CAAChB,IAAI,CAAC0E,IAAI,EAAAD,QAAA,eACN3D,IAAA,OAAA2D,QAAA,CAAKM,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBjE,IAAA,MAAA2D,QAAA,CAAIM,CAAC,CAAC,sCAAsC,CAAC,CAAI,CAAC,cAClDjE,IAAA,CAACb,MAAM,EACHqE,OAAO,CAAC,SAAS,CACjB2D,OAAO,CAAEA,CAAA,GAAM1B,gBAAgB,CAAC,WAAW,CAAE,CAAA9B,QAAA,CAE5CM,CAAC,CAAC,iBAAiB,CAAC,CACjB,CAAC,EACF,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,EAEC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}