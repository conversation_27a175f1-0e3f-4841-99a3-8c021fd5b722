{"ast": null, "code": "import React,{Suspense,useEffect,useState}from'react';import{initSupabase}from'./supabaseClient';import{HashRouter,Routes,Route,Link,Navigate,useLocation}from'react-router-dom';import{Container,Navbar,Nav,NavDropdown}from'react-bootstrap';import{useTranslation}from'react-i18next';import{FaTachometerAlt,FaHardHat,FaUser,FaGlobe,FaCoins,FaChartBar,FaFileInvoiceDollar,FaUsers,FaShoppingBag,FaYenSign,FaBuffer}from'react-icons/fa';// Lazy load components for better performance\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=/*#__PURE__*/React.lazy(()=>import('./pages/LoginPage'));const CustomerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/customer/Dashboard'));const ProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/ProductListPage'));const OrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/OrderListPage'));const MyAccountPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyAccountPage'));const MyGainsPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyGainsPage'));const KycPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/KycPage'));const RecommendPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/RecommendPage'));const AgentDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Dashboard'));const Members=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Members'));const Recommend=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Recommend'));const AgentProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentProductListPage'));const DebugAgent=/*#__PURE__*/React.lazy(()=>import('./pages/agent/DebugAgent'));const MakerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Dashboard'));const MakerProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerProductListPage'));const MakerOrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerOrderListPage'));const MakerFacilityListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerFacilities'));const MakerMinerListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerMiners'));const MinerEarnings=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerEarnings'));const MinerSnapshots=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerSnapshots'));const Transactions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Transactions'));const CoinBatches=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CoinBatches'));const NetworkStats=/*#__PURE__*/React.lazy(()=>import('./pages/maker/NetworkStats'));const CustomerAssets=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CustomerAssets'));const OrderReports=/*#__PURE__*/React.lazy(()=>import('./pages/maker/OrderReports'));const OrderDistributions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/OrderDistributions'));const CapacityRequest=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CapacityRequest'));const ManualDeposits=/*#__PURE__*/React.lazy(()=>import('./pages/maker/ManualDeposits'));const AgentCapacityRequest=/*#__PURE__*/React.lazy(()=>import('./pages/agent/CapacityRequest'));const AgentNetworkStats=/*#__PURE__*/React.lazy(()=>import('./pages/agent/NetworkStats'));const WalletFlow=/*#__PURE__*/React.lazy(()=>import('./pages/agent/WalletFlow'));const AgentOrderReports=/*#__PURE__*/React.lazy(()=>import('./pages/agent/OrderReports'));const WithdrawList=/*#__PURE__*/React.lazy(()=>import('./pages/agent/WithdrawList'));const CustomerFilfox=/*#__PURE__*/React.lazy(()=>import('./pages/customer/Filfox'));function App(){const{t,i18n}=useTranslation();const[supabase,setSupabase]=useState(null);const[session,setSession]=useState(null);const[loading,setLoading]=useState(true);const role=localStorage.getItem('user_role');// 从 localStorage 读取用户角色\nuseEffect(()=>{const initialize=async()=>{const supa=await initSupabase();setSupabase(supa);const{data:{session}}=await supa.auth.getSession();setSession(session);supa.auth.onAuthStateChange((_event,newSession)=>{setSession(newSession);if(!newSession){localStorage.removeItem('user_role');}});setLoading(false);};initialize();},[]);const changeLanguage=lng=>{i18n.changeLanguage(lng);};// Debug: Log current URL and hash\nReact.useEffect(()=>{console.log('App mounted. Current URL:',window.location.href);console.log('Hash:',window.location.hash);},[]);// Require login to access protected pages\nconst RequireAuth=_ref=>{let{children}=_ref;const location=useLocation();if(!session){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}return children;};// Auto redirect from \"/\" based on role\nconst RoleRedirect=()=>{const role=localStorage.getItem('user_role');if(role==='maker')return/*#__PURE__*/_jsx(Navigate,{to:\"/maker\",replace:true});if(role==='agent')return/*#__PURE__*/_jsx(Navigate,{to:\"/agent\",replace:true});if(role==='customer')return/*#__PURE__*/_jsx(Navigate,{to:\"/customer\",replace:true});return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});// default to login page\n};return/*#__PURE__*/_jsx(HashRouter,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Navbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Navbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(Navbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsxs(Nav,{className:\"me-auto\",children:[role==='maker'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaHardHat,{className:\"me-1\"}),t('miner_management')]}),id:\"maker-miner-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/miners\",children:t('miner_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/facilities\",children:t('facility_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/earnings\",children:t('earnings_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/transfers\",children:t('transfer_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/snapshots\",children:t('daily_snapshot')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),t('operations_management')]}),id:\"maker-operations-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/capacity\",children:t('capacity_expansion_request')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/orders\",children:t('maker_orders')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/manual-deposits\",children:t('manual_deposit')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaCoins,{className:\"me-1\"}),t('coin_management')]}),id:\"maker-coin-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/coin-batches\",children:t('coin_batches')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/network-stats\",children:t('network_stats')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaChartBar,{className:\"me-1\"}),t('report_management')]}),id:\"maker-report-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/customer-assets\",children:t('customer_assets')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/order-reports\",children:t('order_reports')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/order-distributions\",children:t('order_distributions')})]})]}),role==='agent'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),t('operational_settings')]}),id:\"agent-operational-dropdown\",children:/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/power\",children:t('power_records')})}),/*#__PURE__*/_jsx(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaFileInvoiceDollar,{className:\"me-1\"}),t('profit_management')]}),id:\"agent-profit-dropdown\",children:/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/profit\",children:t('profit_records')})}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaUsers,{className:\"me-1\"}),t('member_management')]}),id:\"agent-member-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/member-list\",children:t('member_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/recommendation\",children:t('recommendation')})]}),/*#__PURE__*/_jsx(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaShoppingBag,{className:\"me-1\"}),t('product_management')]}),id:\"agent-product-dropdown\",children:/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/products\",children:t('product_list')})}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaYenSign,{className:\"me-1\"}),t('finance_management')]}),id:\"agent-finance-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/wallet-flow-list\",children:t('wallet_flow')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/withdraw-list\",children:t('withdraw_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/order-list\",children:t('order_list')})]})]}),role==='customer'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Nav.Link,{href:\"/customer/filfox\",children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),\"FilFox\"]}),/*#__PURE__*/_jsxs(Nav.Link,{href:\"/customer/assets\",children:[/*#__PURE__*/_jsx(FaBuffer,{className:\"me-1\"}),t('assets')]}),/*#__PURE__*/_jsxs(Nav.Link,{href:\"/customer/mypage\",children:[/*#__PURE__*/_jsx(FaUser,{className:\"me-1\"}),t('my_page')]})]}),/*#__PURE__*/_jsxs(Nav.Link,{href:\"#/\",children:[/*#__PURE__*/_jsx(FaTachometerAlt,{className:\"me-1\"}),t('dashboard')]})]}),/*#__PURE__*/_jsx(Nav,{children:/*#__PURE__*/_jsxs(NavDropdown,{title:t('language'),id:\"basic-nav-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('ja'),children:\"\\u65E5\\u672C\\u8A9E\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('zh'),children:\"\\u4E2D\\u6587\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('en'),children:\"English\"})]})})]})]})}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(\"div\",{children:t('loading')}),children:loading?/*#__PURE__*/_jsx(\"div\",{children:t('initializing_platform')}):!supabase?/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-danger\",children:t('backend_connection_failed')}):/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RoleRedirect,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/customer\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/customer/filfox\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerFilfox,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyAccountPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my-gains\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyGainsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/kyc\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(KycPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/recommend\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RecommendPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/member-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Members,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/recommendation\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Recommend,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/debug\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(DebugAgent,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/power\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentCapacityRequest,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/profit\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentNetworkStats,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/wallet-flow-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(WalletFlow,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/order-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentOrderReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/withdraw-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(WithdrawList,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerOrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/facilities\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerFacilityListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/miners\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerMinerListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/earnings\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerEarnings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/transfers\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Transactions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/snapshots\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerSnapshots,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/coin-batches\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CoinBatches,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/network-stats\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(NetworkStats,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/customer-assets\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerAssets,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/order-reports\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/order-distributions\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderDistributions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/capacity\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CapacityRequest,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/manual-deposits\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ManualDeposits,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})})})]})});}export default App;", "map": {"version": 3, "names": ["React", "Suspense", "useEffect", "useState", "initSupabase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Link", "Navigate", "useLocation", "Container", "<PERSON><PERSON><PERSON>", "Nav", "NavDropdown", "useTranslation", "FaTachometerAlt", "FaHardHat", "FaUser", "FaGlobe", "FaCoins", "FaChartBar", "FaFileInvoiceDollar", "FaUsers", "FaShoppingBag", "FaYenSign", "<PERSON>a<PERSON><PERSON><PERSON>", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "LoginPage", "lazy", "CustomerDashboard", "ProductListPage", "OrderListPage", "MyAccountPage", "MyGainsPage", "KycPage", "RecommendPage", "AgentDashboard", "Members", "Recommend", "AgentProductListPage", "DebugAgent", "MakerDashboard", "MakerProductListPage", "MakerOrderListPage", "MakerFacilityListPage", "MakerMinerListPage", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "MinerSnapshots", "Transactions", "CoinBatches", "NetworkStats", "CustomerAssets", "OrderReports", "OrderDistributions", "CapacityRequest", "ManualDeposits", "AgentCapacityRequest", "AgentNetworkStats", "WalletFlow", "AgentOrderReports", "WithdrawList", "CustomerFilfox", "App", "t", "i18n", "supabase", "set<PERSON><PERSON><PERSON><PERSON>", "session", "setSession", "loading", "setLoading", "role", "localStorage", "getItem", "initialize", "supa", "data", "auth", "getSession", "onAuthStateChange", "_event", "newSession", "removeItem", "changeLanguage", "lng", "console", "log", "window", "location", "href", "hash", "RequireAuth", "_ref", "children", "to", "state", "from", "replace", "RoleRedirect", "bg", "variant", "expand", "Toggle", "Collapse", "id", "className", "title", "<PERSON><PERSON>", "as", "onClick", "fallback", "path", "element"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/App.js"], "sourcesContent": ["import React, { Suspense, useEffect, useState } from 'react';\nimport { initSupabase } from './supabaseClient';\nimport {\n  HashRouter,\n  Routes,\n  Route,\n  Link,\n  Navigate,\n  useLocation,\n} from 'react-router-dom';\nimport { Container, Navbar, Nav, NavDropdown } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { FaTachometerAlt, FaHardHat, FaUser, FaGlobe, FaCoins, FaChartBar, FaFileInvoiceDollar, FaUsers, FaShoppingBag, FaYenSign, FaBuffer } from 'react-icons/fa';\n\n// Lazy load components for better performance\nconst LoginPage = React.lazy(() => import('./pages/LoginPage'));\nconst CustomerDashboard = React.lazy(() => import('./pages/customer/Dashboard'));\nconst ProductListPage = React.lazy(() => import('./pages/customer/ProductListPage'));\nconst OrderListPage = React.lazy(() => import('./pages/customer/OrderListPage'));\nconst MyAccountPage = React.lazy(() => import('./pages/customer/MyAccountPage'));\nconst MyGainsPage = React.lazy(() => import('./pages/customer/MyGainsPage'));\nconst KycPage = React.lazy(() => import('./pages/customer/KycPage'));\nconst RecommendPage = React.lazy(() => import('./pages/customer/RecommendPage'));\nconst AgentDashboard = React.lazy(() => import('./pages/agent/Dashboard'));\nconst Members = React.lazy(() => import('./pages/agent/Members'));\nconst Recommend = React.lazy(() => import('./pages/agent/Recommend'));\nconst AgentProductListPage = React.lazy(() => import('./pages/agent/AgentProductListPage'));\nconst DebugAgent = React.lazy(() => import('./pages/agent/DebugAgent'));\nconst MakerDashboard = React.lazy(() => import('./pages/maker/Dashboard'));\nconst MakerProductListPage = React.lazy(() => import('./pages/maker/MakerProductListPage'));\nconst MakerOrderListPage = React.lazy(() => import('./pages/maker/MakerOrderListPage'));\nconst MakerFacilityListPage = React.lazy(() => import('./pages/maker/MakerFacilities'));\nconst MakerMinerListPage = React.lazy(() => import('./pages/maker/MakerMiners'));\nconst MinerEarnings = React.lazy(() => import('./pages/maker/MinerEarnings'));\nconst MinerSnapshots = React.lazy(() => import('./pages/maker/MinerSnapshots'));\nconst Transactions = React.lazy(() => import('./pages/maker/Transactions'));\nconst CoinBatches = React.lazy(() => import('./pages/maker/CoinBatches'));\nconst NetworkStats = React.lazy(() => import('./pages/maker/NetworkStats'));\nconst CustomerAssets = React.lazy(() => import('./pages/maker/CustomerAssets'));\nconst OrderReports = React.lazy(() => import('./pages/maker/OrderReports'));\nconst OrderDistributions = React.lazy(() => import('./pages/maker/OrderDistributions'));\nconst CapacityRequest = React.lazy(() => import('./pages/maker/CapacityRequest'));\nconst ManualDeposits = React.lazy(() => import('./pages/maker/ManualDeposits'));\nconst AgentCapacityRequest = React.lazy(() => import('./pages/agent/CapacityRequest'));\nconst AgentNetworkStats = React.lazy(() => import('./pages/agent/NetworkStats'));\nconst WalletFlow = React.lazy(() => import('./pages/agent/WalletFlow'));\nconst AgentOrderReports = React.lazy(() => import('./pages/agent/OrderReports'));\nconst WithdrawList = React.lazy(() => import('./pages/agent/WithdrawList'));\nconst CustomerFilfox = React.lazy(() => import('./pages/customer/Filfox'));\n\nfunction App() {\n  const { t, i18n } = useTranslation();\n  const [supabase, setSupabase] = useState(null);\n  const [session, setSession] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const role = localStorage.getItem('user_role'); // 从 localStorage 读取用户角色\n\n  useEffect(() => {\n    const initialize = async () => {\n      const supa = await initSupabase();\n      setSupabase(supa);\n\n      const { data: { session } } = await supa.auth.getSession();\n      setSession(session);\n\n      supa.auth.onAuthStateChange((_event, newSession) => {\n        setSession(newSession);\n        if (!newSession) {\n          localStorage.removeItem('user_role');\n        }\n      });\n\n      setLoading(false);\n    };\n    initialize();\n  }, []);\n\n  const changeLanguage = (lng) => {\n    i18n.changeLanguage(lng);\n  };\n\n  // Debug: Log current URL and hash\n  React.useEffect(() => {\n    console.log('App mounted. Current URL:', window.location.href);\n    console.log('Hash:', window.location.hash);\n  }, []);\n\n  // Require login to access protected pages\n  const RequireAuth = ({ children }) => {\n    const location = useLocation();\n    if (!session) {\n      return <Navigate to=\"/login\" state={{ from: location }} replace />;\n    }\n    return children;\n  };\n\n  // Auto redirect from \"/\" based on role\n  const RoleRedirect = () => {\n    const role = localStorage.getItem('user_role');\n    if (role === 'maker') return <Navigate to=\"/maker\" replace />;\n    if (role === 'agent') return <Navigate to=\"/agent\" replace />;\n    if (role === 'customer') return <Navigate to=\"/customer\" replace />;\n    return <Navigate to=\"/login\" replace />; // default to login page\n  };\n\n  return (\n    <HashRouter>\n      <div>\n        <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\">\n          <Container>\n            <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n            <Navbar.Collapse id=\"basic-navbar-nav\">\n              <Nav className=\"me-auto\">\n                {/* ===== ★ Maker 导航开始 ★ ===== */}\n                {role === 'maker' && (\n                  <>\n                    {/* Miner Management 下拉 */}\n                    <NavDropdown\n                      title={\n                        <>\n                          <FaHardHat className=\"me-1\" />\n                          {t('miner_management')}\n                        </>\n                      }\n                      id=\"maker-miner-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/miners\">\n                        {t('miner_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/facilities\">\n                        {t('facility_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/earnings\">\n                        {t('earnings_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/transfers\">\n                        {t('transfer_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/snapshots\">\n                        {t('daily_snapshot')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaGlobe className=\"me-1\" />\n                          {t('operations_management')}\n                        </>\n                      }\n                      id=\"maker-operations-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/capacity\">\n                        {t('capacity_expansion_request')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/orders\">\n                        {t('maker_orders')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/manual-deposits\">\n                        {t('manual_deposit')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaCoins className=\"me-1\" />\n                          {t('coin_management')}\n                        </>\n                      }\n                      id=\"maker-coin-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/coin-batches\">\n                        {t('coin_batches')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/network-stats\">\n                        {t('network_stats')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaChartBar className=\"me-1\" />\n                          {t('report_management')}\n                        </>\n                      }\n                      id=\"maker-report-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/customer-assets\">\n                        {t('customer_assets')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/order-reports\">\n                        {t('order_reports')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/order-distributions\">\n                        {t('order_distributions')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n                  </>\n                )}\n                {/* ===== ★ Maker 导航结束 ★ ===== */}\n\n                {/* ===== ★ Agent 导航开始 ★ ===== */}\n                {role === 'agent' && (\n                  <>\n                    {/* Agent Management 下拉 */}\n                    <NavDropdown\n                      title={\n                        <>\n                          <FaGlobe className=\"me-1\" />\n                          {t('operational_settings')}\n                        </>\n                      }\n                      id=\"agent-operational-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/power\">\n                        {t('power_records')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaFileInvoiceDollar className=\"me-1\" />\n                          {t('profit_management')}\n                        </>\n                      }\n                      id=\"agent-profit-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/profit\">\n                        {t('profit_records')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaUsers className=\"me-1\" />\n                          {t('member_management')}\n                        </>\n                      }\n                      id=\"agent-member-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/member-list\">\n                        {t('member_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/recommendation\">\n                        {t('recommendation')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaShoppingBag className=\"me-1\" />\n                          {t('product_management')}\n                        </>\n                      }\n                      id=\"agent-product-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/products\">\n                        {t('product_list')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaYenSign className=\"me-1\" />\n                          {t('finance_management')}\n                        </>\n                      }\n                      id=\"agent-finance-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/wallet-flow-list\">\n                        {t('wallet_flow')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/withdraw-list\">\n                        {t('withdraw_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/order-list\">\n                        {t('order_list')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n                  </>\n                )}\n                {/* ===== ★ Agent 导航结束 ★ ===== */}\n\n                {/* ===== ★ Customer 导航开始 ★ ===== */}\n                {role === 'customer' && (\n                  <>\n                    {/* Customer Management 下拉 */}\n                    <Nav.Link href=\"/customer/filfox\">\n                      <FaGlobe className=\"me-1\" />\n                      FilFox\n                    </Nav.Link>\n                    <Nav.Link href=\"/customer/assets\">\n                      <FaBuffer className=\"me-1\" />\n                      {t('assets')}\n                    </Nav.Link>\n                    <Nav.Link href=\"/customer/mypage\">\n                      <FaUser className=\"me-1\" />\n                      {t('my_page')}\n                    </Nav.Link>\n                  </>\n                )}\n                {/* ===== ★ Customer 导航结束 ★ ===== */}\n\n                <Nav.Link href=\"#/\">\n                  <FaTachometerAlt className=\"me-1\" />\n                  {t('dashboard')}\n                </Nav.Link>\n                {/* Add other nav links based on role later */}\n              </Nav>\n              <Nav>\n                <NavDropdown title={t('language')} id=\"basic-nav-dropdown\">\n                  <NavDropdown.Item onClick={() => changeLanguage('ja')}>日本語</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('zh')}>中文</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('en')}>English</NavDropdown.Item>\n                </NavDropdown>\n              </Nav>\n            </Navbar.Collapse>\n          </Container>\n        </Navbar>\n\n        <Container className=\"mt-4\">\n          <Suspense fallback={<div>{t('loading')}</div>}>\n            {loading ? (\n              <div>{t('initializing_platform')}</div>\n            ) : !supabase ? (\n              <div className=\"alert alert-danger\">{t('backend_connection_failed')}</div>\n            ) : (\n              <Routes>\n                {/* Public Route */}\n                <Route path=\"/login\" element={<LoginPage />} />\n\n                {/* Root path → redirect by role */}\n                <Route path=\"/\" element={<RequireAuth><RoleRedirect /></RequireAuth>} />\n\n                {/* Customer Routes */}\n                <Route path=\"/customer\" element={<RequireAuth><CustomerDashboard /></RequireAuth>} />\n                <Route path=\"/customer/filfox\" element={<RequireAuth><CustomerFilfox /></RequireAuth>} />\n                <Route path=\"/products\" element={<RequireAuth><ProductListPage /></RequireAuth>} />\n                <Route path=\"/orders\" element={<RequireAuth><OrderListPage /></RequireAuth>} />\n                <Route path=\"/my\" element={<RequireAuth><MyAccountPage /></RequireAuth>} />\n                <Route path=\"/my-gains\" element={<RequireAuth><MyGainsPage /></RequireAuth>} />\n                <Route path=\"/my/kyc\" element={<RequireAuth><KycPage /></RequireAuth>} />\n                <Route path=\"/my/recommend\" element={<RequireAuth><RecommendPage /></RequireAuth>} />\n\n                {/* Agent Routes */}\n                <Route path=\"/agent\" element={<RequireAuth><AgentDashboard /></RequireAuth>} />\n                <Route path=\"/agent/member-list\" element={<RequireAuth><Members /></RequireAuth>} />\n                <Route path=\"/agent/recommendation\" element={<RequireAuth><Recommend /></RequireAuth>} />\n                <Route path=\"/agent/products\" element={<RequireAuth><AgentProductListPage /></RequireAuth>} />\n                <Route path=\"/agent/debug\" element={<RequireAuth><DebugAgent /></RequireAuth>} />\n                <Route path=\"/agent/power\" element={<RequireAuth><AgentCapacityRequest /></RequireAuth>} />\n                <Route path=\"/agent/profit\" element={<RequireAuth><AgentNetworkStats /></RequireAuth>} />\n                <Route path=\"/agent/wallet-flow-list\" element={<RequireAuth><WalletFlow /></RequireAuth>} />\n                <Route path=\"/agent/order-list\" element={<RequireAuth><AgentOrderReports /></RequireAuth>} />\n                <Route path=\"/agent/withdraw-list\" element={<RequireAuth><WithdrawList /></RequireAuth>} />\n\n                {/* Maker Routes */}\n                  <Route path=\"/maker\" element={<RequireAuth><MakerDashboard /></RequireAuth>} />\n                  <Route path=\"/maker/products\" element={<RequireAuth><MakerProductListPage /></RequireAuth>} />\n                  <Route path=\"/maker/orders\" element={<RequireAuth><MakerOrderListPage /></RequireAuth>} />\n                  <Route path=\"/maker/facilities\" element={<RequireAuth><MakerFacilityListPage /></RequireAuth>} />\n                  <Route path=\"/maker/miners\" element={<RequireAuth><MakerMinerListPage /></RequireAuth>} />\n                  <Route path=\"/maker/earnings\" element={<RequireAuth><MinerEarnings /></RequireAuth>} />\n                  <Route path=\"/maker/transfers\" element={<RequireAuth><Transactions /></RequireAuth>} />\n                  <Route path=\"/maker/snapshots\" element={<RequireAuth><MinerSnapshots /></RequireAuth>} />\n                  <Route path=\"/maker/coin-batches\" element={<RequireAuth><CoinBatches /></RequireAuth>} />\n                  <Route path=\"/maker/network-stats\" element={<RequireAuth><NetworkStats /></RequireAuth>} />\n                  <Route path=\"/maker/customer-assets\" element={<RequireAuth><CustomerAssets /></RequireAuth>} />\n                  <Route path=\"/maker/order-reports\" element={<RequireAuth><OrderReports /></RequireAuth>} />\n                  <Route path=\"/maker/order-distributions\" element={<RequireAuth><OrderDistributions /></RequireAuth>} />\n                  <Route path=\"/maker/capacity\" element={<RequireAuth><CapacityRequest /></RequireAuth>} />\n                  <Route path=\"/maker/manual-deposits\" element={<RequireAuth><ManualDeposits /></RequireAuth>} />\n                \n                {/* Fallback */}\n                <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n              </Routes>\n            )}\n          </Suspense>\n        </Container>\n      </div>\n    </HashRouter>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC5D,OAASC,YAAY,KAAQ,kBAAkB,CAC/C,OACEC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,WAAW,KACN,kBAAkB,CACzB,OAASC,SAAS,CAAEC,MAAM,CAAEC,GAAG,CAAEC,WAAW,KAAQ,iBAAiB,CACrE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,eAAe,CAAEC,SAAS,CAAEC,MAAM,CAAEC,OAAO,CAAEC,OAAO,CAAEC,UAAU,CAAEC,mBAAmB,CAAEC,OAAO,CAAEC,aAAa,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,gBAAgB,CAEnK;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,SAAS,cAAGjC,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAC,iBAAiB,cAAGnC,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAAE,eAAe,cAAGpC,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACpF,KAAM,CAAAG,aAAa,cAAGrC,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAI,aAAa,cAAGtC,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAK,WAAW,cAAGvC,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC5E,KAAM,CAAAM,OAAO,cAAGxC,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACpE,KAAM,CAAAO,aAAa,cAAGzC,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAQ,cAAc,cAAG1C,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAS,OAAO,cAAG3C,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC,CACjE,KAAM,CAAAU,SAAS,cAAG5C,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CACrE,KAAM,CAAAW,oBAAoB,cAAG7C,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAY,UAAU,cAAG9C,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACvE,KAAM,CAAAa,cAAc,cAAG/C,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAc,oBAAoB,cAAGhD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAe,kBAAkB,cAAGjD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACvF,KAAM,CAAAgB,qBAAqB,cAAGlD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACvF,KAAM,CAAAiB,kBAAkB,cAAGnD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAChF,KAAM,CAAAkB,aAAa,cAAGpD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC7E,KAAM,CAAAmB,cAAc,cAAGrD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAoB,YAAY,cAAGtD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAqB,WAAW,cAAGvD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CACzE,KAAM,CAAAsB,YAAY,cAAGxD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAuB,cAAc,cAAGzD,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAwB,YAAY,cAAG1D,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAyB,kBAAkB,cAAG3D,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACvF,KAAM,CAAA0B,eAAe,cAAG5D,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACjF,KAAM,CAAA2B,cAAc,cAAG7D,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAA4B,oBAAoB,cAAG9D,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACtF,KAAM,CAAA6B,iBAAiB,cAAG/D,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAA8B,UAAU,cAAGhE,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACvE,KAAM,CAAA+B,iBAAiB,cAAGjE,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAAgC,YAAY,cAAGlE,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAiC,cAAc,cAAGnE,KAAK,CAACkC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAE1E,QAAS,CAAAkC,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGvD,cAAc,CAAC,CAAC,CACpC,KAAM,CAACwD,QAAQ,CAAEC,WAAW,CAAC,CAAGrE,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACsE,OAAO,CAAEC,UAAU,CAAC,CAAGvE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwE,OAAO,CAAEC,UAAU,CAAC,CAAGzE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAA0E,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAE;AAEhD7E,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8E,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAA7E,YAAY,CAAC,CAAC,CACjCoE,WAAW,CAACS,IAAI,CAAC,CAEjB,KAAM,CAAEC,IAAI,CAAE,CAAET,OAAQ,CAAE,CAAC,CAAG,KAAM,CAAAQ,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,CAAC,CAC1DV,UAAU,CAACD,OAAO,CAAC,CAEnBQ,IAAI,CAACE,IAAI,CAACE,iBAAiB,CAAC,CAACC,MAAM,CAAEC,UAAU,GAAK,CAClDb,UAAU,CAACa,UAAU,CAAC,CACtB,GAAI,CAACA,UAAU,CAAE,CACfT,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC,CACtC,CACF,CAAC,CAAC,CAEFZ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CACDI,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,cAAc,CAAIC,GAAG,EAAK,CAC9BpB,IAAI,CAACmB,cAAc,CAACC,GAAG,CAAC,CAC1B,CAAC,CAED;AACA1F,KAAK,CAACE,SAAS,CAAC,IAAM,CACpByF,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAC9DJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEC,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAC,CAC5C,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC/B,KAAM,CAAAJ,QAAQ,CAAGpF,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAC+D,OAAO,CAAE,CACZ,mBAAO7C,IAAA,CAACnB,QAAQ,EAAC2F,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAER,QAAS,CAAE,CAACS,OAAO,MAAE,CAAC,CACpE,CACA,MAAO,CAAAJ,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAA3B,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC9C,GAAIF,IAAI,GAAK,OAAO,CAAE,mBAAOjD,IAAA,CAACnB,QAAQ,EAAC2F,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,GAAI1B,IAAI,GAAK,OAAO,CAAE,mBAAOjD,IAAA,CAACnB,QAAQ,EAAC2F,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,GAAI1B,IAAI,GAAK,UAAU,CAAE,mBAAOjD,IAAA,CAACnB,QAAQ,EAAC2F,EAAE,CAAC,WAAW,CAACG,OAAO,MAAE,CAAC,CACnE,mBAAO3E,IAAA,CAACnB,QAAQ,EAAC2F,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAAE;AAC3C,CAAC,CAED,mBACE3E,IAAA,CAACvB,UAAU,EAAA8F,QAAA,cACTnE,KAAA,QAAAmE,QAAA,eACEvE,IAAA,CAAChB,MAAM,EAAC6F,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAAR,QAAA,cAC1CnE,KAAA,CAACrB,SAAS,EAAAwF,QAAA,eACRvE,IAAA,CAAChB,MAAM,CAACgG,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAClD5E,KAAA,CAACpB,MAAM,CAACiG,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAX,QAAA,eACpCnE,KAAA,CAACnB,GAAG,EAACkG,SAAS,CAAC,SAAS,CAAAZ,QAAA,EAErBtB,IAAI,GAAK,OAAO,eACf7C,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eAEEnE,KAAA,CAAClB,WAAW,EACVkG,KAAK,cACHhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACX,SAAS,EAAC8F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7B1C,CAAC,CAAC,kBAAkB,CAAC,EACtB,CACH,CACDyC,EAAE,CAAC,sBAAsB,CAAAX,QAAA,eAEzBvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,YAAY,CAAC,CACA,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAC/C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9C9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdrC,KAAA,CAAClB,WAAW,EAACkG,KAAK,cACdhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACT,OAAO,EAAC4F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B1C,CAAC,CAAC,uBAAuB,CAAC,EAC3B,CACH,CACDyC,EAAE,CAAC,2BAA2B,CAAAX,QAAA,eAE9BvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,4BAA4B,CAAC,CAChB,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpD9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdrC,KAAA,CAAClB,WAAW,EAACkG,KAAK,cACdhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACR,OAAO,EAAC2F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B1C,CAAC,CAAC,iBAAiB,CAAC,EACrB,CACH,CACDyC,EAAE,CAAC,qBAAqB,CAAAX,QAAA,eAExBvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,qBAAqB,CAAAD,QAAA,CACjD9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClD9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,EACR,CAAC,cAEdrC,KAAA,CAAClB,WAAW,EAACkG,KAAK,cACdhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACP,UAAU,EAAC0F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC9B1C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACDyC,EAAE,CAAC,uBAAuB,CAAAX,QAAA,eAE1BvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpD9B,CAAC,CAAC,iBAAiB,CAAC,CACL,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClD9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,4BAA4B,CAAAD,QAAA,CACxD9B,CAAC,CAAC,qBAAqB,CAAC,CACT,CAAC,EACR,CAAC,EACd,CACH,CAIAQ,IAAI,GAAK,OAAO,eACf7C,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eAEEvE,IAAA,CAACd,WAAW,EACVkG,KAAK,cACHhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACT,OAAO,EAAC4F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B1C,CAAC,CAAC,sBAAsB,CAAC,EAC1B,CACH,CACDyC,EAAE,CAAC,4BAA4B,CAAAX,QAAA,cAE/BvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,cAAc,CAAAD,QAAA,CAC1C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,CACR,CAAC,cAEdzC,IAAA,CAACd,WAAW,EAACkG,KAAK,cACdhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACN,mBAAmB,EAACyF,SAAS,CAAC,MAAM,CAAE,CAAC,CACvC1C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACDyC,EAAE,CAAC,uBAAuB,CAAAX,QAAA,cAE1BvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,CACR,CAAC,cAEdrC,KAAA,CAAClB,WAAW,EAACkG,KAAK,cACdhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACL,OAAO,EAACwF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B1C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACDyC,EAAE,CAAC,uBAAuB,CAAAX,QAAA,eAE1BvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,oBAAoB,CAAAD,QAAA,CAChD9B,CAAC,CAAC,aAAa,CAAC,CACD,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,uBAAuB,CAAAD,QAAA,CACnD9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdzC,IAAA,CAACd,WAAW,EAACkG,KAAK,cACdhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACJ,aAAa,EAACuF,SAAS,CAAC,MAAM,CAAE,CAAC,CACjC1C,CAAC,CAAC,oBAAoB,CAAC,EACxB,CACH,CACDyC,EAAE,CAAC,wBAAwB,CAAAX,QAAA,cAE3BvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,CACR,CAAC,cAEdrC,KAAA,CAAClB,WAAW,EAACkG,KAAK,cACdhF,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eACEvE,IAAA,CAACH,SAAS,EAACsF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7B1C,CAAC,CAAC,oBAAoB,CAAC,EACxB,CACH,CACDyC,EAAE,CAAC,wBAAwB,CAAAX,QAAA,eAE3BvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,yBAAyB,CAAAD,QAAA,CACrD9B,CAAC,CAAC,aAAa,CAAC,CACD,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClD9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBzC,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACC,EAAE,CAAE1G,IAAK,CAAC4F,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAC/C9B,CAAC,CAAC,YAAY,CAAC,CACA,CAAC,EACR,CAAC,EACd,CACH,CAIAQ,IAAI,GAAK,UAAU,eAClB7C,KAAA,CAAAF,SAAA,EAAAqE,QAAA,eAEEnE,KAAA,CAACnB,GAAG,CAACL,IAAI,EAACuF,IAAI,CAAC,kBAAkB,CAAAI,QAAA,eAC/BvE,IAAA,CAACT,OAAO,EAAC4F,SAAS,CAAC,MAAM,CAAE,CAAC,SAE9B,EAAU,CAAC,cACX/E,KAAA,CAACnB,GAAG,CAACL,IAAI,EAACuF,IAAI,CAAC,kBAAkB,CAAAI,QAAA,eAC/BvE,IAAA,CAACF,QAAQ,EAACqF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC5B1C,CAAC,CAAC,QAAQ,CAAC,EACJ,CAAC,cACXrC,KAAA,CAACnB,GAAG,CAACL,IAAI,EAACuF,IAAI,CAAC,kBAAkB,CAAAI,QAAA,eAC/BvE,IAAA,CAACV,MAAM,EAAC6F,SAAS,CAAC,MAAM,CAAE,CAAC,CAC1B1C,CAAC,CAAC,SAAS,CAAC,EACL,CAAC,EACX,CACH,cAGDrC,KAAA,CAACnB,GAAG,CAACL,IAAI,EAACuF,IAAI,CAAC,IAAI,CAAAI,QAAA,eACjBvE,IAAA,CAACZ,eAAe,EAAC+F,SAAS,CAAC,MAAM,CAAE,CAAC,CACnC1C,CAAC,CAAC,WAAW,CAAC,EACP,CAAC,EAER,CAAC,cACNzC,IAAA,CAACf,GAAG,EAAAsF,QAAA,cACFnE,KAAA,CAAClB,WAAW,EAACkG,KAAK,CAAE3C,CAAC,CAAC,UAAU,CAAE,CAACyC,EAAE,CAAC,oBAAoB,CAAAX,QAAA,eACxDvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM1B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,oBAAG,CAAkB,CAAC,cAC7EvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM1B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,cAAE,CAAkB,CAAC,cAC5EvE,IAAA,CAACd,WAAW,CAACmG,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM1B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,SAAO,CAAkB,CAAC,EACtE,CAAC,CACX,CAAC,EACS,CAAC,EACT,CAAC,CACN,CAAC,cAETvE,IAAA,CAACjB,SAAS,EAACoG,SAAS,CAAC,MAAM,CAAAZ,QAAA,cACzBvE,IAAA,CAAC3B,QAAQ,EAACmH,QAAQ,cAAExF,IAAA,QAAAuE,QAAA,CAAM9B,CAAC,CAAC,SAAS,CAAC,CAAM,CAAE,CAAA8B,QAAA,CAC3CxB,OAAO,cACN/C,IAAA,QAAAuE,QAAA,CAAM9B,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CACrC,CAACE,QAAQ,cACX3C,IAAA,QAAKmF,SAAS,CAAC,oBAAoB,CAAAZ,QAAA,CAAE9B,CAAC,CAAC,2BAA2B,CAAC,CAAM,CAAC,cAE1ErC,KAAA,CAAC1B,MAAM,EAAA6F,QAAA,eAELvE,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE1F,IAAA,CAACK,SAAS,GAAE,CAAE,CAAE,CAAC,cAG/CL,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAAC4E,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGxE5E,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACO,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACrFP,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACuC,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFvC,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACQ,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFR,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,SAAS,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACS,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/ET,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,KAAK,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACU,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3EV,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACW,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/EX,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,SAAS,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACY,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzEZ,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACa,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGrFb,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACc,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/Ed,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACe,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACpFf,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACgB,SAAS,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFhB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACiB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC9FjB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,cAAc,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACkB,UAAU,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACjFlB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,cAAc,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACkC,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3FlC,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACmC,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFnC,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACoC,UAAU,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC5FpC,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACqC,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC7FrC,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACsC,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGzFtC,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACmB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/EnB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACoB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC9FpB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACqB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FrB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACsB,qBAAqB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACjGtB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACuB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FvB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACwB,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFxB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAAC0B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvF1B,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACyB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFzB,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAAC2B,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzF3B,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAAC4B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F5B,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAAC6B,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/F7B,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAAC8B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F9B,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,4BAA4B,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAAC+B,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvG/B,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACgC,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFhC,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAE1F,IAAA,CAACqE,WAAW,EAAAE,QAAA,cAACvE,IAAA,CAACiC,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGjGjC,IAAA,CAACrB,KAAK,EAAC8G,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE1F,IAAA,CAACnB,QAAQ,EAAC2F,EAAE,CAAC,GAAG,CAACG,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CACT,CACO,CAAC,CACF,CAAC,EACT,CAAC,CACI,CAAC,CAEjB,CAEA,cAAe,CAAAnC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}