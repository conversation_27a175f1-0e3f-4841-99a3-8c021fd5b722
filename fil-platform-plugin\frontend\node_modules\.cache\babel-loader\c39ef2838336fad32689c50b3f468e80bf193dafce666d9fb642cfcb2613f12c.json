{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{Container,Row,Col,Card,Table,Spinner,<PERSON><PERSON>,<PERSON><PERSON>}from'react-bootstrap';import{<PERSON><PERSON><PERSON>,Line,<PERSON>Axis,<PERSON><PERSON><PERSON>s,Car<PERSON>ianGrid,<PERSON><PERSON><PERSON>,<PERSON>,ResponsiveContainer,<PERSON><PERSON><PERSON>,<PERSON>}from'recharts';import{getSupabase}from'../../supabaseClient';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const StatCard=_ref=>{let{title,value,unit,variant,loading,icon}=_ref;return/*#__PURE__*/_jsx(Card,{className:`bg-${variant} text-white mb-3 h-100`,children:/*#__PURE__*/_jsx(Card.Body,{className:\"d-flex flex-column justify-content-between\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-start\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Card.Title,{className:\"h6\",children:title}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading...\"})]}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:value}),unit&&/*#__PURE__*/_jsx(\"small\",{className:\"opacity-75\",children:unit})]})]}),icon&&/*#__PURE__*/_jsx(\"div\",{className:\"fs-2 opacity-50\",children:icon})]})})});};const Filfox=()=>{const{t}=useTranslation();const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[currentStats,setCurrentStats]=useState(null);const[historicalData,setHistoricalData]=useState([]);const[refreshing,setRefreshing]=useState(false);// Fetch current and historical network stats\nconst fetchNetworkStats=async()=>{const supabase=getSupabase();if(!supabase){setError('Failed to initialize Supabase client');setLoading(false);return;}try{setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setError('User not logged in');setLoading(false);return;}// Fetch latest network stats\nconst{data:latestData,error:latestError}=await supabase.from('network_stats').select('*').order('stat_date',{ascending:false}).limit(1);if(latestError){console.error('Error fetching latest stats:',latestError);setError('Failed to fetch latest network statistics');return;}if(latestData&&latestData.length>0){setCurrentStats(latestData[0]);}// Fetch historical data for charts (last 30 days)\nconst{data:historicalData,error:historicalError}=await supabase.from('network_stats').select('*').order('stat_date',{ascending:false}).limit(30);if(historicalError){console.error('Error fetching historical stats:',historicalError);}else{// Reverse to show chronological order in charts\nsetHistoricalData(historicalData.reverse());}}catch(error){console.error('Error fetching network stats:',error);setError('Failed to load network statistics');}finally{setLoading(false);setRefreshing(false);}};useEffect(()=>{fetchNetworkStats();},[]);const handleRefresh=()=>{setRefreshing(true);fetchNetworkStats();};const formatNumber=num=>{if(num===null||num===undefined)return'N/A';return new Intl.NumberFormat('en-US',{minimumFractionDigits:0,maximumFractionDigits:4}).format(num);};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};if(error){return/*#__PURE__*/_jsx(Container,{fluid:true,children:/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"h2\",{children:t('filfox_network_stats')}),/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error})]})})});}return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsx(\"h2\",{children:t('filfox_network_stats')}),/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",onClick:handleRefresh,disabled:refreshing,children:refreshing?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),t('refreshing')]}):t('refresh')})]})})}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('block_height'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.block_height),variant:\"primary\",loading:loading,icon:\"\\uD83D\\uDD17\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('network_storage_power'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.network_storage_power),unit:\"EiB\",variant:\"success\",loading:loading,icon:\"\\uD83D\\uDCBE\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('active_miners'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.active_miners),variant:\"info\",loading:loading,icon:\"\\u26CF\\uFE0F\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('block_reward'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.block_reward),unit:\"FIL\",variant:\"warning\",loading:loading,icon:\"\\uD83C\\uDF81\"})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('mining_reward_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.fil_per_tib),unit:\"FIL/TiB\",variant:\"secondary\",loading:loading,icon:\"\\u26A1\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('fil_production_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.fil_production_24h),unit:\"FIL\",variant:\"dark\",loading:loading,icon:\"\\uD83C\\uDFED\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('total_pledge_collateral'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.total_pledge_collateral),unit:\"FIL\",variant:\"danger\",loading:loading,icon:\"\\uD83D\\uDD12\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('messages_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.messages_24h),variant:\"light\",loading:loading,icon:\"\\uD83D\\uDCE8\"})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(StatCard,{title:t('sector_initial_pledge'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.sector_initial_pledge),unit:\"FIL/32GiB\",variant:\"primary\",loading:loading,icon:\"\\uD83D\\uDD10\"})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(StatCard,{title:t('latest_block'),value:(currentStats===null||currentStats===void 0?void 0:currentStats.latest_block)||'N/A',variant:\"info\",loading:loading,icon:\"\\u23F0\"})})]}),historicalData.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('mining_reward_trend')}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:300,children:/*#__PURE__*/_jsxs(LineChart,{data:historicalData,children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"stat_date\",tickFormatter:formatDate}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{labelFormatter:formatDate,formatter:value=>[formatNumber(value),'FIL/TiB']}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"fil_per_tib\",stroke:\"#8884d8\",name:t('mining_reward')})]})})]})})})}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('network_storage_trend')}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:300,children:/*#__PURE__*/_jsxs(LineChart,{data:historicalData,children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"stat_date\",tickFormatter:formatDate}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{labelFormatter:formatDate,formatter:value=>[formatNumber(value),'EiB']}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"network_storage_power\",stroke:\"#82ca9d\",name:t('storage_power')})]})})]})})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('active_miners_trend')}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:300,children:/*#__PURE__*/_jsxs(BarChart,{data:historicalData,children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"stat_date\",tickFormatter:formatDate}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{labelFormatter:formatDate,formatter:value=>[formatNumber(value),t('miners')]}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"active_miners\",fill:\"#ffc658\",name:t('active_miners')})]})})]})})})]})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('recent_network_data')}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:t('loading')})]}):/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('date')}),/*#__PURE__*/_jsx(\"th\",{children:t('block_height')}),/*#__PURE__*/_jsx(\"th\",{children:t('storage_power')}),/*#__PURE__*/_jsx(\"th\",{children:t('active_miners')}),/*#__PURE__*/_jsx(\"th\",{children:t('mining_reward')}),/*#__PURE__*/_jsx(\"th\",{children:t('fil_production')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:historicalData.slice(-10).reverse().map((stat,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:formatDate(stat.stat_date)}),/*#__PURE__*/_jsx(\"td\",{children:formatNumber(stat.block_height)}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(stat.network_storage_power),\" EiB\"]}),/*#__PURE__*/_jsx(\"td\",{children:formatNumber(stat.active_miners)}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(stat.fil_per_tib),\" FIL/TiB\"]}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(stat.fil_production_24h),\" FIL\"]})]},`${stat.stat_date}-${index}`))})]})]})})})})]});};export default Filfox;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Row", "Col", "Card", "Table", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON>", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Bar", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "StatCard", "_ref", "title", "value", "unit", "variant", "loading", "icon", "className", "children", "Body", "Title", "animation", "size", "Filfox", "t", "setLoading", "error", "setError", "currentStats", "setCurrentStats", "historicalData", "setHistoricalData", "refreshing", "setRefreshing", "fetchNetworkStats", "supabase", "data", "user", "auth", "getUser", "latestData", "latestError", "from", "select", "order", "ascending", "limit", "console", "length", "historicalError", "reverse", "handleRefresh", "formatNumber", "num", "undefined", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "fluid", "onClick", "disabled", "md", "block_height", "network_storage_power", "active_miners", "block_reward", "fil_per_tib", "fil_production_24h", "total_pledge_collateral", "messages_24h", "sector_initial_pledge", "latest_block", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick<PERSON><PERSON><PERSON><PERSON>", "labelFormatter", "formatter", "type", "stroke", "name", "fill", "striped", "bordered", "hover", "responsive", "slice", "map", "stat", "index", "stat_date"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/Filfox.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, Table, Spinner, Al<PERSON>, Button } from 'react-bootstrap';\nimport { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Bar } from 'recharts';\nimport { getSupabase } from '../../supabaseClient';\n\nconst StatCard = ({ title, value, unit, variant, loading, icon }) => (\n    <Card className={`bg-${variant} text-white mb-3 h-100`}>\n        <Card.Body className=\"d-flex flex-column justify-content-between\">\n            <div className=\"d-flex justify-content-between align-items-start\">\n                <div>\n                    <Card.Title className=\"h6\">{title}</Card.Title>\n                    {loading ? (\n                        <div className=\"d-flex align-items-center\">\n                            <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                            <span>Loading...</span>\n                        </div>\n                    ) : (\n                        <div>\n                            <h4 className=\"mb-0\">{value}</h4>\n                            {unit && <small className=\"opacity-75\">{unit}</small>}\n                        </div>\n                    )}\n                </div>\n                {icon && <div className=\"fs-2 opacity-50\">{icon}</div>}\n            </div>\n        </Card.Body>\n    </Card>\n);\n\nconst Filfox = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [currentStats, setCurrentStats] = useState(null);\n    const [historicalData, setHistoricalData] = useState([]);\n    const [refreshing, setRefreshing] = useState(false);\n\n    // Fetch current and historical network stats\n    const fetchNetworkStats = async () => {\n        const supabase = getSupabase();\n        if (!supabase) {\n            setError('Failed to initialize Supabase client');\n            setLoading(false);\n            return;\n        }\n\n        try {\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setError('User not logged in');\n                setLoading(false);\n                return;\n            }\n\n            // Fetch latest network stats\n            const { data: latestData, error: latestError } = await supabase\n                .from('network_stats')\n                .select('*')\n                .order('stat_date', { ascending: false })\n                .limit(1);\n\n            if (latestError) {\n                console.error('Error fetching latest stats:', latestError);\n                setError('Failed to fetch latest network statistics');\n                return;\n            }\n\n            if (latestData && latestData.length > 0) {\n                setCurrentStats(latestData[0]);\n            }\n\n            // Fetch historical data for charts (last 30 days)\n            const { data: historicalData, error: historicalError } = await supabase\n                .from('network_stats')\n                .select('*')\n                .order('stat_date', { ascending: false })\n                .limit(30);\n\n            if (historicalError) {\n                console.error('Error fetching historical stats:', historicalError);\n            } else {\n                // Reverse to show chronological order in charts\n                setHistoricalData(historicalData.reverse());\n            }\n\n        } catch (error) {\n            console.error('Error fetching network stats:', error);\n            setError('Failed to load network statistics');\n        } finally {\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n\n    useEffect(() => {\n        fetchNetworkStats();\n    }, []);\n\n    const handleRefresh = () => {\n        setRefreshing(true);\n        fetchNetworkStats();\n    };\n\n    const formatNumber = (num) => {\n        if (num === null || num === undefined) return 'N/A';\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 4\n        }).format(num);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString();\n    };\n\n    if (error) {\n        return (\n            <Container fluid>\n                <Row className=\"mb-3\">\n                    <Col>\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Alert variant=\"danger\">{error}</Alert>\n                    </Col>\n                </Row>\n            </Container>\n        );\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Button \n                            variant=\"outline-primary\" \n                            onClick={handleRefresh}\n                            disabled={refreshing}\n                        >\n                            {refreshing ? (\n                                <>\n                                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                                    {t('refreshing')}\n                                </>\n                            ) : (\n                                t('refresh')\n                            )}\n                        </Button>\n                    </div>\n                </Col>\n            </Row>\n\n            {/* Current Statistics Cards */}\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_height')}\n                        value={formatNumber(currentStats?.block_height)}\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔗\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('network_storage_power')}\n                        value={formatNumber(currentStats?.network_storage_power)}\n                        unit=\"EiB\"\n                        variant=\"success\"\n                        loading={loading}\n                        icon=\"💾\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('active_miners')}\n                        value={formatNumber(currentStats?.active_miners)}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⛏️\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_reward')}\n                        value={formatNumber(currentStats?.block_reward)}\n                        unit=\"FIL\"\n                        variant=\"warning\"\n                        loading={loading}\n                        icon=\"🎁\"\n                    />\n                </Col>\n            </Row>\n\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('mining_reward_24h')}\n                        value={formatNumber(currentStats?.fil_per_tib)}\n                        unit=\"FIL/TiB\"\n                        variant=\"secondary\"\n                        loading={loading}\n                        icon=\"⚡\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('fil_production_24h')}\n                        value={formatNumber(currentStats?.fil_production_24h)}\n                        unit=\"FIL\"\n                        variant=\"dark\"\n                        loading={loading}\n                        icon=\"🏭\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('total_pledge_collateral')}\n                        value={formatNumber(currentStats?.total_pledge_collateral)}\n                        unit=\"FIL\"\n                        variant=\"danger\"\n                        loading={loading}\n                        icon=\"🔒\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('messages_24h')}\n                        value={formatNumber(currentStats?.messages_24h)}\n                        variant=\"light\"\n                        loading={loading}\n                        icon=\"📨\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Additional Stats */}\n            <Row className=\"mb-4\">\n                <Col md={6}>\n                    <StatCard\n                        title={t('sector_initial_pledge')}\n                        value={formatNumber(currentStats?.sector_initial_pledge)}\n                        unit=\"FIL/32GiB\"\n                        variant=\"primary\"\n                        loading={loading}\n                        icon=\"🔐\"\n                    />\n                </Col>\n                <Col md={6}>\n                    <StatCard\n                        title={t('latest_block')}\n                        value={currentStats?.latest_block || 'N/A'}\n                        variant=\"info\"\n                        loading={loading}\n                        icon=\"⏰\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Historical Charts */}\n            {historicalData.length > 0 && (\n                <>\n                    <Row className=\"mb-4\">\n                        <Col>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('mining_reward_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <LineChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), 'FIL/TiB']}\n                                            />\n                                            <Legend />\n                                            <Line \n                                                type=\"monotone\" \n                                                dataKey=\"fil_per_tib\" \n                                                stroke=\"#8884d8\" \n                                                name={t('mining_reward')}\n                                            />\n                                        </LineChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n\n                    <Row className=\"mb-4\">\n                        <Col md={6}>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('network_storage_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <LineChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), 'EiB']}\n                                            />\n                                            <Legend />\n                                            <Line \n                                                type=\"monotone\" \n                                                dataKey=\"network_storage_power\" \n                                                stroke=\"#82ca9d\" \n                                                name={t('storage_power')}\n                                            />\n                                        </LineChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                        <Col md={6}>\n                            <Card>\n                                <Card.Body>\n                                    <Card.Title>{t('active_miners_trend')}</Card.Title>\n                                    <ResponsiveContainer width=\"100%\" height={300}>\n                                        <BarChart data={historicalData}>\n                                            <CartesianGrid strokeDasharray=\"3 3\" />\n                                            <XAxis \n                                                dataKey=\"stat_date\" \n                                                tickFormatter={formatDate}\n                                            />\n                                            <YAxis />\n                                            <Tooltip \n                                                labelFormatter={formatDate}\n                                                formatter={(value) => [formatNumber(value), t('miners')]}\n                                            />\n                                            <Legend />\n                                            <Bar \n                                                dataKey=\"active_miners\" \n                                                fill=\"#ffc658\" \n                                                name={t('active_miners')}\n                                            />\n                                        </BarChart>\n                                    </ResponsiveContainer>\n                                </Card.Body>\n                            </Card>\n                        </Col>\n                    </Row>\n                </>\n            )}\n\n            {/* Data Table */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('recent_network_data')}</Card.Title>\n                            {loading ? (\n                                <div className=\"text-center\">\n                                    <Spinner animation=\"border\" />\n                                    <p className=\"mt-2\">{t('loading')}</p>\n                                </div>\n                            ) : (\n                                <Table striped bordered hover responsive>\n                                    <thead>\n                                        <tr>\n                                            <th>{t('date')}</th>\n                                            <th>{t('block_height')}</th>\n                                            <th>{t('storage_power')}</th>\n                                            <th>{t('active_miners')}</th>\n                                            <th>{t('mining_reward')}</th>\n                                            <th>{t('fil_production')}</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        {historicalData.slice(-10).reverse().map((stat, index) => (\n                                            <tr key={`${stat.stat_date}-${index}`}>\n                                                <td>{formatDate(stat.stat_date)}</td>\n                                                <td>{formatNumber(stat.block_height)}</td>\n                                                <td>{formatNumber(stat.network_storage_power)} EiB</td>\n                                                <td>{formatNumber(stat.active_miners)}</td>\n                                                <td>{formatNumber(stat.fil_per_tib)} FIL/TiB</td>\n                                                <td>{formatNumber(stat.fil_production_24h)} FIL</td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </Table>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Filfox;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,KAAK,CAAEC,MAAM,KAAQ,iBAAiB,CAC1F,OAASC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,aAAa,CAAEC,OAAO,CAAEC,MAAM,CAAEC,mBAAmB,CAAEC,QAAQ,CAAEC,GAAG,KAAQ,UAAU,CAC5H,OAASC,WAAW,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnD,KAAM,CAAAC,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,IAAI,CAAEC,OAAO,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAAN,IAAA,oBAC5DN,IAAA,CAACjB,IAAI,EAAC8B,SAAS,CAAE,MAAMH,OAAO,wBAAyB,CAAAI,QAAA,cACnDd,IAAA,CAACjB,IAAI,CAACgC,IAAI,EAACF,SAAS,CAAC,4CAA4C,CAAAC,QAAA,cAC7DZ,KAAA,QAAKW,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC7DZ,KAAA,QAAAY,QAAA,eACId,IAAA,CAACjB,IAAI,CAACiC,KAAK,EAACH,SAAS,CAAC,IAAI,CAAAC,QAAA,CAAEP,KAAK,CAAa,CAAC,CAC9CI,OAAO,cACJT,KAAA,QAAKW,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACtCd,IAAA,CAACf,OAAO,EAACgC,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACL,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDb,IAAA,SAAAc,QAAA,CAAM,YAAU,CAAM,CAAC,EACtB,CAAC,cAENZ,KAAA,QAAAY,QAAA,eACId,IAAA,OAAIa,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEN,KAAK,CAAK,CAAC,CAChCC,IAAI,eAAIT,IAAA,UAAOa,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEL,IAAI,CAAQ,CAAC,EACpD,CACR,EACA,CAAC,CACLG,IAAI,eAAIZ,IAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEF,IAAI,CAAM,CAAC,EACrD,CAAC,CACC,CAAC,CACV,CAAC,EACV,CAED,KAAM,CAAAO,MAAM,CAAGA,CAAA,GAAM,CACjB,KAAM,CAAEC,CAAE,CAAC,CAAGzC,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACgC,OAAO,CAAEU,UAAU,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6C,KAAK,CAAEC,QAAQ,CAAC,CAAG9C,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAC+C,YAAY,CAAEC,eAAe,CAAC,CAAGhD,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACmD,UAAU,CAAEC,aAAa,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAAAqD,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,KAAM,CAAAC,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACiC,QAAQ,CAAE,CACXR,QAAQ,CAAC,sCAAsC,CAAC,CAChDF,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA,GAAI,CACAA,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEW,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPV,QAAQ,CAAC,oBAAoB,CAAC,CAC9BF,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEW,IAAI,CAAEI,UAAU,CAAEd,KAAK,CAAEe,WAAY,CAAC,CAAG,KAAM,CAAAN,QAAQ,CAC1DO,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CACxCC,KAAK,CAAC,CAAC,CAAC,CAEb,GAAIL,WAAW,CAAE,CACbM,OAAO,CAACrB,KAAK,CAAC,8BAA8B,CAAEe,WAAW,CAAC,CAC1Dd,QAAQ,CAAC,2CAA2C,CAAC,CACrD,OACJ,CAEA,GAAIa,UAAU,EAAIA,UAAU,CAACQ,MAAM,CAAG,CAAC,CAAE,CACrCnB,eAAe,CAACW,UAAU,CAAC,CAAC,CAAC,CAAC,CAClC,CAEA;AACA,KAAM,CAAEJ,IAAI,CAAEN,cAAc,CAAEJ,KAAK,CAAEuB,eAAgB,CAAC,CAAG,KAAM,CAAAd,QAAQ,CAClEO,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CACxCC,KAAK,CAAC,EAAE,CAAC,CAEd,GAAIG,eAAe,CAAE,CACjBF,OAAO,CAACrB,KAAK,CAAC,kCAAkC,CAAEuB,eAAe,CAAC,CACtE,CAAC,IAAM,CACH;AACAlB,iBAAiB,CAACD,cAAc,CAACoB,OAAO,CAAC,CAAC,CAAC,CAC/C,CAEJ,CAAE,MAAOxB,KAAK,CAAE,CACZqB,OAAO,CAACrB,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrDC,QAAQ,CAAC,mCAAmC,CAAC,CACjD,CAAC,OAAS,CACNF,UAAU,CAAC,KAAK,CAAC,CACjBQ,aAAa,CAAC,KAAK,CAAC,CACxB,CACJ,CAAC,CAEDnD,SAAS,CAAC,IAAM,CACZoD,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAiB,aAAa,CAAGA,CAAA,GAAM,CACxBlB,aAAa,CAAC,IAAI,CAAC,CACnBC,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAkB,YAAY,CAAIC,GAAG,EAAK,CAC1B,GAAIA,GAAG,GAAK,IAAI,EAAIA,GAAG,GAAKC,SAAS,CAAE,MAAO,KAAK,CACnD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CAClCC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CAC3B,CAAC,CAAC,CAACC,MAAM,CAACN,GAAG,CAAC,CAClB,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CAC/B,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CACpD,CAAC,CAED,GAAIrC,KAAK,CAAE,CACP,mBACItB,IAAA,CAACpB,SAAS,EAACgF,KAAK,MAAA9C,QAAA,cACZd,IAAA,CAACnB,GAAG,EAACgC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBZ,KAAA,CAACpB,GAAG,EAAAgC,QAAA,eACAd,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,cACpCpB,IAAA,CAACd,KAAK,EAACwB,OAAO,CAAC,QAAQ,CAAAI,QAAA,CAAEQ,KAAK,CAAQ,CAAC,EACtC,CAAC,CACL,CAAC,CACC,CAAC,CAEpB,CAEA,mBACIpB,KAAA,CAACtB,SAAS,EAACgF,KAAK,MAAA9C,QAAA,eACZd,IAAA,CAACnB,GAAG,EAACgC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBd,IAAA,CAAClB,GAAG,EAAAgC,QAAA,cACAZ,KAAA,QAAKW,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC9Dd,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,cACpCpB,IAAA,CAACb,MAAM,EACHuB,OAAO,CAAC,iBAAiB,CACzBmD,OAAO,CAAEd,aAAc,CACvBe,QAAQ,CAAElC,UAAW,CAAAd,QAAA,CAEpBc,UAAU,cACP1B,KAAA,CAAAE,SAAA,EAAAU,QAAA,eACId,IAAA,CAACf,OAAO,EAACgC,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACL,SAAS,CAAC,MAAM,CAAE,CAAC,CACxDO,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,CAEHA,CAAC,CAAC,SAAS,CACd,CACG,CAAC,EACR,CAAC,CACL,CAAC,CACL,CAAC,cAGNlB,KAAA,CAACrB,GAAG,EAACgC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBd,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,cAAc,CAAE,CACzBZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEwC,YAAY,CAAE,CAChDtD,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,uBAAuB,CAAE,CAClCZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEyC,qBAAqB,CAAE,CACzDxD,IAAI,CAAC,KAAK,CACVC,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,eAAe,CAAE,CAC1BZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE0C,aAAa,CAAE,CACjDxD,OAAO,CAAC,MAAM,CACdC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,cAAc,CAAE,CACzBZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE2C,YAAY,CAAE,CAChD1D,IAAI,CAAC,KAAK,CACVC,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,EACL,CAAC,cAENV,KAAA,CAACrB,GAAG,EAACgC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBd,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,mBAAmB,CAAE,CAC9BZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE4C,WAAW,CAAE,CAC/C3D,IAAI,CAAC,SAAS,CACdC,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,QAAG,CACX,CAAC,CACD,CAAC,cACNZ,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,oBAAoB,CAAE,CAC/BZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE6C,kBAAkB,CAAE,CACtD5D,IAAI,CAAC,KAAK,CACVC,OAAO,CAAC,MAAM,CACdC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,yBAAyB,CAAE,CACpCZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE8C,uBAAuB,CAAE,CAC3D7D,IAAI,CAAC,KAAK,CACVC,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,cAAc,CAAE,CACzBZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE+C,YAAY,CAAE,CAChD7D,OAAO,CAAC,OAAO,CACfC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,EACL,CAAC,cAGNV,KAAA,CAACrB,GAAG,EAACgC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBd,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,uBAAuB,CAAE,CAClCZ,KAAK,CAAEwC,YAAY,CAACxB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEgD,qBAAqB,CAAE,CACzD/D,IAAI,CAAC,WAAW,CAChBC,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNZ,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEa,CAAC,CAAC,cAAc,CAAE,CACzBZ,KAAK,CAAE,CAAAgB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiD,YAAY,GAAI,KAAM,CAC3C/D,OAAO,CAAC,MAAM,CACdC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,QAAG,CACX,CAAC,CACD,CAAC,EACL,CAAC,CAGLc,cAAc,CAACkB,MAAM,CAAG,CAAC,eACtB1C,KAAA,CAAAE,SAAA,EAAAU,QAAA,eACId,IAAA,CAACnB,GAAG,EAACgC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBd,IAAA,CAAClB,GAAG,EAAAgC,QAAA,cACAd,IAAA,CAACjB,IAAI,EAAA+B,QAAA,cACDZ,KAAA,CAACnB,IAAI,CAACgC,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACjB,IAAI,CAACiC,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,qBAAqB,CAAC,CAAa,CAAC,cACnDpB,IAAA,CAACL,mBAAmB,EAAC+E,KAAK,CAAC,MAAM,CAACC,MAAM,CAAE,GAAI,CAAA7D,QAAA,cAC1CZ,KAAA,CAACd,SAAS,EAAC4C,IAAI,CAAEN,cAAe,CAAAZ,QAAA,eAC5Bd,IAAA,CAACR,aAAa,EAACoF,eAAe,CAAC,KAAK,CAAE,CAAC,cACvC5E,IAAA,CAACV,KAAK,EACFuF,OAAO,CAAC,WAAW,CACnBC,aAAa,CAAEtB,UAAW,CAC7B,CAAC,cACFxD,IAAA,CAACT,KAAK,GAAE,CAAC,cACTS,IAAA,CAACP,OAAO,EACJsF,cAAc,CAAEvB,UAAW,CAC3BwB,SAAS,CAAGxE,KAAK,EAAK,CAACwC,YAAY,CAACxC,KAAK,CAAC,CAAE,SAAS,CAAE,CAC1D,CAAC,cACFR,IAAA,CAACN,MAAM,GAAE,CAAC,cACVM,IAAA,CAACX,IAAI,EACD4F,IAAI,CAAC,UAAU,CACfJ,OAAO,CAAC,aAAa,CACrBK,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAE/D,CAAC,CAAC,eAAe,CAAE,CAC5B,CAAC,EACK,CAAC,CACK,CAAC,EACf,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAENlB,KAAA,CAACrB,GAAG,EAACgC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBd,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACjB,IAAI,EAAA+B,QAAA,cACDZ,KAAA,CAACnB,IAAI,CAACgC,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACjB,IAAI,CAACiC,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,uBAAuB,CAAC,CAAa,CAAC,cACrDpB,IAAA,CAACL,mBAAmB,EAAC+E,KAAK,CAAC,MAAM,CAACC,MAAM,CAAE,GAAI,CAAA7D,QAAA,cAC1CZ,KAAA,CAACd,SAAS,EAAC4C,IAAI,CAAEN,cAAe,CAAAZ,QAAA,eAC5Bd,IAAA,CAACR,aAAa,EAACoF,eAAe,CAAC,KAAK,CAAE,CAAC,cACvC5E,IAAA,CAACV,KAAK,EACFuF,OAAO,CAAC,WAAW,CACnBC,aAAa,CAAEtB,UAAW,CAC7B,CAAC,cACFxD,IAAA,CAACT,KAAK,GAAE,CAAC,cACTS,IAAA,CAACP,OAAO,EACJsF,cAAc,CAAEvB,UAAW,CAC3BwB,SAAS,CAAGxE,KAAK,EAAK,CAACwC,YAAY,CAACxC,KAAK,CAAC,CAAE,KAAK,CAAE,CACtD,CAAC,cACFR,IAAA,CAACN,MAAM,GAAE,CAAC,cACVM,IAAA,CAACX,IAAI,EACD4F,IAAI,CAAC,UAAU,CACfJ,OAAO,CAAC,uBAAuB,CAC/BK,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAE/D,CAAC,CAAC,eAAe,CAAE,CAC5B,CAAC,EACK,CAAC,CACK,CAAC,EACf,CAAC,CACV,CAAC,CACN,CAAC,cACNpB,IAAA,CAAClB,GAAG,EAACiF,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACPd,IAAA,CAACjB,IAAI,EAAA+B,QAAA,cACDZ,KAAA,CAACnB,IAAI,CAACgC,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACjB,IAAI,CAACiC,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,qBAAqB,CAAC,CAAa,CAAC,cACnDpB,IAAA,CAACL,mBAAmB,EAAC+E,KAAK,CAAC,MAAM,CAACC,MAAM,CAAE,GAAI,CAAA7D,QAAA,cAC1CZ,KAAA,CAACN,QAAQ,EAACoC,IAAI,CAAEN,cAAe,CAAAZ,QAAA,eAC3Bd,IAAA,CAACR,aAAa,EAACoF,eAAe,CAAC,KAAK,CAAE,CAAC,cACvC5E,IAAA,CAACV,KAAK,EACFuF,OAAO,CAAC,WAAW,CACnBC,aAAa,CAAEtB,UAAW,CAC7B,CAAC,cACFxD,IAAA,CAACT,KAAK,GAAE,CAAC,cACTS,IAAA,CAACP,OAAO,EACJsF,cAAc,CAAEvB,UAAW,CAC3BwB,SAAS,CAAGxE,KAAK,EAAK,CAACwC,YAAY,CAACxC,KAAK,CAAC,CAAEY,CAAC,CAAC,QAAQ,CAAC,CAAE,CAC5D,CAAC,cACFpB,IAAA,CAACN,MAAM,GAAE,CAAC,cACVM,IAAA,CAACH,GAAG,EACAgF,OAAO,CAAC,eAAe,CACvBO,IAAI,CAAC,SAAS,CACdD,IAAI,CAAE/D,CAAC,CAAC,eAAe,CAAE,CAC5B,CAAC,EACI,CAAC,CACM,CAAC,EACf,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,EACR,CACL,cAGDpB,IAAA,CAACnB,GAAG,EAAAiC,QAAA,cACAd,IAAA,CAAClB,GAAG,EAAAgC,QAAA,cACAd,IAAA,CAACjB,IAAI,EAAA+B,QAAA,cACDZ,KAAA,CAACnB,IAAI,CAACgC,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACjB,IAAI,CAACiC,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,qBAAqB,CAAC,CAAa,CAAC,CAClDT,OAAO,cACJT,KAAA,QAAKW,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBd,IAAA,CAACf,OAAO,EAACgC,SAAS,CAAC,QAAQ,CAAE,CAAC,cAC9BjB,IAAA,MAAGa,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEM,CAAC,CAAC,SAAS,CAAC,CAAI,CAAC,EACrC,CAAC,cAENlB,KAAA,CAAClB,KAAK,EAACqG,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAA1E,QAAA,eACpCd,IAAA,UAAAc,QAAA,cACIZ,KAAA,OAAAY,QAAA,eACId,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,MAAM,CAAC,CAAK,CAAC,cACpBpB,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BpB,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BpB,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BpB,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BpB,IAAA,OAAAc,QAAA,CAAKM,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,EAC9B,CAAC,CACF,CAAC,cACRpB,IAAA,UAAAc,QAAA,CACKY,cAAc,CAAC+D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC3C,OAAO,CAAC,CAAC,CAAC4C,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACjD1F,KAAA,OAAAY,QAAA,eACId,IAAA,OAAAc,QAAA,CAAK0C,UAAU,CAACmC,IAAI,CAACE,SAAS,CAAC,CAAK,CAAC,cACrC7F,IAAA,OAAAc,QAAA,CAAKkC,YAAY,CAAC2C,IAAI,CAAC3B,YAAY,CAAC,CAAK,CAAC,cAC1C9D,KAAA,OAAAY,QAAA,EAAKkC,YAAY,CAAC2C,IAAI,CAAC1B,qBAAqB,CAAC,CAAC,MAAI,EAAI,CAAC,cACvDjE,IAAA,OAAAc,QAAA,CAAKkC,YAAY,CAAC2C,IAAI,CAACzB,aAAa,CAAC,CAAK,CAAC,cAC3ChE,KAAA,OAAAY,QAAA,EAAKkC,YAAY,CAAC2C,IAAI,CAACvB,WAAW,CAAC,CAAC,UAAQ,EAAI,CAAC,cACjDlE,KAAA,OAAAY,QAAA,EAAKkC,YAAY,CAAC2C,IAAI,CAACtB,kBAAkB,CAAC,CAAC,MAAI,EAAI,CAAC,GAN/C,GAAGsB,IAAI,CAACE,SAAS,IAAID,KAAK,EAO/B,CACP,CAAC,CACC,CAAC,EACL,CACV,EACM,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAzE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}