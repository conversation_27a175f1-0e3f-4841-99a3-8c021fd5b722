import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Row, Col, Card, Table, Spinner, Al<PERSON>, Button } from 'react-bootstrap';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Bar } from 'recharts';
import { getSupabase } from '../../supabaseClient';

const StatCard = ({ title, value, unit, variant, loading, icon }) => (
    <Card className={`bg-${variant} text-white mb-3 h-100`}>
        <Card.Body className="d-flex flex-column justify-content-between">
            <div className="d-flex justify-content-between align-items-start">
                <div>
                    <Card.Title className="h6">{title}</Card.Title>
                    {loading ? (
                        <div className="d-flex align-items-center">
                            <Spinner animation="border" size="sm" className="me-2" />
                            <span>Loading...</span>
                        </div>
                    ) : (
                        <div>
                            <h4 className="mb-0">{value}</h4>
                            {unit && <small className="opacity-75">{unit}</small>}
                        </div>
                    )}
                </div>
                {icon && <div className="fs-2 opacity-50">{icon}</div>}
            </div>
        </Card.Body>
    </Card>
);

const Filfox = () => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentStats, setCurrentStats] = useState(null);
    const [historicalData, setHistoricalData] = useState([]);
    const [refreshing, setRefreshing] = useState(false);

    // Fetch current and historical network stats
    const fetchNetworkStats = async () => {
        const supabase = getSupabase();
        if (!supabase) {
            setError('Failed to initialize Supabase client');
            setLoading(false);
            return;
        }

        try {
            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setError('User not logged in');
                setLoading(false);
                return;
            }

            // Fetch latest network stats
            const { data: latestData, error: latestError } = await supabase
                .from('network_stats')
                .select('*')
                .order('stat_date', { ascending: false })
                .limit(1);

            if (latestError) {
                console.error('Error fetching latest stats:', latestError);
                setError('Failed to fetch latest network statistics');
                return;
            }

            if (latestData && latestData.length > 0) {
                setCurrentStats(latestData[0]);
            }

            // Fetch historical data for charts (last 30 days)
            const { data: historicalData, error: historicalError } = await supabase
                .from('network_stats')
                .select('*')
                .order('stat_date', { ascending: false })
                .limit(30);

            if (historicalError) {
                console.error('Error fetching historical stats:', historicalError);
            } else {
                // Reverse to show chronological order in charts
                setHistoricalData(historicalData.reverse());
            }

        } catch (error) {
            console.error('Error fetching network stats:', error);
            setError('Failed to load network statistics');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useEffect(() => {
        fetchNetworkStats();
    }, []);

    const handleRefresh = () => {
        setRefreshing(true);
        fetchNetworkStats();
    };

    const formatNumber = (num) => {
        if (num === null || num === undefined) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 4
        }).format(num);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
    };

    if (error) {
        return (
            <Container fluid>
                <Row className="mb-3">
                    <Col>
                        <h2>{t('filfox_network_stats')}</h2>
                        <Alert variant="danger">{error}</Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>{t('filfox_network_stats')}</h2>
                        <Button 
                            variant="outline-primary" 
                            onClick={handleRefresh}
                            disabled={refreshing}
                        >
                            {refreshing ? (
                                <>
                                    <Spinner animation="border" size="sm" className="me-2" />
                                    {t('refreshing')}
                                </>
                            ) : (
                                t('refresh')
                            )}
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Current Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <StatCard
                        title={t('block_height')}
                        value={formatNumber(currentStats?.block_height)}
                        variant="primary"
                        loading={loading}
                        icon="🔗"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('network_storage_power')}
                        value={formatNumber(currentStats?.network_storage_power)}
                        unit="EiB"
                        variant="success"
                        loading={loading}
                        icon="💾"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('active_miners')}
                        value={formatNumber(currentStats?.active_miners)}
                        variant="info"
                        loading={loading}
                        icon="⛏️"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('block_reward')}
                        value={formatNumber(currentStats?.block_reward)}
                        unit="FIL"
                        variant="warning"
                        loading={loading}
                        icon="🎁"
                    />
                </Col>
            </Row>

            <Row className="mb-4">
                <Col md={3}>
                    <StatCard
                        title={t('mining_reward_24h')}
                        value={formatNumber(currentStats?.fil_per_tib)}
                        unit="FIL/TiB"
                        variant="secondary"
                        loading={loading}
                        icon="⚡"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('fil_production_24h')}
                        value={formatNumber(currentStats?.fil_production_24h)}
                        unit="FIL"
                        variant="dark"
                        loading={loading}
                        icon="🏭"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('total_pledge_collateral')}
                        value={formatNumber(currentStats?.total_pledge_collateral)}
                        unit="FIL"
                        variant="danger"
                        loading={loading}
                        icon="🔒"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('messages_24h')}
                        value={formatNumber(currentStats?.messages_24h)}
                        variant="light"
                        loading={loading}
                        icon="📨"
                    />
                </Col>
            </Row>

            {/* Additional Stats */}
            <Row className="mb-4">
                <Col md={6}>
                    <StatCard
                        title={t('sector_initial_pledge')}
                        value={formatNumber(currentStats?.sector_initial_pledge)}
                        unit="FIL/32GiB"
                        variant="primary"
                        loading={loading}
                        icon="🔐"
                    />
                </Col>
                <Col md={6}>
                    <StatCard
                        title={t('latest_block')}
                        value={currentStats?.latest_block || 'N/A'}
                        variant="info"
                        loading={loading}
                        icon="⏰"
                    />
                </Col>
            </Row>

            {/* Historical Charts */}
            {historicalData.length > 0 && (
                <>
                    <Row className="mb-4">
                        <Col>
                            <Card>
                                <Card.Body>
                                    <Card.Title>{t('mining_reward_trend')}</Card.Title>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <LineChart data={historicalData}>
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis 
                                                dataKey="stat_date" 
                                                tickFormatter={formatDate}
                                            />
                                            <YAxis />
                                            <Tooltip 
                                                labelFormatter={formatDate}
                                                formatter={(value) => [formatNumber(value), 'FIL/TiB']}
                                            />
                                            <Legend />
                                            <Line 
                                                type="monotone" 
                                                dataKey="fil_per_tib" 
                                                stroke="#8884d8" 
                                                name={t('mining_reward')}
                                            />
                                        </LineChart>
                                    </ResponsiveContainer>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>

                    <Row className="mb-4">
                        <Col md={6}>
                            <Card>
                                <Card.Body>
                                    <Card.Title>{t('network_storage_trend')}</Card.Title>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <LineChart data={historicalData}>
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis 
                                                dataKey="stat_date" 
                                                tickFormatter={formatDate}
                                            />
                                            <YAxis />
                                            <Tooltip 
                                                labelFormatter={formatDate}
                                                formatter={(value) => [formatNumber(value), 'EiB']}
                                            />
                                            <Legend />
                                            <Line 
                                                type="monotone" 
                                                dataKey="network_storage_power" 
                                                stroke="#82ca9d" 
                                                name={t('storage_power')}
                                            />
                                        </LineChart>
                                    </ResponsiveContainer>
                                </Card.Body>
                            </Card>
                        </Col>
                        <Col md={6}>
                            <Card>
                                <Card.Body>
                                    <Card.Title>{t('active_miners_trend')}</Card.Title>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <BarChart data={historicalData}>
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis 
                                                dataKey="stat_date" 
                                                tickFormatter={formatDate}
                                            />
                                            <YAxis />
                                            <Tooltip 
                                                labelFormatter={formatDate}
                                                formatter={(value) => [formatNumber(value), t('miners')]}
                                            />
                                            <Legend />
                                            <Bar 
                                                dataKey="active_miners" 
                                                fill="#ffc658" 
                                                name={t('active_miners')}
                                            />
                                        </BarChart>
                                    </ResponsiveContainer>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </>
            )}

            {/* Data Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Card.Title>{t('recent_network_data')}</Card.Title>
                            {loading ? (
                                <div className="text-center">
                                    <Spinner animation="border" />
                                    <p className="mt-2">{t('loading')}</p>
                                </div>
                            ) : (
                                <Table striped bordered hover responsive>
                                    <thead>
                                        <tr>
                                            <th>{t('date')}</th>
                                            <th>{t('block_height')}</th>
                                            <th>{t('storage_power')}</th>
                                            <th>{t('active_miners')}</th>
                                            <th>{t('mining_reward')}</th>
                                            <th>{t('fil_production')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {historicalData.slice(-10).reverse().map((stat, index) => (
                                            <tr key={`${stat.stat_date}-${index}`}>
                                                <td>{formatDate(stat.stat_date)}</td>
                                                <td>{formatNumber(stat.block_height)}</td>
                                                <td>{formatNumber(stat.network_storage_power)} EiB</td>
                                                <td>{formatNumber(stat.active_miners)}</td>
                                                <td>{formatNumber(stat.fil_per_tib)} FIL/TiB</td>
                                                <td>{formatNumber(stat.fil_production_24h)} FIL</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default Filfox;
