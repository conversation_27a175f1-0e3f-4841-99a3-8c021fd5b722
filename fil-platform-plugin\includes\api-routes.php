<?php
// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

// This file will now be much simpler.
class FIL_Platform_API {

    public function __construct() {
        add_action('rest_api_init', [$this, 'register_routes']);
    }

    public function register_routes() {
        register_rest_route('fil-platform/v1', '/config', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_supabase_config'],
                'permission_callback' => '__return_true', // Publicly accessible
            ],
        ]);

        // Add manual scraper trigger endpoint
        register_rest_route('fil-platform/v1', '/scrape-filfox', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'manual_scrape_filfox'],
                'permission_callback' => [$this, 'check_admin_permission'],
            ],
        ]);

        // Add scraper status endpoint
        register_rest_route('fil-platform/v1', '/scraper-status', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_scraper_status'],
                'permission_callback' => [$this, 'check_admin_permission'],
            ],
        ]);

        // Add real-time filfox data endpoint
        register_rest_route('fil-platform/v1', '/filfox-realtime', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_filfox_realtime_data'],
                'permission_callback' => '__return_true', // Allow public access for now
            ],
        ]);

        // Add test endpoint
        register_rest_route('fil-platform/v1', '/test', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'test_endpoint'],
                'permission_callback' => '__return_true',
            ],
        ]);
    }

    public function get_supabase_config() {
        // IMPORTANT: These values should be stored securely,
        // for example, in wp-config.php, not directly in the code.
        // For this example, I will define them here.
        // The user will need to replace these with their actual Supabase credentials.
        $supabase_url = get_option('fil_platform_supabase_url', 'YOUR_SUPABASE_URL');
        $supabase_anon_key = get_option('fil_platform_supabase_anon_key', 'YOUR_SUPABASE_ANON_KEY');

        if ($supabase_url === 'YOUR_SUPABASE_URL' || $supabase_anon_key === 'YOUR_SUPABASE_ANON_KEY') {
            return new WP_Error(
                'supabase_not_configured',
                'Supabase URL and Key are not configured in WordPress settings.',
                ['status' => 500]
            );
        }

        return new WP_REST_Response([
            'url' => $supabase_url,
            'anonKey' => $supabase_anon_key,
        ], 200);
    }

    /**
     * Check admin permission
     */
    public function check_admin_permission() {
        return current_user_can('manage_options');
    }

    /**
     * Check user permission (any logged-in user)
     */
    public function check_user_permission() {
        return is_user_logged_in();
    }

    /**
     * Test endpoint
     */
    public function test_endpoint($request) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'API is working!',
            'timestamp' => current_time('mysql')
        ], 200);
    }

    /**
     * Manual trigger for filfox scraper (via REST API)
     */
    public function manual_scrape_filfox($request) {
        // Get the scraper instance and run it
        global $fil_platform_scraper;

        if (!$fil_platform_scraper) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Scraper not initialized'
            ], 500);
        }

        try {
            // Call the scraper's test method
            $result = $this->call_scraper_method();

            if ($result['success']) {
                return new WP_REST_Response([
                    'success' => true,
                    'message' => 'Filfox data scraped successfully',
                    'data' => $result['data']
                ], 200);
            } else {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Failed to scrape filfox data',
                    'error' => $result['error']
                ], 500);
            }

        } catch (Exception $e) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scraper status
     */
    public function get_scraper_status($request) {
        $next_scheduled = wp_next_scheduled('fil_platform_filfox_scraper');
        $last_run = get_option('fil_platform_last_scrape_run', 'Never');
        $last_success = get_option('fil_platform_last_scrape_success', 'Never');

        return new WP_REST_Response([
            'next_scheduled' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled) . ' UTC' : 'Not scheduled',
            'next_scheduled_jst' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled + 9 * 3600) . ' JST' : 'Not scheduled',
            'last_run' => $last_run,
            'last_success' => $last_success,
            'cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON
        ], 200);
    }

    /**
     * Get real-time filfox data
     */
    public function get_filfox_realtime_data($request) {
        error_log('FIL Platform: Real-time API endpoint called');

        try {
            // Scrape data from filfox.info in real-time
            $result = $this->scrape_filfox_realtime();

            error_log('FIL Platform: Scrape result: ' . json_encode($result));

            if ($result['success']) {
                return new WP_REST_Response([
                    'success' => true,
                    'data' => $result['data'],
                    'timestamp' => current_time('mysql')
                ], 200);
            } else {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Failed to fetch real-time data',
                    'error' => $result['error']
                ], 500);
            }

        } catch (Exception $e) {
            error_log('FIL Platform: Exception in real-time API: ' . $e->getMessage());
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to call scraper functionality
     */
    private function call_scraper_method() {
        // Directly implement scraping logic here for API access
        update_option('fil_platform_last_scrape_run', current_time('mysql'));

        try {
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract data
            $data = $this->parse_filfox_html($body);

            if (!$data['mining_reward']) {
                throw new Exception('No valid data found in the response');
            }

            update_option('fil_platform_last_scrape_success', current_time('mysql'));

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content (simplified version for API)
     */
    private function parse_filfox_html($html) {
        $mining_reward = null;

        // Remove script and style tags
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Try to find mining reward
        if (preg_match('/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) {
                $mining_reward = $reward;
            }
        }

        // Alternative pattern for mining reward
        if (!$mining_reward && preg_match('/([0-9]*\.?[0-9]+)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) {
                $mining_reward = $reward;
            }
        }

        return [
            'mining_reward' => $mining_reward ?: 0.0,
            'scraped_at' => current_time('mysql')
        ];
    }

    /**
     * Scrape filfox data in real-time with all network statistics
     */
    private function scrape_filfox_realtime() {
        try {
            // Use WordPress HTTP API to fetch the page
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract all network data
            $data = $this->parse_filfox_realtime_html($body);

            // Validate that we have at least some essential data
            if (!$data['mining_reward'] && !$data['block_height'] && !$data['network_storage_power']) {
                throw new Exception('No valid data found in the response');
            }

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content to extract all network statistics for real-time display
     */
    private function parse_filfox_realtime_html($html) {
        // Initialize all data fields
        $data = [
            'block_height' => null,
            'latest_block' => null,
            'network_storage_power' => null,
            'active_miners' => null,
            'block_reward' => null,
            'mining_reward' => null,
            'fil_production_24h' => null,
            'sector_initial_pledge' => null,
            'total_pledge_collateral' => null,
            'messages_24h' => null,
            'scraped_at' => current_time('mysql')
        ];

        // Remove script and style tags to avoid false matches
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Extract Block Height
        if (preg_match('/Block\s+Height[^0-9]*([0-9,]+)/i', $html, $matches)) {
            $data['block_height'] = (int) str_replace(',', '', $matches[1]);
        }

        // Extract Latest Block (time ago)
        if (preg_match('/Latest\s+Block[^0-9]*([0-9]+)\s*(min|sec|hour|day)s?\s*([0-9]*)\s*(min|sec)?\s*ago/i', $html, $matches)) {
            $data['latest_block'] = trim($matches[0]);
        }

        // Extract Network Storage Power (EiB)
        if (preg_match('/Network\s+Storage\s+Power[^0-9]*([0-9]+\.?[0-9]*)\s*EiB/i', $html, $matches)) {
            $data['network_storage_power'] = (float) $matches[1];
        }

        // Extract Active Miners
        if (preg_match('/Active\s+Miners[^0-9]*([0-9,]+)/i', $html, $matches)) {
            $data['active_miners'] = (int) str_replace(',', '', $matches[1]);
        }

        // Extract Block Reward
        if (preg_match('/Block\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL/i', $html, $matches)) {
            $data['block_reward'] = (float) $matches[1];
        }

        // Extract 24h Average Mining Reward
        if (preg_match('/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/i', $html, $matches)) {
            $data['mining_reward'] = (float) $matches[1];
        }

        // Extract 24h FIL Production
        if (preg_match('/24h\s+FIL\s+Production[^0-9]*([0-9,]+)\s*FIL/i', $html, $matches)) {
            $data['fil_production_24h'] = (int) str_replace(',', '', $matches[1]);
        }

        // Extract Current Sector Initial Pledge
        if (preg_match('/Current\s+Sector\s+Initial\s+Pledge[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/32GiB/i', $html, $matches)) {
            $data['sector_initial_pledge'] = (float) $matches[1];
        }

        // Extract Total Pledge Collateral
        if (preg_match('/Total\s+Pledge\s+Collateral[^0-9]*([0-9,]+)\s*FIL/i', $html, $matches)) {
            $data['total_pledge_collateral'] = (int) str_replace(',', '', $matches[1]);
        }

        // Extract 24h Messages
        if (preg_match('/24h\s+Messages[^0-9]*([0-9,]+)/i', $html, $matches)) {
            $data['messages_24h'] = (int) str_replace(',', '', $matches[1]);
        }

        // Fallback patterns for mining reward if not found above
        if (!$data['mining_reward']) {
            // Pattern 2: Look for decimal numbers followed by "FIL/TiB"
            if (preg_match('/([0-9]*\.?[0-9]+)\s*FIL\/TiB/i', $html, $matches)) {
                $reward = (float) $matches[1];
                if ($reward > 0 && $reward < 10) {
                    $data['mining_reward'] = $reward;
                }
            }
        }

        return $data;
    }
}

new FIL_Platform_API();

// Add a simple settings page for the admin to enter their Supabase credentials.
function fil_platform_add_settings_menu() {
    add_options_page('FIL Platform Settings', 'FIL Platform Settings', 'manage_options', 'fil-platform-settings', 'fil_platform_options_page');
}
add_action('admin_menu', 'fil_platform_add_settings_menu');

function fil_platform_register_settings() {
    register_setting('fil_platform_options', 'fil_platform_supabase_url');
    register_setting('fil_platform_options', 'fil_platform_supabase_anon_key');
    register_setting('fil_platform_options', 'fil_platform_supabase_service_key');
}
add_action('admin_init', 'fil_platform_register_settings');

function fil_platform_options_page() {
    ?>
    <div class="wrap">
        <h1>FIL Platform Settings</h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('fil_platform_options');
            do_settings_sections('fil_platform_options');
            ?>
            <table class="form-table">
                <tr valign="top">
                    <th scope="row">Supabase URL</th>
                    <td><input type="text" name="fil_platform_supabase_url" value="<?php echo esc_attr(get_option('fil_platform_supabase_url')); ?>" size="100" /></td>
                </tr>
                <tr valign="top">
                    <th scope="row">Supabase Anon Key</th>
                    <td><input type="text" name="fil_platform_supabase_anon_key" value="<?php echo esc_attr(get_option('fil_platform_supabase_anon_key')); ?>" size="100"/></td>
                </tr>
                <tr valign="top">
                    <th scope="row">Supabase Service Key</th>
                    <td>
                        <input type="password" name="fil_platform_supabase_service_key" value="<?php echo esc_attr(get_option('fil_platform_supabase_service_key')); ?>" size="100"/>
                        <p class="description">Service role key for server-side operations (required for scraper). Keep this secure!</p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}