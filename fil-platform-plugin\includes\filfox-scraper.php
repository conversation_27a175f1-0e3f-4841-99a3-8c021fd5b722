<?php
// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Filfox Data Scraper for WordPress
 * 
 * This class handles scraping data from filfox.info and storing it in Supabase
 */
class FIL_Platform_Filfox_Scraper {

    public function __construct() {
        add_action('init', [$this, 'schedule_filfox_cron']);
        add_action('fil_platform_filfox_scraper', [$this, 'run_filfox_scraper']);
        
        // Add admin hooks
        add_action('wp_ajax_fil_platform_manual_scrape', [$this, 'ajax_manual_scrape']);
        add_action('wp_ajax_fil_platform_scraper_status', [$this, 'ajax_scraper_status']);
    }

    /**
     * Schedule the filfox scraper cron job
     */
    public function schedule_filfox_cron() {
        if (!wp_next_scheduled('fil_platform_filfox_scraper')) {
            // Schedule for 2:00 AM JST daily
            // Convert JST to UTC: JST is UTC+9, so 2:00 AM JST = 5:00 PM UTC (previous day)
            $timestamp = strtotime('today 17:00:00'); // 5:00 PM UTC = 2:00 AM JST next day
            if ($timestamp < time()) {
                $timestamp = strtotime('tomorrow 17:00:00');
            }
            
            wp_schedule_event($timestamp, 'daily', 'fil_platform_filfox_scraper');
            
            // Log the scheduling
            error_log('FIL Platform: Filfox scraper scheduled for ' . date('Y-m-d H:i:s', $timestamp) . ' UTC (2:00 AM JST)');
        }
    }

    /**
     * Run the filfox scraper
     */
    public function run_filfox_scraper() {
        error_log('FIL Platform: Starting filfox scraper...');
        
        try {
            $result = $this->scrape_filfox_data();
            
            if ($result['success']) {
                error_log('FIL Platform: Filfox scraper completed successfully');
                $this->store_network_stats($result['data']);
            } else {
                error_log('FIL Platform: Filfox scraper failed: ' . $result['error']);
            }
            
        } catch (Exception $e) {
            error_log('FIL Platform: Filfox scraper exception: ' . $e->getMessage());
        }
    }

    /**
     * AJAX handler for manual scrape
     */
    public function ajax_manual_scrape() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        check_ajax_referer('fil_platform_scraper_nonce', 'nonce');

        try {
            $result = $this->scrape_filfox_data();
            
            if ($result['success']) {
                $this->store_network_stats($result['data']);
                wp_send_json_success([
                    'message' => 'Filfox data scraped and stored successfully',
                    'data' => $result['data']
                ]);
            } else {
                wp_send_json_error([
                    'message' => 'Failed to scrape filfox data',
                    'error' => $result['error']
                ]);
            }
            
        } catch (Exception $e) {
            wp_send_json_error([
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for scraper status
     */
    public function ajax_scraper_status() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        check_ajax_referer('fil_platform_scraper_nonce', 'nonce');

        $next_scheduled = wp_next_scheduled('fil_platform_filfox_scraper');
        $last_run = get_option('fil_platform_last_scrape_run', 'Never');
        $last_success = get_option('fil_platform_last_scrape_success', 'Never');
        
        wp_send_json_success([
            'next_scheduled' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled) . ' UTC' : 'Not scheduled',
            'next_scheduled_jst' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled + 9 * 3600) . ' JST' : 'Not scheduled',
            'last_run' => $last_run,
            'last_success' => $last_success,
            'cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON
        ]);
    }

    /**
     * Scrape data from filfox.info using WordPress HTTP API
     */
    private function scrape_filfox_data() {
        update_option('fil_platform_last_scrape_run', current_time('mysql'));
        
        try {
            // Use WordPress HTTP API to fetch the page
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract data
            $data = $this->parse_filfox_html($body);

            if (!$data['mining_reward']) {
                throw new Exception('No valid data found in the response');
            }

            update_option('fil_platform_last_scrape_success', current_time('mysql'));
            
            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content to extract and mining reward
     */
    private function parse_filfox_html($html) {
        $mining_reward = null;

        // Remove script and style tags to avoid false matches
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Debug: Log a portion of the HTML to understand the structure
        error_log('FIL Platform: HTML sample: ' . substr($html, 0, 2000));

        // Try to find mining reward with more specific patterns
        // Pattern 1: "24h Average Mining Reward" followed by number and "FIL/TiB"
        if (preg_match('/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) { // Reasonable range for FIL/TiB
                $mining_reward = $reward;
                error_log('FIL Platform: Found mining reward (pattern 1): ' . $mining_reward);
            }
        }

        // Pattern 2: Look for decimal numbers followed by "FIL/TiB"
        if (!$mining_reward && preg_match('/([0-9]*\.?[0-9]+)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) {
                $mining_reward = $reward;
                error_log('FIL Platform: Found mining reward (pattern 2): ' . $mining_reward);
            }
        }

        // Pattern 3: More general FIL pattern but with stricter validation
        if (!$mining_reward && preg_match('/([0-9]*\.?[0-9]+)\s*FIL(?!\/)/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 1) { // Very small numbers are more likely to be per TiB rewards
                $mining_reward = $reward;
                error_log('FIL Platform: Found mining reward (pattern 3): ' . $mining_reward);
            }
        }

        // If still no data found, try to extract from JSON-LD or other structured data
        if ((!$mining_reward) && preg_match('/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is', $html, $matches)) {
            $json_data = json_decode($matches[1], true);
            if ($json_data) {
                // Try to extract from structured data
                $this->extract_from_structured_data($json_data, $mining_reward);
            }
        }

        // Final debug output
        error_log("FIL Platform: Final results -  Mining Reward: " . ($mining_reward ?: 'NOT FOUND'));

        return [
            'mining_reward' => $mining_reward ?: 0.0,
            'scraped_at' => current_time('mysql')
        ];
    }

    /**
     * Extract data from structured JSON data
     */
    private function extract_from_structured_data($data, &$mining_reward) {
        // Recursively search through structured data
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (is_string($key)) {
                    if (stripos($key, 'reward') !== false && is_numeric($value)) {
                        $reward = (float) $value;
                        if ($reward > 0 && $reward < 1000) {
                            $mining_reward = $reward;
                        }
                    }
                }
                if (is_array($value)) {
                    $this->extract_from_structured_data($value, $mining_reward);
                }
            }
        }
    }

    /**
     * Store network stats in Supabase
     */
    private function store_network_stats($data) {
        $supabase_url = get_option('fil_platform_supabase_url');
        $supabase_service_key = get_option('fil_platform_supabase_service_key');

        if (!$supabase_service_key) {
            // Fallback to anon key if service key not available
            $supabase_service_key = get_option('fil_platform_supabase_anon_key');
        }

        if (!$supabase_url || !$supabase_service_key) {
            throw new Exception('Supabase configuration not found');
        }

        $today = current_time('Y-m-d');

        $payload = [
            'stat_date' => $today,
            'fil_per_tib' => $data['mining_reward']
        ];

        $response = wp_remote_post($supabase_url . '/rest/v1/network_stats', [
            'headers' => [
                'apikey' => $supabase_service_key,
                'Authorization' => 'Bearer ' . $supabase_service_key,
                'Content-Type' => 'application/json',
                'Prefer' => 'resolution=merge-duplicates'
            ],
            'body' => json_encode($payload),
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            throw new Exception('Failed to store data: ' . $response->get_error_message());
        }

        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code < 200 || $status_code >= 300) {
            $body = wp_remote_retrieve_body($response);
            throw new Exception('Supabase API error (HTTP ' . $status_code . '): ' . $body);
        }

        error_log('FIL Platform: Network stats stored successfully for ' . $today);
    }
}

// Initialize the scraper
new FIL_Platform_Filfox_Scraper();
