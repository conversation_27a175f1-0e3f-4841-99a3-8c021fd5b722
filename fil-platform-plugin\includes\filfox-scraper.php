<?php
// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Filfox Data Scraper for WordPress
 * 
 * This class handles scraping data from filfox.info and storing it in Supabase
 */
class FIL_Platform_Filfox_Scraper {

    public function __construct() {
        add_action('init', [$this, 'schedule_filfox_cron']);
        add_action('fil_platform_filfox_scraper', [$this, 'run_filfox_scraper']);
        
        // Add admin hooks
        add_action('wp_ajax_fil_platform_manual_scrape', [$this, 'ajax_manual_scrape']);
        add_action('wp_ajax_fil_platform_scraper_status', [$this, 'ajax_scraper_status']);
    }

    /**
     * Schedule the filfox scraper cron job
     */
    public function schedule_filfox_cron() {
        if (!wp_next_scheduled('fil_platform_filfox_scraper')) {
            // Schedule for 2:00 AM JST daily
            // Convert JST to UTC: JST is UTC+9, so 2:00 AM JST = 5:00 PM UTC (previous day)
            $timestamp = strtotime('today 17:00:00'); // 5:00 PM UTC = 2:00 AM JST next day
            if ($timestamp < time()) {
                $timestamp = strtotime('tomorrow 17:00:00');
            }
            
            wp_schedule_event($timestamp, 'daily', 'fil_platform_filfox_scraper');
            
            // Log the scheduling
            error_log('FIL Platform: Filfox scraper scheduled for ' . date('Y-m-d H:i:s', $timestamp) . ' UTC (2:00 AM JST)');
        }
    }

    /**
     * Run the filfox scraper
     */
    public function run_filfox_scraper() {
        error_log('FIL Platform: Starting filfox scraper...');
        
        try {
            $result = $this->scrape_filfox_data();
            
            if ($result['success']) {
                error_log('FIL Platform: Filfox scraper completed successfully');
                $this->store_network_stats($result['data']);
            } else {
                error_log('FIL Platform: Filfox scraper failed: ' . $result['error']);
            }
            
        } catch (Exception $e) {
            error_log('FIL Platform: Filfox scraper exception: ' . $e->getMessage());
        }
    }

    /**
     * AJAX handler for manual scrape
     */
    public function ajax_manual_scrape() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        check_ajax_referer('fil_platform_scraper_nonce', 'nonce');

        try {
            $result = $this->scrape_filfox_data();
            
            if ($result['success']) {
                $this->store_network_stats($result['data']);
                wp_send_json_success([
                    'message' => 'Filfox data scraped and stored successfully',
                    'data' => $result['data']
                ]);
            } else {
                wp_send_json_error([
                    'message' => 'Failed to scrape filfox data',
                    'error' => $result['error']
                ]);
            }
            
        } catch (Exception $e) {
            wp_send_json_error([
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for scraper status
     */
    public function ajax_scraper_status() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        check_ajax_referer('fil_platform_scraper_nonce', 'nonce');

        $next_scheduled = wp_next_scheduled('fil_platform_filfox_scraper');
        $last_run = get_option('fil_platform_last_scrape_run', 'Never');
        $last_success = get_option('fil_platform_last_scrape_success', 'Never');
        
        wp_send_json_success([
            'next_scheduled' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled) . ' UTC' : 'Not scheduled',
            'next_scheduled_jst' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled + 9 * 3600) . ' JST' : 'Not scheduled',
            'last_run' => $last_run,
            'last_success' => $last_success,
            'cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON
        ]);
    }

    /**
     * Scrape data from filfox.info using WordPress HTTP API
     */
    private function scrape_filfox_data() {
        update_option('fil_platform_last_scrape_run', current_time('mysql'));
        
        try {
            // Use WordPress HTTP API to fetch the page
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract data
            $data = $this->parse_filfox_html($body);

            // Validate that we have at least some essential data
            if (!$data['mining_reward'] && !$data['block_height'] && !$data['network_storage_power']) {
                throw new Exception('No valid data found in the response');
            }

            update_option('fil_platform_last_scrape_success', current_time('mysql'));
            
            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content to extract network statistics
     */
    private function parse_filfox_html($html) {
        // Initialize all data fields
        $data = [
            'block_height' => null,
            'latest_block' => null,
            'network_storage_power' => null,
            'active_miners' => null,
            'block_reward' => null,
            'mining_reward' => null,
            'fil_production_24h' => null,
            'sector_initial_pledge' => null,
            'total_pledge_collateral' => null,
            'messages_24h' => null,
            'scraped_at' => current_time('mysql')
        ];

        // Remove script and style tags to avoid false matches
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Debug: Log a portion of the HTML to understand the structure
        error_log('FIL Platform: HTML sample: ' . substr($html, 0, 2000));

        // Extract Block Height
        if (preg_match('/Block\s+Height[^0-9]*([0-9,]+)/i', $html, $matches)) {
            $data['block_height'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found block height: ' . $data['block_height']);
        }

        // Extract Latest Block (time ago)
        if (preg_match('/Latest\s+Block[^0-9]*([0-9]+)\s*(min|sec|hour|day)s?\s*([0-9]*)\s*(min|sec)?\s*ago/i', $html, $matches)) {
            $data['latest_block'] = trim($matches[0]);
            error_log('FIL Platform: Found latest block: ' . $data['latest_block']);
        }

        // Extract Network Storage Power (EiB)
        if (preg_match('/Network\s+Storage\s+Power[^0-9]*([0-9]+\.?[0-9]*)\s*EiB/i', $html, $matches)) {
            $data['network_storage_power'] = (float) $matches[1];
            error_log('FIL Platform: Found network storage power: ' . $data['network_storage_power'] . ' EiB');
        }

        // Extract Active Miners
        if (preg_match('/Active\s+Miners[^0-9]*([0-9,]+)/i', $html, $matches)) {
            $data['active_miners'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found active miners: ' . $data['active_miners']);
        }

        // Extract Block Reward
        if (preg_match('/Block\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL/i', $html, $matches)) {
            $data['block_reward'] = (float) $matches[1];
            error_log('FIL Platform: Found block reward: ' . $data['block_reward'] . ' FIL');
        }

        // Extract 24h Average Mining Reward
        if (preg_match('/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/i', $html, $matches)) {
            $data['mining_reward'] = (float) $matches[1];
            error_log('FIL Platform: Found mining reward: ' . $data['mining_reward'] . ' FIL/TiB');
        }

        // Extract 24h FIL Production
        if (preg_match('/24h\s+FIL\s+Production[^0-9]*([0-9,]+)\s*FIL/i', $html, $matches)) {
            $data['fil_production_24h'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found 24h FIL production: ' . $data['fil_production_24h'] . ' FIL');
        }

        // Extract Current Sector Initial Pledge
        if (preg_match('/Current\s+Sector\s+Initial\s+Pledge[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/32GiB/i', $html, $matches)) {
            $data['sector_initial_pledge'] = (float) $matches[1];
            error_log('FIL Platform: Found sector initial pledge: ' . $data['sector_initial_pledge'] . ' FIL/32GiB');
        }

        // Extract Total Pledge Collateral
        if (preg_match('/Total\s+Pledge\s+Collateral[^0-9]*([0-9,]+)\s*FIL/i', $html, $matches)) {
            $data['total_pledge_collateral'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found total pledge collateral: ' . $data['total_pledge_collateral'] . ' FIL');
        }

        // Extract 24h Messages
        if (preg_match('/24h\s+Messages[^0-9]*([0-9,]+)/i', $html, $matches)) {
            $data['messages_24h'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found 24h messages: ' . $data['messages_24h']);
        }

        // Fallback patterns for mining reward if not found above
        if (!$data['mining_reward']) {
            // Pattern 2: Look for decimal numbers followed by "FIL/TiB"
            if (preg_match('/([0-9]*\.?[0-9]+)\s*FIL\/TiB/i', $html, $matches)) {
                $reward = (float) $matches[1];
                if ($reward > 0 && $reward < 10) {
                    $data['mining_reward'] = $reward;
                    error_log('FIL Platform: Found mining reward (fallback pattern): ' . $data['mining_reward']);
                }
            }
        }

        // Final debug output
        error_log("FIL Platform: Extracted data - " . json_encode($data));

        return $data;
    }

    /**
     * Extract data from structured JSON data
     */
    private function extract_from_structured_data($data, &$mining_reward) {
        // Recursively search through structured data
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (is_string($key)) {
                    if (stripos($key, 'reward') !== false && is_numeric($value)) {
                        $reward = (float) $value;
                        if ($reward > 0 && $reward < 1000) {
                            $mining_reward = $reward;
                        }
                    }
                }
                if (is_array($value)) {
                    $this->extract_from_structured_data($value, $mining_reward);
                }
            }
        }
    }

    /**
     * Store network stats in Supabase
     */
    private function store_network_stats($data) {
        $supabase_url = get_option('fil_platform_supabase_url');
        $supabase_service_key = get_option('fil_platform_supabase_service_key');

        if (!$supabase_service_key) {
            // Fallback to anon key if service key not available
            $supabase_service_key = get_option('fil_platform_supabase_anon_key');
        }

        if (!$supabase_url || !$supabase_service_key) {
            throw new Exception('Supabase configuration not found');
        }

        $today = current_time('Y-m-d');

        // Prepare payload with all scraped data
        $payload = [
            'stat_date' => $today,
            'fil_per_tib' => $data['mining_reward'],
            'block_height' => $data['block_height'],
            'latest_block' => $data['latest_block'],
            'network_storage_power' => $data['network_storage_power'],
            'active_miners' => $data['active_miners'],
            'block_reward' => $data['block_reward'],
            'fil_production_24h' => $data['fil_production_24h'],
            'sector_initial_pledge' => $data['sector_initial_pledge'],
            'total_pledge_collateral' => $data['total_pledge_collateral'],
            'messages_24h' => $data['messages_24h'],
            'scraped_at' => $data['scraped_at']
        ];

        // Remove null values to avoid database issues
        $payload = array_filter($payload, function($value) {
            return $value !== null;
        });

        $response = wp_remote_post($supabase_url . '/rest/v1/network_stats', [
            'headers' => [
                'apikey' => $supabase_service_key,
                'Authorization' => 'Bearer ' . $supabase_service_key,
                'Content-Type' => 'application/json',
                'Prefer' => 'resolution=merge-duplicates'
            ],
            'body' => json_encode($payload),
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            throw new Exception('Failed to store data: ' . $response->get_error_message());
        }

        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code < 200 || $status_code >= 300) {
            $body = wp_remote_retrieve_body($response);
            throw new Exception('Supabase API error (HTTP ' . $status_code . '): ' . $body);
        }

        error_log('FIL Platform: Network stats stored successfully for ' . $today . ' with data: ' . json_encode($payload));
    }
}

// Initialize the scraper
new FIL_Platform_Filfox_Scraper();
